import { supabaseAdmin } from '../../../lib/supabase';


// Helper function to serialize category data consistently

// Helper function to serialize category data

const serializeCategory = (category) => {
  if (!category) return null;
  return {
    id: String(category.id || ''),
    name: String(category.name || ''),

    description: category.description ? String(category.description) : null, // Ensure null if not present
    parent_id: category.parent_id ? String(category.parent_id) : null,
    created_at: category.created_at ? String(new Date(category.created_at).toISOString()) : null,
    updated_at: category.updated_at ? String(new Date(category.updated_at).toISOString()) : null,
  };
};


export default async function handler(req, res) {
  // Note: Supabase RLS policies should be in place to ensure only authorized users (e.g., admins)
  // can perform POST, PUT, DELETE operations. The policies were added in the
  // 0001_create_service_categories.sql migration.

  if (req.method === 'GET') {
    const { id } = req.query; // Check for ID in query parameters

    if (id) {
      // Fetch a single category by ID
      try {
        console.log(`🔍 Service Categories API - GET: Fetching category with ID: ${id}`);
        const { data: category, error } = await supabaseAdmin
          .from('service_categories')
          .select('id, name, description, parent_id, created_at, updated_at')
          .eq('id', id)
          .single();

        if (error) {
          console.error(`❌ Service Categories API - GET (single): Database error for ID ${id}:`, error);
          // Check if error is due to not found (e.g. PostgREST error code PGRST116 for `single()` finding no rows)
          // However, .single() returns null for data and no error if no row is found but 0 rows matched.
          // It throws an error if multiple rows are found.
          // If error.code is 'PGRST204' (No Content) or similar for "not found" from .select().maybeSingle(), handle as 404.
          // For .single(), if data is null and no error, it means not found.
          if (error.code === 'PGRST204' || (!category && !error) ) { // PGRST204 might not apply here, check for null data
             return res.status(404).json({ success: false, error: 'Category not found', details: `No category found with ID ${id}` });
          }
          throw error; // For other database errors
        }

        if (!category) {
          // This case handles when .single() successfully returns 0 rows (no error object from Supabase)
          console.log(`⚠️ Service Categories API - GET (single): No category found with ID ${id}`);
          return res.status(404).json({ success: false, error: 'Category not found', details: `No category found with ID ${id}` });
        }

        console.log(`✅ Service Categories API - GET (single): Found category ${category.name}`);
        res.status(200).json({ success: true, category: serializeCategory(category) });

      } catch (error) {
        console.error(`💥 Service Categories API - GET (single): Error for ID ${id}:`, error);
        // Ensure a 500 for unexpected errors, 404 should be handled above.
        if (res.headersSent) return; // Avoid sending header twice if 404 was already sent
        res.status(500).json({ success: false, error: 'Failed to fetch service category', details: error.message });
      }
    } else {
      // Fetch all categories (existing behavior)
      try {
        console.log('🔍 Service Categories API - GET: Fetching all categories');
        const { data: categories, error } = await supabaseAdmin
          .from('service_categories')
          .select('id, name, description, parent_id, created_at, updated_at')
          .order('name');

        if (error) {
          console.error('❌ Service Categories API - GET (all): Database error:', error);
          throw error;
        }

        console.log(`✅ Service Categories API - GET (all): Found ${categories?.length || 0} categories`);
        const serializedCategories = categories?.map(serializeCategory) || [];
        res.status(200).json({ success: true, categories: serializedCategories });

      } catch (error) {
        console.error('💥 Service Categories API - GET (all): Error:', error);
        res.status(500).json({ success: false, error: 'Failed to fetch service categories', details: error.message });
      }
    }
  } else if (req.method === 'POST') {
    // Handler for creating a new service category
    try {
      console.log('🔄 Service Categories API - POST: Creating category');
      const { name, description, parent_id } = req.body;

      if (!name) {
        return res.status(400).json({ success: false, error: 'Name is required' });
      }

      const { data: newCategory, error } = await supabaseAdmin
        .from('service_categories')
        .insert([{ name, description, parent_id: parent_id || null }]) // Ensure parent_id is null if not provided
        .select('id, name, description, parent_id, created_at, updated_at')
        .single();

      if (error) {
        console.error('❌ Service Categories API - POST: Database error:', error);
        // Check for unique constraint violation (name already exists)
        if (error.code === '23505') { // PostgreSQL unique violation
          return res.status(409).json({ success: false, error: 'A category with this name already exists.', details: error.message });
        }
        throw error;
      }

      console.log('✅ Service Categories API - POST: Category created:', newCategory?.id);
      res.status(201).json({ success: true, category: serializeCategory(newCategory) });

    } catch (error) {
      console.error('💥 Service Categories API - POST: Error:', error);
      res.status(500).json({ success: false, error: 'Failed to create service category', details: error.message });
    }

  } else if (req.method === 'PUT') {
    // Handler for updating an existing service category
    try {
      console.log('🔄 Service Categories API - PUT: Updating category');
      const { id } = req.query; // Category ID from URL query parameter
      const { name, description, parent_id } = req.body;

      if (!id) {
        return res.status(400).json({ success: false, error: 'Category ID is required in URL query' });
      }
      if (!name && description === undefined && parent_id === undefined) {
        return res.status(400).json({ success: false, error: 'At least one field (name, description, parent_id) must be provided for update' });
      }

      // Construct update object with only provided fields
      const updateData = {};
      if (name) updateData.name = name;
      if (description !== undefined) updateData.description = description;
      if (parent_id !== undefined) updateData.parent_id = parent_id; // Allows setting parent_id to null

      const { data: updatedCategory, error } = await supabaseAdmin
        .from('service_categories')
        .update(updateData)
        .eq('id', id)
        .select('id, name, description, parent_id, created_at, updated_at')
        .single();

      if (error) {
        console.error('❌ Service Categories API - PUT: Database error:', error);
        if (error.code === 'PGRST204') { // PostgREST specific: No rows found
          return res.status(404).json({ success: false, error: 'Category not found', details: error.message });
        }
        if (error.code === '23505') { // PostgreSQL unique violation (name already exists)
             return res.status(409).json({ success: false, error: 'A category with this name already exists.', details: error.message });
        }
        throw error;
      }

      if (!updatedCategory) {
        return res.status(404).json({ success: false, error: 'Category not found or no changes made' });
      }

      console.log('✅ Service Categories API - PUT: Category updated:', updatedCategory.id);
      res.status(200).json({ success: true, category: serializeCategory(updatedCategory) });

    } catch (error) {
      console.error('💥 Service Categories API - PUT: Error:', error);
      res.status(500).json({ success: false, error: 'Failed to update service category', details: error.message });
    }

  } else if (req.method === 'DELETE') {
    // Handler for deleting a service category
    try {
      console.log('🔄 Service Categories API - DELETE: Deleting category');
      const { id } = req.query; // Category ID from URL query parameter

      if (!id) {
        return res.status(400).json({ success: false, error: 'Category ID is required in URL query' });
      }

      // Check if any service is using this category_id
      // Assuming the services table has a column named 'category_id' that references service_categories.id
      // If the column name is different (e.g. 'category'), this query needs to be adjusted.
      // The previous subtask about finding the service_categories table definition did not identify
      // the 'services' table schema in detail, so 'category_id' is an assumption.
      // If 'services' table uses 'category' (TEXT) as seen in a migration, then this check is more complex.
      // For now, we assume a direct foreign key 'category_id'.
      const { data: servicesUsingCategory, error: serviceCheckError } = await supabaseAdmin
        .from('services') // Assuming 'services' table
        .select('id')
        .eq('category_id', id); // Assuming 'category_id' foreign key in 'services' table

      if (serviceCheckError) {
        console.error('❌ Service Categories API - DELETE: Error checking services:', serviceCheckError);
        throw serviceCheckError;
      }

      if (servicesUsingCategory && servicesUsingCategory.length > 0) {
        console.warn('⚠️ Service Categories API - DELETE: Category in use by services:', id);
        return res.status(400).json({
          success: false,
          error: 'Category is in use and cannot be deleted.',
          details: `Category is referenced by ${servicesUsingCategory.length} service(s).`,
        });
      }

      // Proceed with deletion
      const { data, error: deleteError } = await supabaseAdmin
        .from('service_categories')
        .delete()
        .eq('id', id)
        .select('id') // To check if a row was actually deleted
        .single();

      if (deleteError) {
        console.error('❌ Service Categories API - DELETE: Database error:', deleteError);
        if (deleteError.code === 'PGRST204') { // PostgREST specific: No rows found
            return res.status(404).json({ success: false, error: 'Category not found', details: deleteError.message });
        }
        throw deleteError;
      }

      if (!data) { // If data is null and no error, it means no row matched the ID.
        return res.status(404).json({ success: false, error: 'Category not found' });
      }

      console.log('✅ Service Categories API - DELETE: Category deleted:', id);
      res.status(200).json({ success: true, message: 'Service category deleted successfully.' });

    } catch (error) {
      console.error('💥 Service Categories API - DELETE: Error:', error);
      res.status(500).json({ success: false, error: 'Failed to delete service category', details: error.message });
    }

  } else {
    res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
    res.status(405).json({ success: false, error: `Method ${req.method} not allowed` });
  }
}
