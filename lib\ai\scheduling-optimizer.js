/**
 * AI Scheduling Optimizer for Ocean Soul Sparkles
 * Implements intelligent schedule optimization with travel time calculations and conflict resolution
 */

import { TravelTimeCalculator } from './travel-time-calculator'
import { findBestArtist, ASSIGNMENT_STRATEGIES } from '@/lib/artist-assignment'
import { supabase } from '@/lib/supabase'

export class AISchedulingOptimizer {
  constructor() {
    this.travelCalculator = new TravelTimeCalculator()
    this.optimizationCache = new Map()
    this.cacheExpiry = 30 * 60 * 1000 // 30 minutes
  }

  /**
   * Initialize the optimizer
   */
  async initialize() {
    await this.travelCalculator.initialize()
    console.log('[AISchedulingOptimizer] Initialized successfully')
  }

  /**
   * Optimize an artist's schedule for a given date
   * @param {string} artistId - Artist ID
   * @param {Date} date - Date to optimize
   * @param {Object} newBooking - Optional new booking to include
   * @returns {Promise<Object>} Optimization results
   */
  async optimizeSchedule(artistId, date, newBooking = null) {
    const cacheKey = `${artistId}-${date.toISOString().split('T')[0]}-${newBooking?.id || 'none'}`
    
    if (this.optimizationCache.has(cacheKey)) {
      const cached = this.optimizationCache.get(cacheKey)
      if (Date.now() - cached.timestamp < this.cacheExpiry) {
        console.log('[AISchedulingOptimizer] Cache hit for optimization')
        return cached.result
      }
    }

    try {
      console.log(`[AISchedulingOptimizer] Optimizing schedule for artist ${artistId} on ${date.toISOString().split('T')[0]}`)
      
      const schedule = await this.getArtistSchedule(artistId, date)
      const optimizedSchedule = await this.calculateOptimalSchedule(schedule, newBooking)
      
      const result = {
        success: true,
        originalSchedule: schedule,
        optimizedSchedule,
        improvements: this.calculateImprovements(schedule, optimizedSchedule),
        recommendations: this.generateRecommendations(optimizedSchedule),
        optimizedAt: new Date().toISOString(),
        artistId,
        date: date.toISOString().split('T')[0]
      }

      // Cache the result
      this.optimizationCache.set(cacheKey, {
        result,
        timestamp: Date.now()
      })

      console.log(`[AISchedulingOptimizer] Optimization completed with ${result.improvements.efficiencyImprovement}% improvement`)
      return result
    } catch (error) {
      console.error('[AISchedulingOptimizer] Optimization failed:', error)
      return {
        success: false,
        error: error.message,
        artistId,
        date: date.toISOString().split('T')[0]
      }
    }
  }

  /**
   * Get artist's schedule for a specific date
   * @param {string} artistId - Artist ID
   * @param {Date} date - Date to get schedule for
   * @returns {Promise<Array>} Array of bookings
   */
  async getArtistSchedule(artistId, date) {
    const startOfDay = new Date(date)
    startOfDay.setHours(0, 0, 0, 0)
    
    const endOfDay = new Date(date)
    endOfDay.setHours(23, 59, 59, 999)

    const { data: bookings, error } = await supabase
      .from('bookings')
      .select(`
        id,
        start_time,
        end_time,
        status,
        customer_location,
        customers(name, address, phone),
        services(name, duration, category)
      `)
      .eq('assigned_artist_id', artistId)
      .gte('start_time', startOfDay.toISOString())
      .lte('start_time', endOfDay.toISOString())
      .neq('status', 'canceled')
      .order('start_time', { ascending: true })

    if (error) {
      throw new Error(`Failed to fetch schedule: ${error.message}`)
    }

    return (bookings || []).map(booking => ({
      ...booking,
      location: booking.customer_location || booking.customers?.address || 'Melbourne, VIC',
      duration: booking.services?.duration || 60,
      customerName: booking.customers?.name || 'Unknown Customer',
      serviceName: booking.services?.name || 'Unknown Service'
    }))
  }

  /**
   * Calculate optimal schedule using AI optimization
   * @param {Array} schedule - Current schedule
   * @param {Object} newBooking - Optional new booking
   * @returns {Promise<Array>} Optimized schedule
   */
  async calculateOptimalSchedule(schedule, newBooking = null) {
    if (schedule.length === 0) {
      return newBooking ? [newBooking] : []
    }

    // Include new booking if provided
    const allBookings = newBooking ? [...schedule, newBooking] : schedule

    if (allBookings.length <= 1) {
      return allBookings
    }

    // Calculate travel times between all locations
    const travelMatrix = await this.calculateTravelMatrix(allBookings)
    
    // Use genetic algorithm for optimal ordering
    const optimizedOrder = await this.geneticAlgorithmOptimization(allBookings, travelMatrix)
    
    // Rebuild schedule with optimal timing
    return this.rebuildScheduleWithOptimalTiming(optimizedOrder, travelMatrix)
  }

  /**
   * Calculate travel time matrix between all booking locations
   * @param {Array} bookings - Array of bookings
   * @returns {Promise<Object>} Travel matrix and booking data
   */
  async calculateTravelMatrix(bookings) {
    const matrix = {}
    const locations = bookings.map(b => b.location)

    console.log(`[AISchedulingOptimizer] Calculating travel matrix for ${bookings.length} locations`)

    for (let i = 0; i < bookings.length; i++) {
      for (let j = 0; j < bookings.length; j++) {
        if (i !== j) {
          const origin = locations[i]
          const destination = locations[j]
          const key = `${i}-${j}`
          
          try {
            const travelTime = await this.travelCalculator.calculateTravelTime(
              origin, 
              destination,
              new Date(bookings[i].end_time || bookings[i].start_time)
            )
            matrix[key] = travelTime
          } catch (error) {
            console.warn(`[AISchedulingOptimizer] Travel time calculation failed for ${origin} -> ${destination}:`, error)
            // Fallback to default travel time
            matrix[key] = {
              travelTimeMinutes: 20,
              bufferTimeMinutes: 15,
              totalTimeMinutes: 35,
              isEstimate: true,
              source: 'fallback'
            }
          }
        }
      }
    }

    return { bookings, travelMatrix: matrix }
  }

  /**
   * Genetic algorithm optimization for booking order
   * @param {Array} bookings - Array of bookings
   * @param {Object} travelMatrix - Travel time matrix
   * @returns {Promise<Array>} Optimized booking order
   */
  async geneticAlgorithmOptimization(bookings, travelMatrix) {
    const populationSize = Math.min(50, Math.factorial(Math.min(bookings.length, 8))) // Limit for performance
    const generations = 100
    const mutationRate = 0.1
    const eliteSize = Math.floor(populationSize * 0.2)

    // Initialize population with random permutations
    let population = []
    for (let i = 0; i < populationSize; i++) {
      const individual = [...Array(bookings.length).keys()]
      this.shuffleArray(individual)
      population.push(individual)
    }

    // Include the original order as one individual
    population[0] = [...Array(bookings.length).keys()]

    for (let generation = 0; generation < generations; generation++) {
      // Evaluate fitness for all individuals
      const fitness = population.map(individual => 
        this.calculateScheduleFitness(individual, travelMatrix, bookings)
      )

      // Sort by fitness (higher is better)
      const sortedIndices = fitness
        .map((fit, index) => ({ fitness: fit, index }))
        .sort((a, b) => b.fitness - a.fitness)
        .map(item => item.index)

      // Select elite individuals
      const newPopulation = []
      for (let i = 0; i < eliteSize; i++) {
        newPopulation.push([...population[sortedIndices[i]]])
      }

      // Generate offspring through crossover and mutation
      while (newPopulation.length < populationSize) {
        const parent1 = population[sortedIndices[Math.floor(Math.random() * eliteSize)]]
        const parent2 = population[sortedIndices[Math.floor(Math.random() * eliteSize)]]
        
        let offspring = this.crossover(parent1, parent2)
        
        if (Math.random() < mutationRate) {
          offspring = this.mutate(offspring)
        }
        
        newPopulation.push(offspring)
      }

      population = newPopulation
    }

    // Return the best individual
    const finalFitness = population.map(individual => 
      this.calculateScheduleFitness(individual, travelMatrix, bookings)
    )
    const bestIndex = finalFitness.indexOf(Math.max(...finalFitness))
    const bestOrder = population[bestIndex]

    console.log(`[AISchedulingOptimizer] Genetic algorithm completed. Best fitness: ${finalFitness[bestIndex]}`)
    
    return bestOrder.map(index => bookings[index])
  }

  /**
   * Calculate fitness score for a booking order
   * @param {Array} order - Booking order (indices)
   * @param {Object} travelMatrix - Travel time matrix
   * @param {Array} bookings - Original bookings array
   * @returns {number} Fitness score (higher is better)
   */
  calculateScheduleFitness(order, travelMatrix, bookings) {
    let score = 1000 // Base score
    let totalTravelTime = 0
    let totalWaitTime = 0
    let conflicts = 0

    for (let i = 0; i < order.length - 1; i++) {
      const current = order[i]
      const next = order[i + 1]
      const travelKey = `${current}-${next}`
      
      if (travelMatrix[travelKey]) {
        totalTravelTime += travelMatrix[travelKey].totalTimeMinutes
      }

      // Check for timing conflicts
      const currentBooking = bookings[current]
      const nextBooking = bookings[next]
      
      const currentEnd = new Date(currentBooking.end_time || currentBooking.start_time)
      currentEnd.setMinutes(currentEnd.getMinutes() + (currentBooking.duration || 60))
      
      const nextStart = new Date(nextBooking.start_time)
      const timeDiff = (nextStart - currentEnd) / (1000 * 60) // minutes
      
      if (timeDiff < 0) {
        conflicts++
        score -= 200 // Heavy penalty for conflicts
      } else if (timeDiff < (travelMatrix[travelKey]?.totalTimeMinutes || 30)) {
        score -= 100 // Penalty for insufficient travel time
      } else if (timeDiff > 120) {
        totalWaitTime += timeDiff - 30 // Penalty for excessive wait time
      }
    }

    // Apply penalties
    score -= totalTravelTime * 2 // Minimize travel time
    score -= totalWaitTime * 1 // Minimize wait time
    score -= conflicts * 500 // Heavy penalty for conflicts

    // Bonus for maintaining some chronological order
    let chronologicalBonus = 0
    for (let i = 0; i < order.length - 1; i++) {
      const currentTime = new Date(bookings[order[i]].start_time)
      const nextTime = new Date(bookings[order[i + 1]].start_time)
      if (currentTime <= nextTime) {
        chronologicalBonus += 10
      }
    }
    score += chronologicalBonus

    return Math.max(0, score)
  }

  /**
   * Crossover operation for genetic algorithm
   * @param {Array} parent1 - First parent
   * @param {Array} parent2 - Second parent
   * @returns {Array} Offspring
   */
  crossover(parent1, parent2) {
    const length = parent1.length
    const start = Math.floor(Math.random() * length)
    const end = Math.floor(Math.random() * (length - start)) + start
    
    const offspring = new Array(length).fill(-1)
    
    // Copy segment from parent1
    for (let i = start; i <= end; i++) {
      offspring[i] = parent1[i]
    }
    
    // Fill remaining positions with parent2 order
    let parent2Index = 0
    for (let i = 0; i < length; i++) {
      if (offspring[i] === -1) {
        while (offspring.includes(parent2[parent2Index])) {
          parent2Index++
        }
        offspring[i] = parent2[parent2Index]
        parent2Index++
      }
    }
    
    return offspring
  }

  /**
   * Mutation operation for genetic algorithm
   * @param {Array} individual - Individual to mutate
   * @returns {Array} Mutated individual
   */
  mutate(individual) {
    const mutated = [...individual]
    const i = Math.floor(Math.random() * mutated.length)
    const j = Math.floor(Math.random() * mutated.length)
    
    // Swap two random positions
    [mutated[i], mutated[j]] = [mutated[j], mutated[i]]
    return mutated
  }

  /**
   * Shuffle array in place
   * @param {Array} array - Array to shuffle
   */
  shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]]
    }
  }

  /**
   * Rebuild schedule with optimal timing
   * @param {Array} optimizedBookings - Optimized booking order
   * @param {Object} travelMatrix - Travel time matrix
   * @returns {Array} Schedule with optimal timing
   */
  rebuildScheduleWithOptimalTiming(optimizedBookings, travelMatrix) {
    if (optimizedBookings.length === 0) return []

    const schedule = []
    let currentTime = new Date(optimizedBookings[0].start_time)

    for (let i = 0; i < optimizedBookings.length; i++) {
      const booking = optimizedBookings[i]
      const originalIndex = travelMatrix.bookings.indexOf(booking)
      
      const optimizedBooking = {
        ...booking,
        originalStartTime: booking.start_time,
        optimizedStartTime: new Date(currentTime),
        optimizedEndTime: new Date(currentTime.getTime() + (booking.duration || 60) * 60000),
        optimizationApplied: true
      }
      
      schedule.push(optimizedBooking)
      
      // Update current time for next booking
      currentTime = new Date(optimizedBooking.optimizedEndTime)
      
      // Add travel time if there's a next booking
      if (i < optimizedBookings.length - 1) {
        const nextOriginalIndex = travelMatrix.bookings.indexOf(optimizedBookings[i + 1])
        const travelKey = `${originalIndex}-${nextOriginalIndex}`
        const travelTime = travelMatrix.travelMatrix[travelKey]?.totalTimeMinutes || 30
        
        currentTime.setMinutes(currentTime.getMinutes() + travelTime)
      }
    }
    
    return schedule
  }

  /**
   * Calculate improvements from optimization
   * @param {Array} original - Original schedule
   * @param {Array} optimized - Optimized schedule
   * @returns {Object} Improvement metrics
   */
  calculateImprovements(original, optimized) {
    const originalTravelTime = this.estimateTotalTravelTime(original)
    const optimizedTravelTime = this.estimateTotalTravelTime(optimized)
    
    const travelTimeSaved = Math.max(0, originalTravelTime - optimizedTravelTime)
    const efficiencyImprovement = originalTravelTime > 0 
      ? ((travelTimeSaved / originalTravelTime) * 100).toFixed(1)
      : '0.0'
    
    const originalConflicts = this.countConflicts(original)
    const optimizedConflicts = this.countConflicts(optimized)
    const conflictsResolved = Math.max(0, originalConflicts - optimizedConflicts)

    return {
      travelTimeSaved,
      efficiencyImprovement: parseFloat(efficiencyImprovement),
      conflictsResolved,
      originalTravelTime,
      optimizedTravelTime,
      originalConflicts,
      optimizedConflicts
    }
  }

  /**
   * Generate recommendations based on optimized schedule
   * @param {Array} schedule - Optimized schedule
   * @returns {Array} Array of recommendations
   */
  generateRecommendations(schedule) {
    const recommendations = []
    
    // Check for tight schedules
    for (let i = 0; i < schedule.length - 1; i++) {
      const current = schedule[i]
      const next = schedule[i + 1]
      
      const currentEnd = current.optimizedEndTime || current.end_time
      const nextStart = next.optimizedStartTime || next.start_time
      const gap = (new Date(nextStart) - new Date(currentEnd)) / (1000 * 60) // minutes
      
      if (gap < 15) {
        recommendations.push({
          type: 'warning',
          priority: 'high',
          message: `Tight schedule between ${current.customerName} and ${next.customerName}`,
          suggestion: 'Consider adding 15-minute buffer or rescheduling one booking',
          bookingIds: [current.id, next.id]
        })
      } else if (gap > 120) {
        recommendations.push({
          type: 'info',
          priority: 'low',
          message: `Long gap (${Math.round(gap)} minutes) between ${current.customerName} and ${next.customerName}`,
          suggestion: 'Consider scheduling additional bookings or adjusting timing',
          bookingIds: [current.id, next.id]
        })
      }
    }
    
    // Check for peak hour bookings
    const peakHourBookings = schedule.filter(booking => {
      const hour = new Date(booking.optimizedStartTime || booking.start_time).getHours()
      return hour >= 7 && hour <= 9 || hour >= 17 && hour <= 19
    })
    
    if (peakHourBookings.length > 0) {
      recommendations.push({
        type: 'info',
        priority: 'medium',
        message: `${peakHourBookings.length} booking(s) during peak traffic hours`,
        suggestion: 'Allow extra travel time during peak hours (7-9 AM, 5-7 PM)',
        bookingIds: peakHourBookings.map(b => b.id)
      })
    }
    
    return recommendations
  }

  /**
   * Estimate total travel time for a schedule
   * @param {Array} schedule - Schedule to analyze
   * @returns {number} Total travel time in minutes
   */
  estimateTotalTravelTime(schedule) {
    // Simplified estimation: 25 minutes average between bookings
    return Math.max(0, (schedule.length - 1) * 25)
  }

  /**
   * Count scheduling conflicts
   * @param {Array} schedule - Schedule to analyze
   * @returns {number} Number of conflicts
   */
  countConflicts(schedule) {
    let conflicts = 0
    for (let i = 0; i < schedule.length - 1; i++) {
      const current = schedule[i]
      const next = schedule[i + 1]
      
      const currentEnd = new Date(current.end_time || current.start_time)
      currentEnd.setMinutes(currentEnd.getMinutes() + (current.duration || 60))
      
      const nextStart = new Date(next.start_time)
      
      if (currentEnd > nextStart) {
        conflicts++
      }
    }
    return conflicts
  }

  /**
   * Clear optimization cache
   */
  clearCache() {
    this.optimizationCache.clear()
    console.log('[AISchedulingOptimizer] Cache cleared')
  }
}

// Export singleton instance
export const aiSchedulingOptimizer = new AISchedulingOptimizer()

// Export class for testing
export default AISchedulingOptimizer
