/* Customer Dashboard Styles - Phase 8: Advanced Customer Experience */

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  gap: 1rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4ECDC4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.dashboard {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
  box-sizing: border-box;
}

/* Welcome Header */
.welcomeHeader {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 16px;
  margin-bottom: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.welcomeContent {
  flex: 1;
  min-width: 250px;
}

.welcomeTitle {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
}

.welcomeSubtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.quickActionsContainer {
  flex-shrink: 0;
}

/* Tab Navigation */
.tabNavigation {
  margin-bottom: 2rem;
  border-bottom: 2px solid #f0f0f0;
}

.tabList {
  display: flex;
  gap: 0.5rem;
  overflow-x: auto;
  padding-bottom: 1rem;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.tabList::-webkit-scrollbar {
  display: none;
}

.tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  background: transparent;
  color: #666;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  position: relative;
}

.tab:hover {
  background: #f8f9fa;
  color: #4ECDC4;
}

.tab.active {
  background: #4ECDC4;
  color: white;
}

.tabIcon {
  font-size: 1.2rem;
}

.tabLabel {
  font-weight: 600;
}

.badge {
  background: #ff4757;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  min-width: 20px;
  text-align: center;
}

.tab.active .badge {
  background: rgba(255, 255, 255, 0.3);
}

/* Tab Content */
.tabContent {
  min-height: 400px;
}

.error {
  background: #fee;
  border: 1px solid #fcc;
  color: #c33;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  text-align: center;
}

.retryButton {
  background: #4ECDC4;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  margin-top: 0.5rem;
}

.loadingContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  gap: 1rem;
}

/* Recommendations Section */
.recommendationsSection {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 2px solid #f0f0f0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .dashboard {
    padding: 0.5rem;
  }

  .welcomeHeader {
    padding: 1.5rem;
    flex-direction: column;
    text-align: center;
  }

  .welcomeTitle {
    font-size: 1.5rem;
  }

  .welcomeSubtitle {
    font-size: 1rem;
  }

  .tabList {
    gap: 0.25rem;
  }

  .tab {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  .tabIcon {
    font-size: 1rem;
  }

  .tabLabel {
    display: none;
  }

  .tab.active .tabLabel {
    display: inline;
  }
}

@media (max-width: 480px) {
  .welcomeHeader {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .welcomeTitle {
    font-size: 1.25rem;
  }

  .tab {
    padding: 0.5rem 0.75rem;
    min-width: 60px;
    justify-content: center;
  }

  .tabLabel {
    display: none;
  }

  .badge {
    position: absolute;
    top: -5px;
    right: -5px;
    font-size: 0.7rem;
    min-width: 16px;
    height: 16px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .spinner {
    border-width: 2px;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .tabNavigation {
    border-bottom-color: #333;
  }

  .tab {
    color: #ccc;
  }

  .tab:hover {
    background: #333;
    color: #4ECDC4;
  }

  .error {
    background: #331;
    border-color: #553;
    color: #faa;
  }

  .recommendationsSection {
    border-top-color: #333;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .spinner {
    animation: none;
  }

  .tab {
    transition: none;
  }
}

/* Print Styles */
@media print {
  .welcomeHeader {
    background: none !important;
    color: black !important;
    border: 1px solid #ccc;
  }

  .tabNavigation {
    display: none;
  }

  .quickActionsContainer {
    display: none;
  }
}
