import { useState, useEffect } from 'react'
import Head from 'next/head'
import Link from 'next/link'
import { useRouter } from 'next/router'
import styles from '@/styles/Offline.module.css'

export default function OfflinePage() {
  const [isOnline, setIsOnline] = useState(true)
  const [retryCount, setRetryCount] = useState(0)
  const router = useRouter()

  useEffect(() => {
    // Check initial online status
    setIsOnline(navigator.onLine)

    // Listen for online/offline events
    const handleOnline = () => {
      setIsOnline(true)
      // Automatically redirect when back online
      setTimeout(() => {
        router.push('/')
      }, 1000)
    }

    const handleOffline = () => {
      setIsOnline(false)
    }

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [router])

  const handleRetry = () => {
    setRetryCount(prev => prev + 1)
    
    // Check if we're back online
    if (navigator.onLine) {
      router.push('/')
    } else {
      // Show feedback that we're still offline
      setTimeout(() => {
        setRetryCount(prev => prev - 1)
      }, 2000)
    }
  }

  const handleGoHome = () => {
    router.push('/')
  }

  return (
    <>
      <Head>
        <title>Ocean Soul Sparkles - Offline</title>
        <meta name="description" content="Ocean Soul Sparkles is currently offline. Please check your internet connection." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className={styles.container}>
        <div className={styles.content}>
          {/* Logo */}
          <div className={styles.logoContainer}>
            <img 
              src="/images/logo.png" 
              alt="Ocean Soul Sparkles" 
              className={styles.logo}
              onError={(e) => {
                e.target.style.display = 'none'
              }}
            />
          </div>

          {/* Status indicator */}
          <div className={`${styles.statusIndicator} ${isOnline ? styles.online : styles.offline}`}>
            <div className={styles.statusDot}></div>
            <span className={styles.statusText}>
              {isOnline ? 'Back Online!' : 'You\'re Offline'}
            </span>
          </div>

          {/* Main message */}
          <div className={styles.messageContainer}>
            <h1 className={styles.title}>
              {isOnline ? 'Connection Restored!' : 'No Internet Connection'}
            </h1>
            
            <p className={styles.description}>
              {isOnline 
                ? 'Great! Your internet connection has been restored. Redirecting you back to Ocean Soul Sparkles...'
                : 'Ocean Soul Sparkles requires an internet connection to function properly. Please check your connection and try again.'
              }
            </p>

            {!isOnline && (
              <div className={styles.troubleshooting}>
                <h3>Troubleshooting Tips:</h3>
                <ul>
                  <li>Check your WiFi or mobile data connection</li>
                  <li>Try moving to an area with better signal</li>
                  <li>Restart your router if using WiFi</li>
                  <li>Contact your internet service provider if issues persist</li>
                </ul>
              </div>
            )}
          </div>

          {/* Action buttons */}
          <div className={styles.actions}>
            {isOnline ? (
              <button 
                onClick={handleGoHome}
                className={`${styles.button} ${styles.primaryButton}`}
              >
                Go to Ocean Soul Sparkles
              </button>
            ) : (
              <>
                <button 
                  onClick={handleRetry}
                  className={`${styles.button} ${styles.primaryButton}`}
                  disabled={retryCount > 0}
                >
                  {retryCount > 0 ? 'Checking...' : 'Try Again'}
                </button>
                
                <Link href="/" className={`${styles.button} ${styles.secondaryButton}`}>
                  Go Home Anyway
                </Link>
              </>
            )}
          </div>

          {/* Cached content notice */}
          {!isOnline && (
            <div className={styles.cacheNotice}>
              <p>
                <strong>Limited Offline Access:</strong> Some previously viewed pages may still be available while offline.
              </p>
            </div>
          )}

          {/* Loading animation when back online */}
          {isOnline && (
            <div className={styles.loadingContainer}>
              <div className={styles.spinner}></div>
              <p>Redirecting...</p>
            </div>
          )}
        </div>

        {/* Background decoration */}
        <div className={styles.backgroundDecoration}>
          <div className={styles.sparkle}></div>
          <div className={styles.sparkle}></div>
          <div className={styles.sparkle}></div>
        </div>
      </div>
    </>
  )
}

// This page should be statically generated for offline access
export async function getStaticProps() {
  return {
    props: {},
    // Revalidate every hour
    revalidate: 3600
  }
}
