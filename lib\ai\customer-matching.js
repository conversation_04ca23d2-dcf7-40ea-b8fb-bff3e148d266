/**
 * Customer Matching Engine for Ocean Soul Sparkles
 * Implements AI-powered customer-artist compatibility scoring and recommendations
 */

import { supabase } from '@/lib/supabase'

export class CustomerMatchingEngine {
  constructor() {
    this.compatibilityCache = new Map()
    this.cacheExpiry = 60 * 60 * 1000 // 1 hour
  }

  /**
   * Calculate compatibility score between artist and customer
   * @param {string} artistId - Artist ID
   * @param {string} customerId - Customer ID
   * @returns {Promise<Object>} Compatibility analysis
   */
  async calculateArtistCustomerCompatibility(artistId, customerId) {
    const cacheKey = `${artistId}-${customerId}`
    const cached = this.compatibilityCache.get(cacheKey)
    
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      console.log('[CustomerMatchingEngine] Cache hit for compatibility calculation')
      return cached.score
    }

    try {
      console.log(`[CustomerMatchingEngine] Calculating compatibility: artist ${artistId} + customer ${customerId}`)
      
      const [artistData, customerData, historyData] = await Promise.all([
        this.getArtistProfile(artistId),
        this.getCustomerProfile(customerId),
        this.getInteractionHistory(artistId, customerId)
      ])

      const score = this.computeCompatibilityScore(artistData, customerData, historyData)
      
      this.compatibilityCache.set(cacheKey, {
        score,
        timestamp: Date.now()
      })

      console.log(`[CustomerMatchingEngine] Compatibility score: ${score.score.toFixed(2)} (confidence: ${score.confidence.toFixed(2)})`)
      return score
    } catch (error) {
      console.error('[CustomerMatchingEngine] Compatibility calculation failed:', error)
      return { 
        score: 0.5, 
        confidence: 0.1,
        factors: [],
        recommendation: {
          level: 'unknown',
          message: 'Unable to calculate compatibility',
          confidence: 'low'
        }
      }
    }
  }

  /**
   * Get artist profile data
   * @param {string} artistId - Artist ID
   * @returns {Promise<Object>} Artist profile
   */
  async getArtistProfile(artistId) {
    const { data: artist, error } = await supabase
      .from('artist_profiles')
      .select(`
        id,
        name,
        specializations,
        skill_level,
        experience_years,
        bio,
        portfolio_url,
        working_style,
        preferred_service_types,
        created_at
      `)
      .eq('id', artistId)
      .single()

    if (error) {
      console.error('[CustomerMatchingEngine] Error fetching artist profile:', error)
      throw new Error(`Failed to fetch artist profile: ${error.message}`)
    }

    // Get artist statistics
    const { data: stats } = await supabase
      .from('bookings')
      .select('customer_rating, total_amount, status')
      .eq('assigned_artist_id', artistId)
      .not('customer_rating', 'is', null)

    const ratings = stats?.filter(s => s.customer_rating).map(s => s.customer_rating) || []
    const completedBookings = stats?.filter(s => s.status === 'completed') || []

    return {
      ...artist,
      average_rating: ratings.length > 0 ? ratings.reduce((a, b) => a + b, 0) / ratings.length : 0,
      total_bookings: completedBookings.length,
      total_revenue: completedBookings.reduce((sum, b) => sum + (b.total_amount || 0), 0)
    }
  }

  /**
   * Get customer profile data
   * @param {string} customerId - Customer ID
   * @returns {Promise<Object>} Customer profile
   */
  async getCustomerProfile(customerId) {
    const { data: customer, error } = await supabase
      .from('customers')
      .select(`
        id,
        name,
        email,
        phone,
        preferences,
        notes,
        created_at
      `)
      .eq('id', customerId)
      .single()

    if (error) {
      console.error('[CustomerMatchingEngine] Error fetching customer profile:', error)
      throw new Error(`Failed to fetch customer profile: ${error.message}`)
    }

    // Get customer booking history
    const { data: bookings } = await supabase
      .from('bookings')
      .select(`
        id,
        start_time,
        status,
        customer_rating,
        artist_rating,
        total_amount,
        services(name, category),
        artist_profiles(name, specializations)
      `)
      .eq('customer_id', customerId)
      .order('start_time', { ascending: false })

    const completedBookings = bookings?.filter(b => b.status === 'completed') || []
    const ratings = completedBookings.filter(b => b.customer_rating).map(b => b.customer_rating)

    // Analyze service preferences
    const serviceCategories = {}
    completedBookings.forEach(booking => {
      const category = booking.services?.category || 'other'
      serviceCategories[category] = (serviceCategories[category] || 0) + 1
    })

    // Analyze artist preferences
    const artistTypes = {}
    completedBookings.forEach(booking => {
      const specializations = booking.artist_profiles?.specializations || []
      specializations.forEach(spec => {
        artistTypes[spec] = (artistTypes[spec] || 0) + 1
      })
    })

    return {
      ...customer,
      booking_history_count: completedBookings.length,
      average_rating_given: ratings.length > 0 ? ratings.reduce((a, b) => a + b, 0) / ratings.length : 0,
      total_spent: completedBookings.reduce((sum, b) => sum + (b.total_amount || 0), 0),
      service_preferences: Object.keys(serviceCategories).sort((a, b) => serviceCategories[b] - serviceCategories[a]),
      preferred_artist_types: Object.keys(artistTypes).sort((a, b) => artistTypes[b] - artistTypes[a]),
      communication_style: this.inferCommunicationStyle(customer, completedBookings)
    }
  }

  /**
   * Get interaction history between artist and customer
   * @param {string} artistId - Artist ID
   * @param {string} customerId - Customer ID
   * @returns {Promise<Array>} Interaction history
   */
  async getInteractionHistory(artistId, customerId) {
    const { data: bookings, error } = await supabase
      .from('bookings')
      .select(`
        id,
        start_time,
        end_time,
        status,
        customer_rating,
        artist_rating,
        notes,
        total_amount,
        services(name, category, duration)
      `)
      .eq('assigned_artist_id', artistId)
      .eq('customer_id', customerId)
      .order('start_time', { ascending: false })
      .limit(10)

    if (error) {
      console.error('[CustomerMatchingEngine] Error fetching interaction history:', error)
      return []
    }

    return bookings || []
  }

  /**
   * Compute compatibility score using multiple factors
   * @param {Object} artist - Artist data
   * @param {Object} customer - Customer data
   * @param {Array} history - Interaction history
   * @returns {Object} Compatibility analysis
   */
  computeCompatibilityScore(artist, customer, history) {
    let score = 0.5 // Base neutral score
    let confidence = 0.1
    const factors = []

    // Historical performance (40% weight)
    if (history.length > 0) {
      const avgCustomerRating = history
        .filter(b => b.customer_rating)
        .reduce((sum, b) => sum + b.customer_rating, 0) / history.filter(b => b.customer_rating).length || 0
      
      const avgArtistRating = history
        .filter(b => b.artist_rating)
        .reduce((sum, b) => sum + b.artist_rating, 0) / history.filter(b => b.artist_rating).length || 0

      const historicalScore = avgCustomerRating > 0 ? (avgCustomerRating / 5) : 0.5
      score += historicalScore * 0.4
      confidence += 0.4
      
      factors.push({
        factor: 'Historical Performance',
        score: historicalScore,
        weight: 0.4,
        details: `${history.length} previous bookings, avg customer rating: ${avgCustomerRating.toFixed(1)}/5`,
        impact: historicalScore > 0.7 ? 'positive' : historicalScore < 0.5 ? 'negative' : 'neutral'
      })
    }

    // Skill level match (25% weight)
    const skillMatch = this.calculateSkillMatch(artist, customer)
    score += skillMatch * 0.25
    confidence += 0.25
    
    factors.push({
      factor: 'Skill Level Match',
      score: skillMatch,
      weight: 0.25,
      details: `Artist skill: ${artist.skill_level || 'unknown'}, Experience: ${artist.experience_years || 0} years`,
      impact: skillMatch > 0.7 ? 'positive' : skillMatch < 0.5 ? 'negative' : 'neutral'
    })

    // Service specialization match (20% weight)
    const specializationMatch = this.calculateSpecializationMatch(artist, customer)
    score += specializationMatch * 0.2
    confidence += 0.2
    
    factors.push({
      factor: 'Specialization Match',
      score: specializationMatch,
      weight: 0.2,
      details: 'Service type preferences alignment',
      impact: specializationMatch > 0.7 ? 'positive' : specializationMatch < 0.5 ? 'negative' : 'neutral'
    })

    // Communication style compatibility (15% weight)
    const communicationMatch = this.calculateCommunicationMatch(artist, customer)
    score += communicationMatch * 0.15
    confidence += 0.15
    
    factors.push({
      factor: 'Communication Style',
      score: communicationMatch,
      weight: 0.15,
      details: 'Working style compatibility',
      impact: communicationMatch > 0.7 ? 'positive' : communicationMatch < 0.5 ? 'negative' : 'neutral'
    })

    return {
      score: Math.min(1, Math.max(0, score)), // Clamp to 0-1
      confidence: Math.min(1, confidence),
      factors,
      recommendation: this.generateRecommendation(score, factors),
      calculatedAt: new Date().toISOString(),
      artistId: artist.id,
      customerId: customer.id
    }
  }

  /**
   * Calculate skill level matching score
   * @param {Object} artist - Artist data
   * @param {Object} customer - Customer data
   * @returns {number} Skill match score (0-1)
   */
  calculateSkillMatch(artist, customer) {
    const skillLevels = { 'beginner': 1, 'intermediate': 2, 'advanced': 3, 'expert': 4 }
    const artistLevel = skillLevels[artist.skill_level] || 2
    
    // Consider customer's booking history and spending
    const customerTier = this.determineCustomerTier(customer)
    const tierPreference = { 'budget': 2, 'standard': 3, 'premium': 4 }
    const preferredLevel = tierPreference[customerTier] || 2
    
    // Calculate match based on how close artist skill is to customer preference
    const difference = Math.abs(artistLevel - preferredLevel)
    return Math.max(0, 1 - (difference / 3)) // Normalize to 0-1
  }

  /**
   * Calculate specialization matching score
   * @param {Object} artist - Artist data
   * @param {Object} customer - Customer data
   * @returns {number} Specialization match score (0-1)
   */
  calculateSpecializationMatch(artist, customer) {
    const artistSpecs = artist.specializations || []
    const customerPrefs = customer.preferred_artist_types || []
    
    if (artistSpecs.length === 0 || customerPrefs.length === 0) {
      return 0.5 // Neutral if no data
    }

    const matches = artistSpecs.filter(spec => 
      customerPrefs.some(pref => 
        spec.toLowerCase().includes(pref.toLowerCase()) ||
        pref.toLowerCase().includes(spec.toLowerCase())
      )
    )

    return matches.length / Math.max(artistSpecs.length, customerPrefs.length)
  }

  /**
   * Calculate communication style matching score
   * @param {Object} artist - Artist data
   * @param {Object} customer - Customer data
   * @returns {number} Communication match score (0-1)
   */
  calculateCommunicationMatch(artist, customer) {
    const artistStyle = artist.working_style || 'flexible'
    const customerStyle = customer.communication_style || 'standard'
    
    const compatibilityMatrix = {
      'professional-formal': 0.9,
      'professional-standard': 0.8,
      'professional-casual': 0.6,
      'friendly-casual': 0.9,
      'friendly-standard': 0.8,
      'friendly-formal': 0.7,
      'flexible-standard': 0.8,
      'flexible-casual': 0.8,
      'flexible-formal': 0.7
    }

    const key = `${artistStyle}-${customerStyle}`
    return compatibilityMatrix[key] || 0.7 // Default good compatibility
  }

  /**
   * Determine customer tier based on booking history
   * @param {Object} customer - Customer data
   * @returns {string} Customer tier
   */
  determineCustomerTier(customer) {
    const avgSpending = customer.total_spent / Math.max(customer.booking_history_count, 1)
    
    if (avgSpending > 150) return 'premium'
    if (avgSpending > 80) return 'standard'
    return 'budget'
  }

  /**
   * Infer communication style from customer data
   * @param {Object} customer - Customer data
   * @param {Array} bookings - Booking history
   * @returns {string} Communication style
   */
  inferCommunicationStyle(customer, bookings) {
    // Simple heuristic based on booking patterns and notes
    const hasDetailedNotes = bookings.some(b => b.notes && b.notes.length > 50)
    const frequentBooker = bookings.length > 5
    const highRater = customer.average_rating_given > 4
    
    if (hasDetailedNotes && highRater) return 'formal'
    if (frequentBooker) return 'standard'
    return 'casual'
  }

  /**
   * Generate recommendation based on compatibility score
   * @param {number} score - Compatibility score
   * @param {Array} factors - Contributing factors
   * @returns {Object} Recommendation
   */
  generateRecommendation(score, factors) {
    const positiveFactors = factors.filter(f => f.impact === 'positive').length
    const negativeFactors = factors.filter(f => f.impact === 'negative').length
    
    if (score >= 0.8) {
      return {
        level: 'excellent',
        message: 'Highly recommended match with strong compatibility across multiple factors',
        confidence: 'high',
        reasoning: `${positiveFactors} positive compatibility factors identified`
      }
    } else if (score >= 0.6) {
      return {
        level: 'good',
        message: 'Good match with positive compatibility indicators',
        confidence: 'medium',
        reasoning: 'Solid compatibility with room for positive outcomes'
      }
    } else if (score >= 0.4) {
      return {
        level: 'fair',
        message: 'Acceptable match, monitor for customer satisfaction',
        confidence: 'medium',
        reasoning: negativeFactors > 0 ? `${negativeFactors} potential compatibility concerns` : 'Limited compatibility data available'
      }
    } else {
      return {
        level: 'poor',
        message: 'Consider alternative artist assignment for better customer experience',
        confidence: 'low',
        reasoning: `${negativeFactors} compatibility concerns identified`
      }
    }
  }

  /**
   * Get recommended artists for a customer
   * @param {string} customerId - Customer ID
   * @param {string} serviceId - Service ID (optional)
   * @param {number} limit - Maximum number of recommendations
   * @returns {Promise<Array>} Recommended artists
   */
  async getRecommendedArtists(customerId, serviceId = null, limit = 5) {
    try {
      console.log(`[CustomerMatchingEngine] Getting recommendations for customer ${customerId}`)
      
      // Get all available artists
      let query = supabase
        .from('artist_profiles')
        .select(`
          id,
          name,
          specializations,
          skill_level,
          experience_years,
          bio,
          portfolio_url,
          working_style,
          average_rating,
          total_bookings
        `)
        .eq('active', true)

      const { data: artists, error } = await query

      if (error) {
        throw new Error(`Failed to fetch artists: ${error.message}`)
      }

      if (!artists || artists.length === 0) {
        return []
      }

      // Calculate compatibility scores for each artist
      const scoredArtists = await Promise.all(
        artists.map(async (artist) => {
          const compatibility = await this.calculateArtistCustomerCompatibility(
            artist.id, 
            customerId
          )
          
          return {
            ...artist,
            compatibilityScore: compatibility.score,
            compatibilityConfidence: compatibility.confidence,
            compatibilityFactors: compatibility.factors,
            recommendation: compatibility.recommendation,
            matchReasoning: compatibility.recommendation.reasoning
          }
        })
      )

      // Sort by compatibility score and return top matches
      const recommendations = scoredArtists
        .sort((a, b) => {
          // Primary sort: compatibility score
          if (b.compatibilityScore !== a.compatibilityScore) {
            return b.compatibilityScore - a.compatibilityScore
          }
          // Secondary sort: confidence level
          if (b.compatibilityConfidence !== a.compatibilityConfidence) {
            return b.compatibilityConfidence - a.compatibilityConfidence
          }
          // Tertiary sort: artist rating
          return (b.average_rating || 0) - (a.average_rating || 0)
        })
        .slice(0, limit)

      console.log(`[CustomerMatchingEngine] Generated ${recommendations.length} recommendations`)
      return recommendations

    } catch (error) {
      console.error('[CustomerMatchingEngine] Artist recommendation failed:', error)
      return []
    }
  }

  /**
   * Clear compatibility cache
   */
  clearCache() {
    this.compatibilityCache.clear()
    console.log('[CustomerMatchingEngine] Cache cleared')
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      size: this.compatibilityCache.size,
      maxAge: this.cacheExpiry
    }
  }
}

// Export singleton instance
export const customerMatchingEngine = new CustomerMatchingEngine()

// Export class for testing
export default CustomerMatchingEngine
