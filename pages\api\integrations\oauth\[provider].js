/**
 * OAuth Authorization Endpoint for Ocean Soul Sparkles Integrations
 * Handles OAuth authorization flow initiation for third-party providers
 * 
 * Phase 7: Advanced Integrations & Ecosystem
 */

import oauthManager from '@/lib/integrations/oauth-manager'
import { authenticateAdminRequest } from '@/lib/admin-auth'
import { oauthRateLimit } from '@/lib/integrations/rate-limiter'
import { RequestValidator, AuditLogger, SecurityError } from '@/lib/integrations/security-utils'
import { useMobileOptimization } from '@/lib/hooks/useMobileOptimization'

/**
 * OAuth Authorization Handler
 * GET /api/integrations/oauth/[provider] - Initiate OAuth flow
 */
export default async function handler(req, res) {
  // Apply rate limiting
  await new Promise((resolve, reject) => {
    oauthRateLimit(req, res, (error) => {
      if (error) reject(error)
      else resolve()
    })
  })

  const { provider } = req.query
  const startTime = Date.now()

  try {
    // Validate request method
    RequestValidator.validateEndpointAccess(req, ['GET'])

    // Authenticate user
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.success) {
      await AuditLogger.logSecurityEvent(
        null,
        'oauth_unauthorized_access',
        {
          provider,
          endpoint: req.url,
          userAgent: req.headers['user-agent'],
          ipAddress: req.headers['x-forwarded-for'] || req.connection?.remoteAddress
        },
        'warning'
      )

      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required for OAuth flow'
      })
    }

    const { user } = authResult
    const userId = user.id

    // Validate provider
    const availableProviders = oauthManager.getAvailableProviders()
    const providerConfig = availableProviders.find(p => p.id === provider)
    
    if (!providerConfig) {
      await AuditLogger.logSecurityEvent(
        userId,
        'oauth_invalid_provider',
        { provider, availableProviders: availableProviders.map(p => p.id) },
        'warning'
      )

      return res.status(400).json({
        error: 'Invalid provider',
        message: `Provider '${provider}' is not supported`,
        availableProviders: availableProviders.map(p => ({ id: p.id, name: p.name }))
      })
    }

    // Check if user already has this integration
    const existingIntegrations = await oauthManager.getIntegrationStatus(userId, provider)
    const existingIntegration = existingIntegrations.find(i => i.provider === provider)

    if (existingIntegration && existingIntegration.connected && !existingIntegration.needsRefresh) {
      await AuditLogger.logIntegrationActivity(
        userId,
        provider,
        'oauth_already_connected',
        'info',
        { existingIntegration }
      )

      return res.status(200).json({
        success: true,
        message: 'Integration already connected',
        integration: existingIntegration,
        action: 'already_connected'
      })
    }

    // Generate redirect URI
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
    const redirectUri = `${baseUrl}/api/integrations/oauth/callback/${provider}`

    // Generate authorization URL
    const authUrl = oauthManager.generateAuthUrl(provider, userId, redirectUri)

    // Log OAuth initiation
    await AuditLogger.logIntegrationActivity(
      userId,
      provider,
      'oauth_initiated',
      'success',
      {
        redirectUri,
        scopes: providerConfig.scopes,
        userAgent: req.headers['user-agent']
      }
    )

    // Log API access
    await AuditLogger.logApiAccess(
      userId,
      req.url,
      req.method,
      200,
      Date.now() - startTime,
      { provider, action: 'oauth_initiate' }
    )

    // Return authorization URL for client-side redirect
    return res.status(200).json({
      success: true,
      authUrl,
      provider: {
        id: provider,
        name: providerConfig.name,
        scopes: providerConfig.scopes
      },
      redirectUri,
      message: 'OAuth authorization URL generated successfully'
    })

  } catch (error) {
    console.error('OAuth authorization error:', error)

    // Log error
    await AuditLogger.logSecurityEvent(
      req.user?.id || null,
      'oauth_authorization_error',
      {
        provider,
        error: error.message,
        stack: error.stack,
        userAgent: req.headers['user-agent'],
        ipAddress: req.headers['x-forwarded-for'] || req.connection?.remoteAddress
      },
      'error'
    )

    // Log API access
    await AuditLogger.logApiAccess(
      req.user?.id || null,
      req.url,
      req.method,
      500,
      Date.now() - startTime,
      { provider, error: error.message }
    )

    if (error instanceof SecurityError) {
      return res.status(error.statusCode).json({
        error: error.code,
        message: error.message
      })
    }

    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to initiate OAuth authorization'
    })
  }
}

/**
 * API Route Configuration
 */
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
}
