/**
 * Communication Center Component - Phase 8: Advanced Customer Experience
 * Ocean Soul Sparkles - Customer Communication Hub
 */

import { useState, useEffect } from 'react'
import { useMobileOptimization } from '@/lib/hooks/useMobileOptimization'
import { toast } from 'react-toastify'
import MessagingInterface from './MessagingInterface'
import styles from '@/styles/customer/CommunicationCenter.module.css'

export default function CommunicationCenter({ customer, unreadCount, onMessageUpdate }) {
  const { isMobile, viewport } = useMobileOptimization()
  
  const [conversations, setConversations] = useState([])
  const [selectedConversation, setSelectedConversation] = useState(null)
  const [loading, setLoading] = useState(false)
  const [showNewConversation, setShowNewConversation] = useState(false)

  useEffect(() => {
    loadConversations()
  }, [])

  const loadConversations = async () => {
    try {
      setLoading(true)
      
      const response = await fetch('/api/customer/messaging/conversations')
      const data = await response.json()
      
      if (data.success) {
        setConversations(data.conversations || [])
        
        // Auto-select first conversation if none selected
        if (!selectedConversation && data.conversations?.length > 0) {
          setSelectedConversation(data.conversations[0].id)
        }
      } else {
        throw new Error(data.error || 'Failed to load conversations')
      }
    } catch (error) {
      console.error('Error loading conversations:', error)
      toast.error('Failed to load conversations')
    } finally {
      setLoading(false)
    }
  }

  const createNewConversation = async (title = 'General Inquiry') => {
    try {
      const response = await fetch('/api/customer/messaging/conversations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ title })
      })

      const data = await response.json()

      if (data.success) {
        setConversations(prev => [data.conversation, ...prev])
        setSelectedConversation(data.conversation.id)
        setShowNewConversation(false)
        toast.success('New conversation started')
      } else {
        throw new Error(data.error || 'Failed to create conversation')
      }
    } catch (error) {
      console.error('Error creating conversation:', error)
      toast.error('Failed to create conversation')
    }
  }

  const formatLastMessageTime = (timestamp) => {
    if (!timestamp) return ''
    
    const date = new Date(timestamp)
    const now = new Date()
    const diffInHours = (now - date) / (1000 * 60 * 60)
    
    if (diffInHours < 1) {
      return 'Just now'
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      return `${diffInDays}d ago`
    }
  }

  const handleConversationSelect = (conversationId) => {
    setSelectedConversation(conversationId)
    if (onMessageUpdate) {
      onMessageUpdate()
    }
  }

  if (conversations.length === 0 && !loading) {
    return (
      <div className={styles.emptyState}>
        <div className={styles.emptyIcon}>💬</div>
        <h3 className={styles.emptyTitle}>No Conversations Yet</h3>
        <p className={styles.emptyDescription}>
          Start a conversation with our team to get help or ask questions.
        </p>
        <button
          onClick={() => createNewConversation()}
          className={styles.startConversationButton}
        >
          Start Conversation
        </button>
      </div>
    )
  }

  return (
    <div className={styles.communicationCenter}>
      {/* Conversations Sidebar */}
      <div className={styles.conversationsSidebar}>
        <div className={styles.sidebarHeader}>
          <h3 className={styles.sidebarTitle}>Messages</h3>
          <button
            onClick={() => setShowNewConversation(true)}
            className={styles.newConversationButton}
          >
            ➕
          </button>
        </div>

        {loading ? (
          <div className={styles.loading}>
            <div className={styles.spinner}></div>
            <p>Loading conversations...</p>
          </div>
        ) : (
          <div className={styles.conversationsList}>
            {conversations.map(conversation => (
              <div
                key={conversation.id}
                className={`${styles.conversationItem} ${
                  selectedConversation === conversation.id ? styles.selected : ''
                }`}
                onClick={() => handleConversationSelect(conversation.id)}
              >
                <div className={styles.conversationInfo}>
                  <h4 className={styles.conversationTitle}>
                    {conversation.title || 'General Inquiry'}
                  </h4>
                  <p className={styles.conversationPreview}>
                    {conversation.last_message || 'No messages yet'}
                  </p>
                </div>
                
                <div className={styles.conversationMeta}>
                  <span className={styles.conversationTime}>
                    {formatLastMessageTime(conversation.last_message_at)}
                  </span>
                  {conversation.unread_count > 0 && (
                    <span className={styles.unreadBadge}>
                      {conversation.unread_count}
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Messages Area */}
      <div className={styles.messagesArea}>
        {selectedConversation ? (
          <MessagingInterface
            conversationId={selectedConversation}
            onMessageUpdate={onMessageUpdate}
          />
        ) : (
          <div className={styles.noConversationSelected}>
            <div className={styles.noConversationIcon}>💬</div>
            <h3 className={styles.noConversationTitle}>Select a Conversation</h3>
            <p className={styles.noConversationDescription}>
              Choose a conversation from the sidebar to start messaging.
            </p>
          </div>
        )}
      </div>

      {/* New Conversation Modal */}
      {showNewConversation && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <div className={styles.modalHeader}>
              <h3 className={styles.modalTitle}>Start New Conversation</h3>
              <button
                onClick={() => setShowNewConversation(false)}
                className={styles.closeButton}
              >
                ×
              </button>
            </div>

            <div className={styles.modalContent}>
              <p className={styles.modalDescription}>
                What would you like to discuss?
              </p>
              
              <div className={styles.conversationTopics}>
                <button
                  onClick={() => createNewConversation('General Inquiry')}
                  className={styles.topicButton}
                >
                  <span className={styles.topicIcon}>❓</span>
                  <span className={styles.topicLabel}>General Question</span>
                </button>

                <button
                  onClick={() => createNewConversation('Booking Support')}
                  className={styles.topicButton}
                >
                  <span className={styles.topicIcon}>📅</span>
                  <span className={styles.topicLabel}>Booking Help</span>
                </button>

                <button
                  onClick={() => createNewConversation('Service Feedback')}
                  className={styles.topicButton}
                >
                  <span className={styles.topicIcon}>⭐</span>
                  <span className={styles.topicLabel}>Service Feedback</span>
                </button>

                <button
                  onClick={() => createNewConversation('Technical Support')}
                  className={styles.topicButton}
                >
                  <span className={styles.topicIcon}>🔧</span>
                  <span className={styles.topicLabel}>Technical Issue</span>
                </button>

                <button
                  onClick={() => createNewConversation('Billing Question')}
                  className={styles.topicButton}
                >
                  <span className={styles.topicIcon}>💳</span>
                  <span className={styles.topicLabel}>Billing Question</span>
                </button>

                <button
                  onClick={() => createNewConversation('Other')}
                  className={styles.topicButton}
                >
                  <span className={styles.topicIcon}>💬</span>
                  <span className={styles.topicLabel}>Other</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
