-- =============================================
-- SUPABASE SECURITY DEFINER VIEWS FIX
-- Ocean Soul Sparkles Database Security Remediation
-- =============================================

-- This script fixes all 18 SECURITY DEFINER views identified by Supabase security linter
-- Converts SECURITY DEFINER views to SECURITY INVOKER with proper RLS policies
-- Maintains compatibility with 5-tier role system (DEV, Admin, Artist, Braider, User)

-- =============================================
-- HELPER FUNCTIONS FOR ROLE CHECKING
-- =============================================

-- Create enhanced role checking functions using public.user_roles table
CREATE OR REPLACE FUNCTION public.is_admin_or_staff()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.user_roles 
    WHERE id = auth.uid() 
    AND role IN ('admin', 'dev')
  );
END;
$$ LANGUAGE plpgsql SECURITY INVOKER;

-- Create function to check if user has staff privileges (artist/braider/admin/dev)
CREATE OR REPLACE FUNCTION public.is_staff_or_above()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.user_roles 
    WHERE id = auth.uid() 
    AND role IN ('dev', 'admin', 'artist', 'braider')
  );
END;
$$ LANGUAGE plpgsql SECURITY INVOKER;

-- Create function to check if user can access artist data
CREATE OR REPLACE FUNCTION public.can_access_artist_data(artist_id UUID DEFAULT NULL)
RETURNS BOOLEAN AS $$
BEGIN
  -- Admin/Dev can access all artist data
  IF public.is_admin_or_staff() THEN
    RETURN TRUE;
  END IF;
  
  -- Artists can access their own data
  IF artist_id IS NOT NULL THEN
    RETURN EXISTS (
      SELECT 1 FROM public.artist_profiles 
      WHERE user_id = auth.uid() AND id = artist_id
    );
  END IF;
  
  -- Artists can access general artist data
  RETURN EXISTS (
    SELECT 1 FROM public.user_roles 
    WHERE id = auth.uid() 
    AND role IN ('artist', 'braider')
  );
END;
$$ LANGUAGE plpgsql SECURITY INVOKER;

-- =============================================
-- FIX VIEW 1: CUSTOMER_STATISTICS
-- =============================================

DROP VIEW IF EXISTS public.customer_statistics CASCADE;
CREATE VIEW public.customer_statistics 
WITH (security_invoker = true) AS
SELECT 
  COUNT(*) as total_customers,
  COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as new_customers_30d,
  COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '7 days' THEN 1 END) as new_customers_7d
FROM customers
WHERE public.is_admin_or_staff();

REVOKE ALL ON public.customer_statistics FROM anon, public;
GRANT SELECT ON public.customer_statistics TO authenticated;

-- =============================================
-- FIX VIEW 2: PRODUCT_STOCK_STATUS
-- =============================================

DROP VIEW IF EXISTS public.product_stock_status CASCADE;
CREATE VIEW public.product_stock_status
WITH (security_invoker = true) AS
SELECT 
  p.id,
  p.name,
  p.current_stock,
  p.minimum_stock,
  CASE 
    WHEN p.current_stock <= p.minimum_stock THEN 'LOW'
    WHEN p.current_stock <= p.minimum_stock * 2 THEN 'MEDIUM'
    ELSE 'HIGH'
  END as stock_level
FROM products p
WHERE public.is_admin_or_staff();

REVOKE ALL ON public.product_stock_status FROM anon, public;
GRANT SELECT ON public.product_stock_status TO authenticated;

-- =============================================
-- FIX VIEW 3: INVENTORY_SUMMARY
-- =============================================

DROP VIEW IF EXISTS public.inventory_summary CASCADE;
CREATE VIEW public.inventory_summary
WITH (security_invoker = true) AS
SELECT 
  COUNT(*) as total_products,
  SUM(current_stock * price) as total_inventory_value,
  COUNT(CASE WHEN current_stock <= minimum_stock THEN 1 END) as low_stock_items
FROM products
WHERE public.is_admin_or_staff();

REVOKE ALL ON public.inventory_summary FROM anon, public;
GRANT SELECT ON public.inventory_summary TO authenticated;

-- =============================================
-- FIX VIEW 4: ARTIST_AVAILABILITY_VIEW
-- =============================================

DROP VIEW IF EXISTS public.artist_availability_view CASCADE;
CREATE VIEW public.artist_availability_view
WITH (security_invoker = true) AS
SELECT 
  ap.id,
  ap.user_id,
  ap.display_name,
  ap.specializations,
  ap.bio,
  ap.profile_image_url,
  ap.hourly_rate,
  ap.is_available,
  ap.created_at,
  ap.updated_at
FROM artist_profiles ap
WHERE public.can_access_artist_data(ap.id);

REVOKE ALL ON public.artist_availability_view FROM anon, public;
GRANT SELECT ON public.artist_availability_view TO authenticated;

-- =============================================
-- FIX VIEW 5: SERVICES_WITH_AVAILABLE_ARTISTS
-- =============================================

DROP VIEW IF EXISTS public.services_with_available_artists CASCADE;
CREATE VIEW public.services_with_available_artists
WITH (security_invoker = true) AS
SELECT 
  s.*,
  COALESCE(
    json_agg(
      json_build_object(
        'artist_id', ap.id,
        'artist_name', ap.display_name,
        'hourly_rate', ap.hourly_rate
      )
    ) FILTER (WHERE ap.id IS NOT NULL), 
    '[]'::json
  ) as available_artists
FROM services s
LEFT JOIN artist_service_specializations ass ON s.id = ass.service_id
LEFT JOIN artist_profiles ap ON ass.artist_id = ap.id AND ap.is_available = true
WHERE public.is_staff_or_above() OR s.status = 'active'
GROUP BY s.id;

REVOKE ALL ON public.services_with_available_artists FROM anon, public;
GRANT SELECT ON public.services_with_available_artists TO authenticated;

-- =============================================
-- FIX VIEW 6: ARTIST_CURRENT_AVAILABILITY
-- =============================================

DROP VIEW IF EXISTS public.artist_current_availability CASCADE;
CREATE VIEW public.artist_current_availability
WITH (security_invoker = true) AS
SELECT 
  ap.id as artist_id,
  ap.display_name,
  ap.is_available,
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM artist_availability_exceptions aae 
      WHERE aae.artist_id = ap.id 
      AND aae.start_date <= CURRENT_DATE 
      AND aae.end_date >= CURRENT_DATE
    ) THEN false
    ELSE ap.is_available
  END as currently_available
FROM artist_profiles ap
WHERE public.can_access_artist_data(ap.id);

REVOKE ALL ON public.artist_current_availability FROM anon, public;
GRANT SELECT ON public.artist_current_availability TO authenticated;

-- =============================================
-- FIX VIEW 7: QUICK_EVENTS_SUMMARY
-- =============================================

DROP VIEW IF EXISTS public.quick_events_summary CASCADE;
CREATE VIEW public.quick_events_summary
WITH (security_invoker = true) AS
SELECT 
  qe.id,
  qe.transaction_id,
  qe.service_name,
  qe.tier_name,
  qe.duration,
  qe.amount,
  qe.currency,
  qe.payment_method,
  qe.payment_status,
  qe.created_at,
  s.category as service_category,
  qe.created_by as created_by_id -- Only expose UUID, not email for security
FROM quick_events qe
LEFT JOIN services s ON qe.service_id = s.id
WHERE public.is_admin_or_staff()
ORDER BY qe.created_at DESC;

REVOKE ALL ON public.quick_events_summary FROM anon, public;
GRANT SELECT ON public.quick_events_summary TO authenticated;

-- =============================================
-- FIX VIEW 8: SERVICES_WITH_PRICING
-- =============================================

DROP VIEW IF EXISTS public.services_with_pricing CASCADE;
CREATE VIEW public.services_with_pricing
WITH (security_invoker = true) AS
SELECT 
  s.*,
  COALESCE(
    json_agg(
      json_build_object(
        'id', spt.id,
        'name', spt.name,
        'description', spt.description,
        'duration', spt.duration,
        'price', spt.price,
        'is_default', spt.is_default,
        'sort_order', spt.sort_order
      ) ORDER BY spt.sort_order, spt.name
    ) FILTER (WHERE spt.id IS NOT NULL),
    '[]'::json
  ) as pricing_tiers
FROM services s
LEFT JOIN service_pricing_tiers spt ON s.id = spt.service_id
GROUP BY s.id;

REVOKE ALL ON public.services_with_pricing FROM anon, public;
GRANT SELECT ON public.services_with_pricing TO authenticated;

-- =============================================
-- FIX VIEW 9: CUSTOMER_ANALYTICS_VIEW
-- =============================================

DROP VIEW IF EXISTS public.customer_analytics_view CASCADE;
CREATE VIEW public.customer_analytics_view
WITH (security_invoker = true) AS
SELECT
  c.id,
  c.name,
  c.email,
  c.phone,
  COUNT(b.id) as total_bookings,
  SUM(b.total_amount) as total_spent,
  MAX(b.created_at) as last_booking_date,
  MIN(b.created_at) as first_booking_date
FROM customers c
LEFT JOIN bookings b ON c.id = b.customer_id
WHERE public.is_admin_or_staff()
GROUP BY c.id, c.name, c.email, c.phone;

REVOKE ALL ON public.customer_analytics_view FROM anon, public;
GRANT SELECT ON public.customer_analytics_view TO authenticated;

-- =============================================
-- FIX VIEW 10: BOOKING_ANALYTICS
-- =============================================

DROP VIEW IF EXISTS public.booking_analytics CASCADE;
CREATE VIEW public.booking_analytics
WITH (security_invoker = true) AS
SELECT
  DATE_TRUNC('month', created_at) as month,
  COUNT(*) as total_bookings,
  SUM(total_amount) as total_revenue,
  AVG(total_amount) as avg_booking_value,
  COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_bookings,
  COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_bookings
FROM bookings
WHERE public.is_admin_or_staff()
GROUP BY DATE_TRUNC('month', created_at)
ORDER BY month DESC;

REVOKE ALL ON public.booking_analytics FROM anon, public;
GRANT SELECT ON public.booking_analytics TO authenticated;

-- =============================================
-- FIX VIEW 11: POPULAR_TIME_SLOTS
-- =============================================

DROP VIEW IF EXISTS public.popular_time_slots CASCADE;
CREATE VIEW public.popular_time_slots
WITH (security_invoker = true) AS
SELECT
  EXTRACT(hour FROM scheduled_date) as hour_of_day,
  EXTRACT(dow FROM scheduled_date) as day_of_week,
  COUNT(*) as booking_count
FROM bookings
WHERE public.is_admin_or_staff()
  AND scheduled_date IS NOT NULL
  AND status IN ('confirmed', 'completed')
GROUP BY EXTRACT(hour FROM scheduled_date), EXTRACT(dow FROM scheduled_date)
ORDER BY booking_count DESC;

REVOKE ALL ON public.popular_time_slots FROM anon, public;
GRANT SELECT ON public.popular_time_slots TO authenticated;

-- =============================================
-- FIX VIEW 12: CUSTOMER_ANALYTICS_SUMMARY
-- =============================================

DROP VIEW IF EXISTS public.customer_analytics_summary CASCADE;
CREATE VIEW public.customer_analytics_summary
WITH (security_invoker = true) AS
SELECT
  COUNT(DISTINCT c.id) as total_customers,
  COUNT(DISTINCT CASE WHEN b.created_at >= CURRENT_DATE - INTERVAL '30 days' THEN c.id END) as active_customers_30d,
  AVG(customer_stats.total_bookings) as avg_bookings_per_customer,
  AVG(customer_stats.total_spent) as avg_spent_per_customer
FROM customers c
LEFT JOIN bookings b ON c.id = b.customer_id
LEFT JOIN (
  SELECT
    customer_id,
    COUNT(*) as total_bookings,
    SUM(total_amount) as total_spent
  FROM bookings
  GROUP BY customer_id
) customer_stats ON c.id = customer_stats.customer_id
WHERE public.is_admin_or_staff();

REVOKE ALL ON public.customer_analytics_summary FROM anon, public;
GRANT SELECT ON public.customer_analytics_summary TO authenticated;

-- =============================================
-- FIX VIEW 13: EVENT_REVENUE_ANALYTICS
-- =============================================

DROP VIEW IF EXISTS public.event_revenue_analytics CASCADE;
CREATE VIEW public.event_revenue_analytics
WITH (security_invoker = true) AS
SELECT
  DATE_TRUNC('day', created_at) as date,
  COUNT(*) as total_events,
  SUM(amount) as total_revenue,
  AVG(amount) as avg_event_value,
  COUNT(CASE WHEN payment_status = 'completed' THEN 1 END) as completed_payments
FROM quick_events
WHERE public.is_admin_or_staff()
GROUP BY DATE_TRUNC('day', created_at)
ORDER BY date DESC;

REVOKE ALL ON public.event_revenue_analytics FROM anon, public;
GRANT SELECT ON public.event_revenue_analytics TO authenticated;

-- =============================================
-- FIX VIEW 14: SERVICES_QUICK_EVENT_SUMMARY
-- =============================================

DROP VIEW IF EXISTS public.services_quick_event_summary CASCADE;
CREATE VIEW public.services_quick_event_summary
WITH (security_invoker = true) AS
SELECT
  s.id as service_id,
  s.name as service_name,
  s.category,
  COUNT(qe.id) as quick_event_count,
  SUM(qe.amount) as total_revenue,
  AVG(qe.amount) as avg_event_value
FROM services s
LEFT JOIN quick_events qe ON s.id = qe.service_id
WHERE public.is_admin_or_staff()
GROUP BY s.id, s.name, s.category
ORDER BY quick_event_count DESC;

REVOKE ALL ON public.services_quick_event_summary FROM anon, public;
GRANT SELECT ON public.services_quick_event_summary TO authenticated;
