{"timestamp": "2025-06-11T09:34:20.146Z", "environment": "development", "summary": {"total": 28, "passed": 1, "failed": 24, "warnings": 3}, "categories": {"apiConnections": {"tests": [{"name": "Supabase Database Connection", "status": "failed", "message": "Database connection error: request to http://localhost:3000/api/admin/diagnostics/database failed, reason: ", "details": {}, "timestamp": "2025-06-11T09:34:20.196Z"}, {"name": "Storage Service", "status": "failed", "message": "Storage service error: request to http://localhost:3000/api/admin/diagnostics/storage failed, reason: ", "details": {}, "timestamp": "2025-06-11T09:34:20.199Z"}, {"name": "Admin Health Check", "status": "failed", "message": "Admin Health Check error: request to http://localhost:3000/api/admin/health failed, reason: ", "details": {}, "timestamp": "2025-06-11T09:34:20.203Z"}, {"name": "Customers API", "status": "failed", "message": "Customers API error: request to http://localhost:3000/api/admin/customers failed, reason: ", "details": {}, "timestamp": "2025-06-11T09:34:20.206Z"}, {"name": "Bookings API", "status": "failed", "message": "Bookings API error: request to http://localhost:3000/api/admin/bookings failed, reason: ", "details": {}, "timestamp": "2025-06-11T09:34:20.208Z"}, {"name": "Services API", "status": "failed", "message": "Services API error: request to http://localhost:3000/api/admin/services failed, reason: ", "details": {}, "timestamp": "2025-06-11T09:34:20.211Z"}, {"name": "Public Services API", "status": "failed", "message": "Public Services API error: request to http://localhost:3000/api/public/services failed, reason: ", "details": {}, "timestamp": "2025-06-11T09:34:20.213Z"}, {"name": "Public Products API", "status": "failed", "message": "Public Products API error: request to http://localhost:3000/api/public/products failed, reason: ", "details": {}, "timestamp": "2025-06-11T09:34:20.218Z"}], "status": "failed"}, "authentication": {"tests": [{"name": "Admin Au<PERSON>ntication", "status": "failed", "message": "No authentication token available for testing", "details": {}, "timestamp": "2025-06-11T09:34:20.219Z"}, {"name": "JWT Token Validation", "status": "failed", "message": "JWT validation error: request to http://localhost:3000/api/admin/diagnostics/auth-test failed, reason: ", "details": {}, "timestamp": "2025-06-11T09:34:20.226Z"}, {"name": "Public API Access", "status": "failed", "message": "Public API access error: request to http://localhost:3000/api/public/services failed, reason: ", "details": {}, "timestamp": "2025-06-11T09:34:20.229Z"}], "status": "failed"}, "webpages": {"tests": [{"name": "Home Page", "status": "failed", "message": "Home Page error: request to http://localhost:3000/ failed, reason: ", "details": {}, "timestamp": "2025-06-11T09:34:20.234Z"}, {"name": "About Page", "status": "failed", "message": "About Page error: request to http://localhost:3000/about failed, reason: ", "details": {}, "timestamp": "2025-06-11T09:34:20.238Z"}, {"name": "Services Page", "status": "failed", "message": "Services Page error: request to http://localhost:3000/services failed, reason: ", "details": {}, "timestamp": "2025-06-11T09:34:20.241Z"}, {"name": "Shop Page", "status": "failed", "message": "Shop Page error: request to http://localhost:3000/shop failed, reason: ", "details": {}, "timestamp": "2025-06-11T09:34:20.243Z"}, {"name": "Contact Page", "status": "failed", "message": "Contact Page error: request to http://localhost:3000/contact failed, reason: ", "details": {}, "timestamp": "2025-06-11T09:34:20.246Z"}, {"name": "Book Online Page", "status": "failed", "message": "Book Online Page error: request to http://localhost:3000/book-online failed, reason: ", "details": {}, "timestamp": "2025-06-11T09:34:20.251Z"}, {"name": "Gallery Page", "status": "failed", "message": "Gallery Page error: request to http://localhost:3000/gallery failed, reason: ", "details": {}, "timestamp": "2025-06-11T09:34:20.253Z"}, {"name": "Admin Dashboard", "status": "failed", "message": "Admin Dashboard error: request to http://localhost:3000/admin failed, reason: ", "details": {}, "timestamp": "2025-06-11T09:34:20.256Z"}, {"name": "Admin Customers", "status": "failed", "message": "Admin Customers error: request to http://localhost:3000/admin/customers failed, reason: ", "details": {}, "timestamp": "2025-06-11T09:34:20.257Z"}, {"name": "Admin Bookings", "status": "failed", "message": "Admin Bookings error: request to http://localhost:3000/admin/bookings failed, reason: ", "details": {}, "timestamp": "2025-06-11T09:34:20.259Z"}, {"name": "<PERSON><PERSON> Inventory", "status": "failed", "message": "Admin Inventory error: request to http://localhost:3000/admin/inventory failed, reason: ", "details": {}, "timestamp": "2025-06-11T09:34:20.261Z"}, {"name": "Admin Diagnostics", "status": "failed", "message": "Admin Diagnostics error: request to http://localhost:3000/admin/diagnostics failed, reason: ", "details": {}, "timestamp": "2025-06-11T09:34:20.262Z"}], "status": "failed"}, "integrations": {"tests": [{"name": "OneSignal Configuration", "status": "warning", "message": "OneSignal app ID not configured", "details": {}, "timestamp": "2025-06-11T09:34:20.263Z"}, {"name": "Email Configuration", "status": "warning", "message": "Email configuration incomplete or missing", "details": {}, "timestamp": "2025-06-11T09:34:20.263Z"}, {"name": "Square Payment Integration", "status": "warning", "message": "Square payment configuration not found", "details": {}, "timestamp": "2025-06-11T09:34:20.263Z"}], "status": "failed"}, "configuration": {"tests": [{"name": "Required Environment Variables", "status": "failed", "message": "Missing environment variables: NEXT_PUBLIC_SUPABASE_URL, NEXT_PUBLIC_SUPABASE_ANON_KEY, SUPABASE_SERVICE_ROLE_KEY, NEXT_PUBLIC_SITE_URL", "details": {}, "timestamp": "2025-06-11T09:34:20.188Z"}, {"name": "Build Scripts", "status": "passed", "message": "Build and start scripts configured", "details": {}, "timestamp": "2025-06-11T09:34:20.189Z"}], "status": "failed"}}, "overallStatus": "NOT_READY", "recommendations": ["Fix authentication system issues before deployment", "Resolve API connection issues - check Supabase configuration", "Configure all required environment variables", "Consider configuring optional integrations (email, payments, notifications) for full functionality"]}