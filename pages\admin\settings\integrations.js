/**
 * Integration Settings Page for Ocean Soul Sparkles Admin
 * Manages third-party integrations and calendar sync
 * 
 * Phase 7: Advanced Integrations & Ecosystem
 */

import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import { toast } from 'react-toastify'
import AdminLayout from '@/components/admin/AdminLayout'
import CalendarIntegration from '@/components/admin/integrations/CalendarIntegration'
import SocialMediaDashboard from '@/components/admin/integrations/SocialMediaDashboard'
import BusinessDashboard from '@/components/admin/integrations/BusinessDashboard'
import { authenticatedFetch } from '@/lib/auth-utils'
import { useMobileOptimization } from '@/lib/hooks/useMobileOptimization'
import styles from '@/styles/admin/Settings.module.css'

export default function IntegrationsSettings() {
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState(null)
  const [activeTab, setActiveTab] = useState('calendar')

  const {
    isMobile,
    isTablet,
    hapticFeedback,
    shouldReduceAnimations
  } = useMobileOptimization()

  useEffect(() => {
    loadUserData()
    handleUrlParams()
  }, [])

  /**
   * Load user data
   */
  const loadUserData = async () => {
    try {
      const response = await authenticatedFetch('/api/admin/profile')
      if (response.success) {
        setUser(response.user)
      } else {
        toast.error('Failed to load user data')
        router.push('/admin/login')
      }
    } catch (error) {
      console.error('Failed to load user data:', error)
      toast.error('Failed to load user data')
      router.push('/admin/login')
    } finally {
      setLoading(false)
    }
  }

  /**
   * Handle URL parameters for OAuth callbacks and messages
   */
  const handleUrlParams = () => {
    const { success, error, provider, message, action } = router.query

    if (success === 'true' && provider) {
      const providerName = provider.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
      
      if (action === 'connected') {
        toast.success(`${providerName} connected successfully!`)
        hapticFeedback('success')
      } else {
        toast.success(`${providerName} operation completed successfully!`)
      }

      // Clean up URL parameters
      router.replace('/admin/settings/integrations', undefined, { shallow: true })
    }

    if (error && provider) {
      const providerName = provider.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
      const errorMessage = message ? decodeURIComponent(message) : 'An error occurred'
      
      toast.error(`${providerName}: ${errorMessage}`)
      hapticFeedback('error')

      // Clean up URL parameters
      router.replace('/admin/settings/integrations', undefined, { shallow: true })
    }
  }

  if (loading) {
    return (
      <AdminLayout>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Loading integration settings...</p>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className={`${styles.container} ${isMobile ? styles.mobile : ''}`}>
        {/* Header */}
        <div className={styles.header}>
          <h1>Integration Settings</h1>
          <p>Connect and manage third-party integrations for Ocean Soul Sparkles</p>
        </div>

        {/* Navigation Tabs */}
        <div className={styles.tabs}>
          {[
            { id: 'calendar', label: 'Calendar', icon: '📅' },
            { id: 'social', label: 'Social Media', icon: '📱' },
            { id: 'business', label: 'Business Tools', icon: '💼' },
            { id: 'payments', label: 'Payments', icon: '💳', disabled: true }
          ].map(tab => (
            <button
              key={tab.id}
              className={`${styles.tab} ${activeTab === tab.id ? styles.active : ''} ${tab.disabled ? styles.disabled : ''}`}
              onClick={() => {
                if (!tab.disabled) {
                  setActiveTab(tab.id)
                  hapticFeedback('light')
                }
              }}
              disabled={tab.disabled}
            >
              <span className={styles.tabIcon}>{tab.icon}</span>
              <span className={styles.tabLabel}>{tab.label}</span>
              {tab.comingSoon && <span className={styles.comingSoon}>Coming Soon</span>}
              {tab.disabled && <span className={styles.disabled}>Square Only</span>}
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div className={styles.tabContent}>
          {activeTab === 'calendar' && (
            <CalendarIntegration userId={user?.id} />
          )}

          {activeTab === 'social' && (
            <SocialMediaDashboard userId={user?.id} />
          )}

          {activeTab === 'business' && (
            <BusinessDashboard userId={user?.id} />
          )}

          {activeTab === 'payments' && (
            <div className={styles.paymentContent}>
              <div className={styles.paymentCard}>
                <h3>💳 Payment Integration</h3>
                <p>Ocean Soul Sparkles uses Square for secure payment processing.</p>
                
                <div className={styles.squareInfo}>
                  <div className={styles.squareStatus}>
                    <span className={styles.statusIndicator}>✅</span>
                    <span>Square Payment System Active</span>
                  </div>
                  
                  <div className={styles.squareFeatures}>
                    <h4>Current Features:</h4>
                    <ul>
                      <li>💳 Secure card processing</li>
                      <li>📱 Mobile POS terminal</li>
                      <li>🧾 Automated receipts</li>
                      <li>📊 Payment analytics</li>
                      <li>🔒 PCI compliance</li>
                    </ul>
                  </div>

                  <div className={styles.squareNote}>
                    <p><strong>Note:</strong> Square integration is fully configured and cannot be modified. For payment issues, contact support.</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Integration Status Summary */}
        <div className={styles.statusSummary}>
          <h3>Integration Status</h3>
          <div className={styles.statusGrid}>
            <div className={styles.statusCard}>
              <span className={styles.statusIcon}>📅</span>
              <div className={styles.statusInfo}>
                <h4>Calendar</h4>
                <p className={styles.statusActive}>Available</p>
              </div>
            </div>
            
            <div className={styles.statusCard}>
              <span className={styles.statusIcon}>📱</span>
              <div className={styles.statusInfo}>
                <h4>Social Media</h4>
                <p className={styles.statusActive}>Available</p>
              </div>
            </div>
            
            <div className={styles.statusCard}>
              <span className={styles.statusIcon}>💼</span>
              <div className={styles.statusInfo}>
                <h4>Business Tools</h4>
                <p className={styles.statusActive}>Available</p>
              </div>
            </div>
            
            <div className={styles.statusCard}>
              <span className={styles.statusIcon}>💳</span>
              <div className={styles.statusInfo}>
                <h4>Payments</h4>
                <p className={styles.statusActive}>Square Active</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}
