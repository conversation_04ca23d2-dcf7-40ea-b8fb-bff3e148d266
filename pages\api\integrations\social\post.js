/**
 * Social Media Post Creation API Endpoint for Ocean Soul Sparkles
 * Handles creation of posts across multiple social media platforms
 * 
 * Phase 7.3: Social Media Integration Layer
 */

import { authenticateAdminRequest } from '@/lib/admin-auth'
import { integrationRateLimit } from '@/lib/integrations/rate-limiter'
import { RequestValidator, AuditLogger } from '@/lib/integrations/security-utils'
import SocialMediaManager from '@/lib/integrations/social/social-manager'

/**
 * Social Media Post Creation Handler
 * POST /api/integrations/social/post - Create new post on social media platforms
 */
export default async function handler(req, res) {
  // Apply rate limiting
  await new Promise((resolve, reject) => {
    integrationRateLimit(req, res, (error) => {
      if (error) reject(error)
      else resolve()
    })
  })

  const startTime = Date.now()

  try {
    // Validate request method
    RequestValidator.validateEndpointAccess(req, ['POST'])

    // Authenticate user
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.success) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      })
    }

    const { user } = authResult
    const userId = user.id

    const { providers, postData } = req.body

    if (!providers || !Array.isArray(providers) || providers.length === 0) {
      return res.status(400).json({
        error: 'Missing providers',
        message: 'At least one provider must be specified'
      })
    }

    if (!postData) {
      return res.status(400).json({
        error: 'Missing post data',
        message: 'Post data is required'
      })
    }

    // Validate post data
    const validationResult = validatePostData(postData)
    if (!validationResult.valid) {
      return res.status(400).json({
        error: 'Invalid post data',
        message: validationResult.message
      })
    }

    // Initialize social media manager
    const socialManager = new SocialMediaManager(userId)

    // Create post on multiple providers
    const results = await socialManager.createMultiProviderPost(providers, postData)

    const successCount = results.filter(r => r.success).length
    const totalCount = results.length

    await AuditLogger.logIntegrationActivity(
      userId,
      'social_media',
      'multi_post_created',
      successCount === totalCount ? 'success' : 'warning',
      {
        providers,
        successCount,
        totalCount,
        hasImage: !!postData.imageUrl,
        isScheduled: !!postData.scheduledTime,
        messageLength: postData.message?.length || 0
      }
    )

    await AuditLogger.logApiAccess(
      userId,
      req.url,
      req.method,
      200,
      Date.now() - startTime,
      { 
        action: 'create_multi_post',
        providers,
        successCount,
        totalCount
      }
    )

    return res.status(200).json({
      success: true,
      message: `Post created on ${successCount} of ${totalCount} platform(s)`,
      results,
      summary: {
        total: totalCount,
        successful: successCount,
        failed: totalCount - successCount
      }
    })

  } catch (error) {
    console.error('Social media post creation error:', error)

    await AuditLogger.logSecurityEvent(
      req.user?.id || null,
      'social_post_creation_error',
      {
        error: error.message,
        stack: error.stack,
        endpoint: req.url,
        method: req.method,
        userAgent: req.headers['user-agent'],
        ipAddress: req.headers['x-forwarded-for'] || req.connection?.remoteAddress
      },
      'error'
    )

    await AuditLogger.logApiAccess(
      req.user?.id || null,
      req.url,
      req.method,
      500,
      Date.now() - startTime,
      { error: error.message }
    )

    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to create social media post'
    })
  }
}

/**
 * Validate post data
 */
function validatePostData(postData) {
  // Check message
  if (!postData.message || typeof postData.message !== 'string') {
    return {
      valid: false,
      message: 'Post message is required and must be a string'
    }
  }

  const trimmedMessage = postData.message.trim()
  if (trimmedMessage.length === 0) {
    return {
      valid: false,
      message: 'Post message cannot be empty'
    }
  }

  if (trimmedMessage.length > 2200) {
    return {
      valid: false,
      message: 'Post message must be 2200 characters or less'
    }
  }

  // Validate image URL if provided
  if (postData.imageUrl) {
    if (typeof postData.imageUrl !== 'string') {
      return {
        valid: false,
        message: 'Image URL must be a string'
      }
    }

    try {
      const url = new URL(postData.imageUrl)
      if (!['http:', 'https:'].includes(url.protocol)) {
        return {
          valid: false,
          message: 'Image URL must use HTTP or HTTPS protocol'
        }
      }
    } catch {
      return {
        valid: false,
        message: 'Invalid image URL format'
      }
    }
  }

  // Validate scheduled time if provided
  if (postData.scheduledTime) {
    if (typeof postData.scheduledTime !== 'string') {
      return {
        valid: false,
        message: 'Scheduled time must be a string'
      }
    }

    const scheduledDate = new Date(postData.scheduledTime)
    if (isNaN(scheduledDate.getTime())) {
      return {
        valid: false,
        message: 'Invalid scheduled time format'
      }
    }

    if (scheduledDate <= new Date()) {
      return {
        valid: false,
        message: 'Scheduled time must be in the future'
      }
    }

    // Check if scheduled time is not too far in the future (1 year max)
    const oneYearFromNow = new Date()
    oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1)
    
    if (scheduledDate > oneYearFromNow) {
      return {
        valid: false,
        message: 'Scheduled time cannot be more than 1 year in the future'
      }
    }
  }

  // Validate hashtags if provided
  if (postData.hashtags) {
    if (!Array.isArray(postData.hashtags)) {
      return {
        valid: false,
        message: 'Hashtags must be an array'
      }
    }

    if (postData.hashtags.length > 30) {
      return {
        valid: false,
        message: 'Maximum 30 hashtags allowed'
      }
    }

    for (const hashtag of postData.hashtags) {
      if (typeof hashtag !== 'string') {
        return {
          valid: false,
          message: 'All hashtags must be strings'
        }
      }

      if (hashtag.length > 100) {
        return {
          valid: false,
          message: 'Hashtags must be 100 characters or less'
        }
      }
    }
  }

  return { valid: true }
}

/**
 * API Route Configuration
 */
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
}
