/* Upcoming Bookings Styles - Phase 8: Advanced Customer Experience */

.upcomingBookings {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  padding: 1rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #333;
  margin: 0;
}

.count {
  font-size: 0.9rem;
  color: #666;
  background: #f8f9fa;
  padding: 0.5rem 1rem;
  border-radius: 20px;
}

/* Empty State */
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.emptyIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.emptyTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

.emptyDescription {
  color: #666;
  margin-bottom: 2rem;
  line-height: 1.5;
}

.bookNowButton {
  background: #4ECDC4;
  color: white;
  text-decoration: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.bookNowButton:hover {
  background: #44A08D;
  transform: translateY(-1px);
}

/* Bookings List */
.bookingsList {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.bookingCard {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border-left: 4px solid #4ECDC4;
}

.bookingCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* Booking Header */
.bookingHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f0f0f0;
}

.serviceInfo {
  flex: 1;
}

.serviceName {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.statusBadge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.timeUntil {
  background: rgba(78, 205, 196, 0.1);
  color: #4ECDC4;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  white-space: nowrap;
}

/* Booking Details */
.bookingDetails {
  margin-bottom: 1.5rem;
}

.detailRow {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f8f9fa;
}

.detailRow:last-child {
  border-bottom: none;
}

.detailIcon {
  font-size: 1rem;
  color: #4ECDC4;
  width: 20px;
  text-align: center;
}

.detailLabel {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
  min-width: 80px;
}

.detailValue {
  font-size: 0.9rem;
  color: #333;
  font-weight: 600;
  flex: 1;
}

/* Booking Actions */
.bookingActions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.viewButton,
.rescheduleButton,
.modifyButton,
.cancelButton {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  text-align: center;
  transition: all 0.3s ease;
  flex: 1;
  min-width: 100px;
}

.viewButton {
  background: #f8f9fa;
  color: #666;
  border: 1px solid #e9ecef;
}

.viewButton:hover {
  background: #e9ecef;
  color: #333;
}

.rescheduleButton {
  background: #ffc107;
  color: #212529;
}

.rescheduleButton:hover {
  background: #e0a800;
  transform: translateY(-1px);
}

.modifyButton {
  background: #17a2b8;
  color: white;
}

.modifyButton:hover {
  background: #138496;
  transform: translateY(-1px);
}

.cancelButton {
  background: #dc3545;
  color: white;
}

.cancelButton:hover {
  background: #c82333;
  transform: translateY(-1px);
}

.viewButton:disabled,
.rescheduleButton:disabled,
.modifyButton:disabled,
.cancelButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Quick Actions */
.quickActions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 2px solid #f0f0f0;
}

.quickActionButton {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  text-decoration: none;
  color: #666;
  transition: all 0.3s ease;
  min-width: 120px;
}

.quickActionButton:hover {
  border-color: #4ECDC4;
  color: #4ECDC4;
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(78, 205, 196, 0.2);
}

.quickActionIcon {
  font-size: 1.5rem;
}

.quickActionLabel {
  font-size: 0.9rem;
  font-weight: 500;
  text-align: center;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .upcomingBookings {
    padding: 0.5rem;
  }

  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .title {
    font-size: 1.5rem;
  }

  .bookingCard {
    padding: 1rem;
  }

  .bookingHeader {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .timeUntil {
    align-self: flex-start;
  }

  .bookingActions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .viewButton,
  .rescheduleButton,
  .modifyButton,
  .cancelButton {
    flex: none;
    width: 100%;
  }

  .quickActions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .quickActionButton {
    flex-direction: row;
    justify-content: flex-start;
    text-align: left;
    min-width: auto;
  }

  .detailLabel {
    min-width: 70px;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 1.3rem;
  }

  .bookingCard {
    padding: 0.75rem;
  }

  .serviceName {
    font-size: 1.1rem;
  }

  .detailRow {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .detailLabel {
    min-width: auto;
    font-weight: 600;
  }

  .quickActionButton {
    padding: 0.75rem;
  }

  .quickActionIcon {
    font-size: 1.2rem;
  }

  .quickActionLabel {
    font-size: 0.8rem;
  }

  .emptyState {
    padding: 2rem 1rem;
  }

  .emptyIcon {
    font-size: 3rem;
  }

  .emptyTitle {
    font-size: 1.3rem;
  }
}

/* Status Badge Colors */
.statusBadge[style*="background-color: #28a745"] {
  background-color: #28a745 !important; /* confirmed */
}

.statusBadge[style*="background-color: #ffc107"] {
  background-color: #ffc107 !important; /* pending */
  color: #212529 !important;
}

.statusBadge[style*="background-color: #17a2b8"] {
  background-color: #17a2b8 !important; /* in_progress */
}

.statusBadge[style*="background-color: #6c757d"] {
  background-color: #6c757d !important; /* completed */
}

.statusBadge[style*="background-color: #dc3545"] {
  background-color: #dc3545 !important; /* cancelled */
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .bookingCard {
    background: #2d2d2d;
    border-left-color: #4ECDC4;
  }

  .title,
  .serviceName {
    color: #fff;
  }

  .count {
    background: #3d3d3d;
    color: #ccc;
  }

  .emptyTitle {
    color: #fff;
  }

  .emptyDescription {
    color: #ccc;
  }

  .detailLabel {
    color: #aaa;
  }

  .detailValue {
    color: #fff;
  }

  .bookingDetails {
    border-bottom-color: #444;
  }

  .detailRow {
    border-bottom-color: #444;
  }

  .viewButton {
    background: #3d3d3d;
    color: #ccc;
    border-color: #444;
  }

  .viewButton:hover {
    background: #444;
    color: #fff;
  }

  .quickActionButton {
    background: #2d2d2d;
    border-color: #444;
    color: #ccc;
  }

  .quickActionButton:hover {
    border-color: #4ECDC4;
    color: #4ECDC4;
  }

  .quickActions {
    border-top-color: #444;
  }
}
