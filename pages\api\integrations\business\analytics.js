/**
 * Business Analytics API Endpoint for Ocean Soul Sparkles
 * Provides marketing analytics from business integrations
 * 
 * Phase 7.4: Business Management Integrations
 */

import { authenticateAdminRequest } from '@/lib/admin-auth'
import { integrationRateLimit } from '@/lib/integrations/rate-limiter'
import { RequestValidator, AuditLogger } from '@/lib/integrations/security-utils'
import BusinessManager from '@/lib/integrations/business-manager'

/**
 * Business Analytics Handler
 * GET /api/integrations/business/analytics - Get marketing analytics
 */
export default async function handler(req, res) {
  // Apply rate limiting
  await new Promise((resolve, reject) => {
    integrationRateLimit(req, res, (error) => {
      if (error) reject(error)
      else resolve()
    })
  })

  const startTime = Date.now()

  try {
    // Validate request method
    RequestValidator.validateEndpointAccess(req, ['GET'])

    // Authenticate user
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.success) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      })
    }

    const { user } = authResult
    const userId = user.id

    // Get query parameters
    const { provider } = req.query

    // Initialize business manager
    const businessManager = new BusinessManager(userId)

    let analytics
    if (provider) {
      // Get analytics for specific provider
      try {
        const client = await businessManager.getClient(provider)
        
        if (provider === 'mailchimp') {
          // Get Mailchimp specific analytics
          const [campaigns, account] = await Promise.all([
            client.getCampaigns(20, 0, 'sent'),
            client.getAccount()
          ])

          const campaignReports = []
          for (const campaign of campaigns.campaigns.slice(0, 10)) {
            try {
              const report = await client.getCampaignReports(campaign.id)
              campaignReports.push(report)
            } catch (error) {
              console.error(`Failed to get campaign report for ${campaign.id}:`, error)
            }
          }

          analytics = {
            [provider]: {
              provider,
              providerName: 'Mailchimp',
              account,
              campaigns: campaigns.campaigns,
              campaignReports,
              totalCampaigns: campaigns.totalItems
            }
          }
        }
        // Add other provider-specific analytics here
        
      } catch (error) {
        analytics = {
          [provider]: {
            provider,
            providerName: getProviderName(provider),
            error: error.message
          }
        }
      }
    } else {
      // Get analytics from all connected marketing providers
      analytics = await businessManager.getMarketingAnalytics()
    }

    // Calculate summary statistics
    const summary = calculateAnalyticsSummary(analytics)

    await AuditLogger.logApiAccess(
      userId,
      req.url,
      req.method,
      200,
      Date.now() - startTime,
      { 
        action: 'get_business_analytics',
        provider,
        providerCount: Object.keys(analytics).length
      }
    )

    return res.status(200).json({
      success: true,
      analytics,
      summary
    })

  } catch (error) {
    console.error('Business analytics error:', error)

    await AuditLogger.logApiAccess(
      req.user?.id || null,
      req.url,
      req.method,
      500,
      Date.now() - startTime,
      { error: error.message }
    )

    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to get business analytics'
    })
  }
}

/**
 * Get provider display name
 */
function getProviderName(provider) {
  const names = {
    mailchimp: 'Mailchimp',
    constant_contact: 'Constant Contact'
  }
  return names[provider] || provider
}

/**
 * Calculate summary statistics across all marketing providers
 */
function calculateAnalyticsSummary(analytics) {
  const summary = {
    totalCampaigns: 0,
    totalEmailsSent: 0,
    totalOpens: 0,
    totalClicks: 0,
    averageOpenRate: 0,
    averageClickRate: 0,
    totalSubscribers: 0,
    providerCount: 0,
    errorCount: 0,
    topPerformingCampaigns: [],
    recentActivity: []
  }

  let totalOpenRate = 0
  let totalClickRate = 0
  let campaignCount = 0
  const allCampaigns = []

  for (const [provider, data] of Object.entries(analytics)) {
    if (data.error) {
      summary.errorCount++
      continue
    }

    summary.providerCount++

    // Process Mailchimp data
    if (provider === 'mailchimp' && data.campaigns) {
      summary.totalCampaigns += data.campaigns.length

      // Process campaign reports
      if (data.campaignReports) {
        for (const report of data.campaignReports) {
          if (report.emails_sent) {
            summary.totalEmailsSent += report.emails_sent
          }
          
          if (report.opens) {
            summary.totalOpens += report.opens.opens
            totalOpenRate += (report.opens.open_rate || 0)
            campaignCount++
          }
          
          if (report.clicks) {
            summary.totalClicks += report.clicks.clicks
            totalClickRate += (report.clicks.click_rate || 0)
          }

          // Add to all campaigns for top performers
          allCampaigns.push({
            id: report.campaign_id,
            title: report.campaign_title,
            provider,
            emailsSent: report.emails_sent || 0,
            opens: report.opens?.opens || 0,
            clicks: report.clicks?.clicks || 0,
            openRate: report.opens?.open_rate || 0,
            clickRate: report.clicks?.click_rate || 0,
            sendTime: report.send_time
          })
        }
      }

      // Get subscriber count from account info
      if (data.account && data.account.total_subscribers) {
        summary.totalSubscribers += data.account.total_subscribers
      }
    }

    // Add other provider processing here
  }

  // Calculate averages
  if (campaignCount > 0) {
    summary.averageOpenRate = totalOpenRate / campaignCount
    summary.averageClickRate = totalClickRate / campaignCount
  }

  // Get top performing campaigns (by open rate)
  summary.topPerformingCampaigns = allCampaigns
    .sort((a, b) => b.openRate - a.openRate)
    .slice(0, 5)
    .map(campaign => ({
      title: campaign.title,
      provider: campaign.provider,
      openRate: campaign.openRate,
      clickRate: campaign.clickRate,
      emailsSent: campaign.emailsSent
    }))

  // Get recent activity (last 10 campaigns by send time)
  summary.recentActivity = allCampaigns
    .filter(campaign => campaign.sendTime)
    .sort((a, b) => new Date(b.sendTime) - new Date(a.sendTime))
    .slice(0, 10)
    .map(campaign => ({
      title: campaign.title,
      provider: campaign.provider,
      sendTime: campaign.sendTime,
      emailsSent: campaign.emailsSent,
      opens: campaign.opens,
      clicks: campaign.clicks
    }))

  return summary
}

/**
 * API Route Configuration
 */
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
}
