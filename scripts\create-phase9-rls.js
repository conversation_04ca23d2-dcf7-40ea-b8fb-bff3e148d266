/**
 * Create Phase 9.1 Row Level Security Policies
 * Ocean Soul Sparkles - Security & Compliance Enhancement
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'
import path from 'path'
import { fileURLToPath } from 'url'

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Load environment variables
dotenv.config({ path: path.join(__dirname, '..', '.env.local') })

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

async function createRLSPolicies() {
  console.log('🔐 Creating Phase 9.1 Row Level Security Policies...')
  
  const policies = [
    // Enable RLS on all tables
    {
      name: 'Enable RLS on user_mfa_settings',
      sql: 'ALTER TABLE public.user_mfa_settings ENABLE ROW LEVEL SECURITY;'
    },
    {
      name: 'Enable RLS on mfa_verification_logs',
      sql: 'ALTER TABLE public.mfa_verification_logs ENABLE ROW LEVEL SECURITY;'
    },
    {
      name: 'Enable RLS on webauthn_credentials',
      sql: 'ALTER TABLE public.webauthn_credentials ENABLE ROW LEVEL SECURITY;'
    },
    {
      name: 'Enable RLS on biometric_auth_logs',
      sql: 'ALTER TABLE public.biometric_auth_logs ENABLE ROW LEVEL SECURITY;'
    },
    {
      name: 'Enable RLS on user_sessions',
      sql: 'ALTER TABLE public.user_sessions ENABLE ROW LEVEL SECURITY;'
    },
    {
      name: 'Enable RLS on device_fingerprints',
      sql: 'ALTER TABLE public.device_fingerprints ENABLE ROW LEVEL SECURITY;'
    },
    {
      name: 'Enable RLS on security_events',
      sql: 'ALTER TABLE public.security_events ENABLE ROW LEVEL SECURITY;'
    },
    {
      name: 'Enable RLS on social_login_accounts',
      sql: 'ALTER TABLE public.social_login_accounts ENABLE ROW LEVEL SECURITY;'
    },

    // MFA settings policies
    {
      name: 'Users can manage their own MFA settings',
      sql: `
        CREATE POLICY "Users can manage their own MFA settings" ON public.user_mfa_settings
        FOR ALL USING (user_id = auth.uid());
      `
    },
    {
      name: 'Admin can view all MFA settings',
      sql: `
        CREATE POLICY "Admin can view all MFA settings" ON public.user_mfa_settings
        FOR SELECT USING (
          auth.role() IN ('service_role', 'supabase_admin') OR
          EXISTS (
            SELECT 1 FROM public.user_roles ur
            WHERE ur.id = auth.uid() AND ur.role IN ('dev', 'admin')
          )
        );
      `
    },

    // MFA verification logs policies
    {
      name: 'Users can view their own MFA logs',
      sql: `
        CREATE POLICY "Users can view their own MFA logs" ON public.mfa_verification_logs
        FOR SELECT USING (user_id = auth.uid());
      `
    },
    {
      name: 'Admin can view all MFA logs',
      sql: `
        CREATE POLICY "Admin can view all MFA logs" ON public.mfa_verification_logs
        FOR SELECT USING (
          auth.role() IN ('service_role', 'supabase_admin') OR
          EXISTS (
            SELECT 1 FROM public.user_roles ur
            WHERE ur.id = auth.uid() AND ur.role IN ('dev', 'admin')
          )
        );
      `
    },

    // WebAuthn credentials policies
    {
      name: 'Users can manage their own WebAuthn credentials',
      sql: `
        CREATE POLICY "Users can manage their own WebAuthn credentials" ON public.webauthn_credentials
        FOR ALL USING (user_id = auth.uid());
      `
    },

    // Biometric auth logs policies
    {
      name: 'Users can view their own biometric logs',
      sql: `
        CREATE POLICY "Users can view their own biometric logs" ON public.biometric_auth_logs
        FOR SELECT USING (user_id = auth.uid());
      `
    },
    {
      name: 'Admin can view all biometric logs',
      sql: `
        CREATE POLICY "Admin can view all biometric logs" ON public.biometric_auth_logs
        FOR SELECT USING (
          auth.role() IN ('service_role', 'supabase_admin') OR
          EXISTS (
            SELECT 1 FROM public.user_roles ur
            WHERE ur.id = auth.uid() AND ur.role IN ('dev', 'admin')
          )
        );
      `
    },

    // User sessions policies
    {
      name: 'Users can view their own sessions',
      sql: `
        CREATE POLICY "Users can view their own sessions" ON public.user_sessions
        FOR SELECT USING (user_id = auth.uid());
      `
    },
    {
      name: 'Users can update their own sessions',
      sql: `
        CREATE POLICY "Users can update their own sessions" ON public.user_sessions
        FOR UPDATE USING (user_id = auth.uid());
      `
    },
    {
      name: 'Admin can view all sessions',
      sql: `
        CREATE POLICY "Admin can view all sessions" ON public.user_sessions
        FOR SELECT USING (
          auth.role() IN ('service_role', 'supabase_admin') OR
          EXISTS (
            SELECT 1 FROM public.user_roles ur
            WHERE ur.id = auth.uid() AND ur.role IN ('dev', 'admin')
          )
        );
      `
    },

    // Device fingerprints policies
    {
      name: 'Users can view their own device fingerprints',
      sql: `
        CREATE POLICY "Users can view their own device fingerprints" ON public.device_fingerprints
        FOR SELECT USING (user_id = auth.uid());
      `
    },
    {
      name: 'Users can update their own device fingerprints',
      sql: `
        CREATE POLICY "Users can update their own device fingerprints" ON public.device_fingerprints
        FOR UPDATE USING (user_id = auth.uid());
      `
    },

    // Security events policies
    {
      name: 'Users can view their own security events',
      sql: `
        CREATE POLICY "Users can view their own security events" ON public.security_events
        FOR SELECT USING (user_id = auth.uid());
      `
    },
    {
      name: 'Admin can manage all security events',
      sql: `
        CREATE POLICY "Admin can manage all security events" ON public.security_events
        FOR ALL USING (
          auth.role() IN ('service_role', 'supabase_admin') OR
          EXISTS (
            SELECT 1 FROM public.user_roles ur
            WHERE ur.id = auth.uid() AND ur.role IN ('dev', 'admin')
          )
        );
      `
    },

    // Social login accounts policies
    {
      name: 'Users can manage their own social accounts',
      sql: `
        CREATE POLICY "Users can manage their own social accounts" ON public.social_login_accounts
        FOR ALL USING (user_id = auth.uid());
      `
    },
    {
      name: 'Admin can view all social accounts',
      sql: `
        CREATE POLICY "Admin can view all social accounts" ON public.social_login_accounts
        FOR SELECT USING (
          auth.role() IN ('service_role', 'supabase_admin') OR
          EXISTS (
            SELECT 1 FROM public.user_roles ur
            WHERE ur.id = auth.uid() AND ur.role IN ('dev', 'admin')
          )
        );
      `
    }
  ]

  let successCount = 0
  let errorCount = 0

  for (const policy of policies) {
    try {
      console.log(`🔒 Creating policy: ${policy.name}`)
      
      const { error } = await supabase.rpc('execute_sql', {
        sql: policy.sql
      })
      
      if (error) {
        console.error(`❌ Error creating policy "${policy.name}":`, error.message)
        errorCount++
      } else {
        console.log(`✅ Policy "${policy.name}" created successfully`)
        successCount++
      }
    } catch (err) {
      console.error(`❌ Exception creating policy "${policy.name}":`, err.message)
      errorCount++
    }
  }

  console.log('\n📊 RLS Policy Creation Summary:')
  console.log(`✅ Successful policies: ${successCount}`)
  console.log(`❌ Failed policies: ${errorCount}`)

  if (errorCount === 0) {
    console.log('🎉 All Phase 9.1 RLS policies created successfully!')
  } else {
    console.log('⚠️  Some policies failed to create. Please review the logs.')
  }
}

// Run the RLS policy creation
createRLSPolicies()
