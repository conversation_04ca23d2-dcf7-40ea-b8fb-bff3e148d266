/**
 * MFA Verification API Endpoint for Ocean Soul Sparkles
 * Verifies TOTP token and enables MFA for user
 * 
 * Phase 9.1: Enhanced Authentication System
 */

import { withAdminAuth } from '@/lib/admin-auth'
import { mfaManager } from '@/lib/security/mfa-manager'

async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { token } = req.body
    const userId = req.user.id

    if (!token || token.length !== 6) {
      return res.status(400).json({ 
        error: 'Invalid token',
        message: 'Please provide a valid 6-digit verification code'
      })
    }

    // Verify TOTP token and enable MFA
    const result = await mfaManager.verifyAndEnableMFA(userId, token)

    if (!result.success) {
      return res.status(400).json({
        error: 'Verification failed',
        message: result.error
      })
    }

    res.status(200).json({
      success: true,
      message: 'MFA enabled successfully',
      backupCodes: result.backupCodes
    })

  } catch (error) {
    console.error('MFA verification error:', error)
    res.status(500).json({
      error: 'MFA verification failed',
      message: error.message
    })
  }
}

export default withAdminAuth(handler)
