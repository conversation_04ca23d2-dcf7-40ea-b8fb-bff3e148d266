/* Artist & Braider Dashboard Styles */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.headerContent h1 {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.headerContent p {
  margin: 0;
  color: #6b7280;
  font-size: 1.125rem;
}

.headerActions {
  display: flex;
  gap: 12px;
}

.refreshButton {
  padding: 12px 20px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  color: #374151;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.refreshButton:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
  transform: translateY(-1px);
}

.signOutButton {
  padding: 12px 20px;
  background: #dc2626;
  border: 1px solid #dc2626;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.signOutButton:hover {
  background: #b91c1c;
  border-color: #b91c1c;
  transform: translateY(-1px);
}

/* Phase 6: AI Features Styles */
.aiToggleButton {
  padding: 12px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: 1px solid #667eea;
  border-radius: 8px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.aiToggleButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.aiToggleButton.active {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-color: #10b981;
  animation: aiPulse 2s infinite;
}

@keyframes aiPulse {
  0%, 100% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.4); }
  50% { box-shadow: 0 0 0 10px rgba(16, 185, 129, 0); }
}

.welcomeBanner {
  margin-top: 12px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 8px;
  color: white;
  font-weight: 500;
}

.welcomeBanner p {
  margin: 0;
  color: white !important;
}

/* Loading States */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  color: #6b7280;
}

.spinner {
  width: 48px;
  height: 48px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error States */
.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.errorIcon {
  font-size: 4rem;
  margin-bottom: 16px;
}

.errorContainer h2 {
  margin: 0 0 12px 0;
  color: #dc2626;
  font-size: 1.5rem;
  font-weight: 600;
}

.errorContainer p {
  margin: 0 0 24px 0;
  color: #6b7280;
  font-size: 1.125rem;
  max-width: 500px;
}

.retryButton {
  padding: 12px 24px;
  background: #3b82f6;
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retryButton:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* Card Base Styles */
.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 24px;
  border: 1px solid #e5e7eb;
}

.card h3 {
  margin: 0 0 20px 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

/* Dashboard Grid */
.dashboardGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto auto auto;
  gap: 24px;
  margin-bottom: 32px;
}

.quickActionsSection {
  grid-column: 1;
  grid-row: 1;
}

.availabilitySection {
  grid-column: 2;
  grid-row: 1;
}

.bookingsSection {
  grid-column: 1 / -1;
  grid-row: 2;
}

.metricsSection {
  grid-column: 1 / -1;
  grid-row: 3;
}

/* Phase 6: AI Sections */
.aiSchedulingSection {
  grid-column: 1 / -1;
  grid-row: 4;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 2px solid #667eea;
  position: relative;
  overflow: hidden;
}

.aiSchedulingSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2, #10b981);
  animation: aiGradientShift 3s ease-in-out infinite;
}

@keyframes aiGradientShift {
  0%, 100% { transform: translateX(-100%); }
  50% { transform: translateX(100%); }
}

.aiInsightsSection {
  grid-column: 1 / -1;
  grid-row: 5;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 2px solid #0ea5e9;
  transition: all 0.3s ease;
}

.aiInsightsSection.expanded {
  min-height: 600px;
}

.aiSectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid rgba(102, 126, 234, 0.1);
}

.aiSectionHeader h3 {
  margin: 0;
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.aiControls {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.dateInput {
  padding: 0.5rem 0.75rem;
  border: 2px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  color: #475569;
  transition: border-color 0.2s ease;
}

.dateInput:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.expandButton,
.toggleAIButton {
  padding: 0.5rem 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: white;
  color: #475569;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;
}

.expandButton:hover,
.toggleAIButton:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-1px);
}

.toggleAIButton {
  color: #dc2626;
  border-color: #fecaca;
  background: #fef2f2;
}

.toggleAIButton:hover {
  background: #fee2e2;
  border-color: #fca5a5;
}

.aiPreview {
  text-align: center;
  padding: 2rem;
  color: #64748b;
}

.aiPreview p {
  margin: 0 0 1rem 0;
  font-size: 1rem;
}

.aiFeatures {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.aiFeatures span {
  padding: 0.5rem 1rem;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  color: #667eea;
}

.aiMobile {
  padding: 1rem;
  border-radius: 8px;
}

/* Profile Summary */
.profileSummary {
  margin-bottom: 32px;
}

.summaryCard {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 24px;
  border: 1px solid #e5e7eb;
}

.summaryCard h3 {
  margin: 0 0 20px 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.summaryGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.summaryItem {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.summaryLabel {
  font-size: 0.875rem;
  color: #6b7280;
  text-transform: uppercase;
  font-weight: 500;
  letter-spacing: 0.05em;
}

.summaryValue {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
}

.completeProfileLink {
  color: #f59e0b;
  text-decoration: underline;
  font-weight: 600;
  transition: color 0.2s ease;
}

.completeProfileLink:hover {
  color: #d97706;
}

/* Help Section */
.helpSection {
  margin-bottom: 32px;
}

.helpCard {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 24px;
  border: 1px solid #e5e7eb;
}

.helpCard h3 {
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.helpLinks {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.helpLink {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  color: #374151;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
}

.helpLink:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* AI Status Card */
.aiStatusCard {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 1.5rem;
  margin-top: 1rem;
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.2);
}

.aiStatusCard h4 {
  margin: 0 0 1rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.aiStatusGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.aiStatusItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.aiStatusIcon {
  font-size: 1.25rem;
}

.aiStatusText {
  font-size: 0.875rem;
  font-weight: 500;
}

.aiStatusNote {
  margin: 0;
  font-size: 0.875rem;
  opacity: 0.9;
  line-height: 1.5;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboardGrid {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto auto auto auto;
  }

  .quickActionsSection {
    grid-column: 1;
    grid-row: 1;
  }

  .availabilitySection {
    grid-column: 1;
    grid-row: 2;
  }

  .bookingsSection {
    grid-column: 1;
    grid-row: 3;
  }

  .metricsSection {
    grid-column: 1;
    grid-row: 4;
  }

  .aiSchedulingSection {
    grid-column: 1;
    grid-row: 5;
  }

  .aiInsightsSection {
    grid-column: 1;
    grid-row: 6;
  }

  /* AI Mobile Optimizations */
  .aiSectionHeader {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .aiControls {
    justify-content: space-between;
  }

  .aiStatusGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    padding: 20px;
  }

  .headerContent h1 {
    font-size: 1.75rem;
  }

  .headerContent p {
    font-size: 1rem;
  }

  .dashboardGrid {
    gap: 16px;
    margin-bottom: 24px;
  }

  .summaryGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .helpLinks {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  /* AI Mobile Styles */
  .aiFeatures {
    flex-direction: column;
    align-items: center;
  }

  .aiStatusGrid {
    grid-template-columns: 1fr;
  }

  .aiControls {
    flex-direction: column;
    gap: 0.5rem;
  }

  .dateInput {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .summaryGrid {
    grid-template-columns: 1fr;
  }

  .headerActions {
    width: 100%;
    flex-direction: column;
    gap: 8px;
  }

  .refreshButton,
  .aiToggleButton {
    flex: 1;
    justify-content: center;
    width: 100%;
  }

  /* AI Mobile Optimizations for Small Screens */
  .aiSectionHeader h3 {
    font-size: 1rem;
  }

  .aiPreview {
    padding: 1rem;
  }

  .aiStatusCard {
    padding: 1rem;
  }

  .expandButton,
  .toggleAIButton {
    padding: 0.5rem;
    font-size: 0.75rem;
  }
}

/* Bookings List */
.bookingsList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.bookingCard {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f7fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.bookingDate {
  font-size: 0.75rem;
  font-weight: 600;
  color: #667eea;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  min-width: 60px;
}

.bookingDetails {
  flex: 1;
}

.bookingDetails h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 0.25rem 0;
}

.bookingDetails p {
  font-size: 0.75rem;
  color: #4a5568;
  margin: 0;
}

.bookingTime {
  font-size: 0.75rem;
  color: #667eea;
  font-weight: 500;
}

.bookingStatus {
  min-width: 80px;
  text-align: right;
}

.statusBadge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.statusBadge.confirmed {
  background: #c6f6d5;
  color: #22543d;
}

.statusBadge.pending {
  background: #fef5e7;
  color: #c05621;
}

.statusBadge.completed {
  background: #bee3f8;
  color: #2a4365;
}

/* Payments List */
.paymentsList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.paymentCard {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f7fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.paymentDate {
  font-size: 0.75rem;
  font-weight: 600;
  color: #667eea;
  min-width: 80px;
}

.paymentDetails {
  flex: 1;
}

.paymentDetails h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 0.25rem 0;
}

.paymentDetails p {
  font-size: 0.75rem;
  color: #4a5568;
  margin: 0;
}

.paymentAmount {
  text-align: right;
  min-width: 100px;
}

.amount {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: #22543d;
}

.commission {
  display: block;
  font-size: 0.75rem;
  color: #4a5568;
}

/* Empty State */
.emptyState {
  text-align: center;
  padding: 2rem;
  color: #4a5568;
}

.emptyState p {
  margin: 0;
  font-style: italic;
}

/* Quick Actions */
.quickActions {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.quickActions h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 1rem 0;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #f7fafc;
}

.actionButtons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.actionButton {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  color: #1a202c;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.actionButton:hover {
  background: #667eea;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.actionIcon {
  font-size: 1.25rem;
}

/* Card Component Styles */
.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 24px;
  margin-bottom: 24px;
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.cardHeader h3 {
  margin: 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
}

/* EarningsCard Styles */
.timeframeSelect {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
}

.earningsOverview {
  margin-bottom: 24px;
}

.mainEarning {
  text-align: center;
  margin-bottom: 20px;
}

.earningAmount {
  display: block;
  font-size: 32px;
  font-weight: 700;
  color: #059669;
  margin-bottom: 4px;
}

.earningLabel {
  display: block;
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.earningStats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-top: 20px;
}

.statItem {
  text-align: center;
  padding: 12px;
  background: #f9fafb;
  border-radius: 8px;
}

.statValue {
  display: block;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 2px;
}

.statLabel {
  display: block;
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.recentPayments h4 {
  margin: 0 0 12px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.paymentsList {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.paymentItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.paymentInfo {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.paymentService {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
}

.paymentDate {
  font-size: 12px;
  color: #6b7280;
}

.paymentAmount {
  font-size: 14px;
  font-weight: 600;
  color: #059669;
}

/* QuickActionsCard Styles */
.statusIndicators {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.statusIndicator {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 8px;
}

.statusIcon {
  font-size: 16px;
}

.statusInfo {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.statusLabel {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.statusValue {
  font-size: 14px;
  font-weight: 600;
}

.statusAvailable {
  color: #059669;
}

.statusUnavailable {
  color: #dc2626;
}

.statusActive {
  color: #059669;
}

.quickActionsList {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
}

.quickActionButton {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
  font-weight: 500;
}

.quickActionButton:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #3b82f6;
}

.quickActionButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.actionIcon {
  font-size: 16px;
}

.actionLabel {
  flex: 1;
  text-align: left;
}

.primaryAction {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.primaryAction:hover:not(:disabled) {
  background: #2563eb;
  border-color: #2563eb;
}

.secondaryAction {
  background: #f3f4f6;
  color: #374151;
}

.availableAction {
  background: #fef3c7;
  color: #92400e;
  border-color: #fbbf24;
}

.unavailableAction {
  background: #dcfce7;
  color: #166534;
  border-color: #22c55e;
}

.helpSection {
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.helpSection h4 {
  margin: 0 0 12px 0;
  color: #1f2937;
  font-size: 14px;
  font-weight: 600;
}

.helpLinks {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.helpLink {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #3b82f6;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: color 0.2s;
}

.helpLink:hover {
  color: #2563eb;
}

/* Loading and Error States */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 1rem;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading p {
  color: #4a5568;
  font-size: 1rem;
}

.error {
  text-align: center;
  padding: 2rem;
  background: #fed7d7;
  border: 1px solid #feb2b2;
  border-radius: 8px;
  color: #c53030;
}

.error h2 {
  margin: 0 0 0.5rem 0;
  color: #c53030;
}

.error p {
  margin: 0 0 1rem 0;
}

.retryButton {
  padding: 0.5rem 1rem;
  background: #c53030;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.retryButton:hover {
  background: #e53e3e;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard {
    padding: 1rem;
  }

  .contentGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .statsGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .actionButtons {
    grid-template-columns: 1fr;
  }

  .bookingCard,
  .paymentCard {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .bookingStatus,
  .paymentAmount {
    text-align: left;
    min-width: auto;
  }

  .card {
    padding: 16px;
    margin-bottom: 16px;
  }

  .earningStats {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .paymentItem {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .statusIndicators {
    gap: 8px;
  }

  .quickActionsList {
    gap: 6px;
  }

  .quickActionButton {
    padding: 10px 12px;
    font-size: 13px;
  }
}

/* Onboarding Section Styles */
.onboardingSection {
  background-color: #eef2f7; /* Light blue/grey background */
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 25px;
  border: 1px solid #d1d9e6;
}

.onboardingSection h2 {
  margin-top: 0;
  color: #334155; /* Darker text for heading */
  font-size: 1.5rem;
  margin-bottom: 15px;
}

.onboardingList {
  list-style: none;
  padding-left: 0;
}

.onboardingList li {
  margin-bottom: 15px;
  padding-left: 25px;
  position: relative;
}

.onboardingList li::before {
  content: '➡️'; /* Or use an SVG icon */
  position: absolute;
  left: 0;
  top: 2px;
  color: #6e8efb;
}

.onboardingList li p {
  margin: 5px 0 0 0;
  font-size: 0.9rem;
  color: #475569;
}

.onboardingLink {
  font-weight: 600;
  color: #6e8efb;
  text-decoration: none;
  font-size: 1.1rem;
}

.onboardingLink:hover {
  text-decoration: underline;
}

.completeOnboardingButton {
  background-color: #28a745; /* Green */
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  margin-top: 10px;
  transition: background-color 0.2s;
}

.completeOnboardingButton:hover {
  background-color: #218838;
}

.completeOnboardingButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}
