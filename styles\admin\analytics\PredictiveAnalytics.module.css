/* Predictive Analytics Component Styles */

.predictiveAnalytics {
  padding: 20px;
  background: #f8fafc;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header h2 {
  color: #1f2937;
  font-size: 28px;
  font-weight: 700;
  margin: 0;
}

.controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.modelSelect,
.periodSelect,
.confidenceSelect {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.modelSelect:focus,
.periodSelect:focus,
.confidenceSelect:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Loading and Error States */
.loadingContainer,
.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.errorContainer h3 {
  color: #dc2626;
  margin-bottom: 12px;
}

.retryButton {
  padding: 10px 20px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-top: 16px;
}

.retryButton:hover {
  background: #2563eb;
}

/* Summary Grid */
.summaryGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.summaryCard {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.summaryCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.summaryCard h3 {
  color: #6b7280;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0 0 12px 0;
}

.summaryValue {
  color: #1f2937;
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
  line-height: 1.2;
}

.summarySubtext {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 8px;
}

.confidence {
  color: #059669;
  font-size: 12px;
  font-weight: 600;
  background: #ecfdf5;
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
}

/* Charts Grid */
.chartsGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.chartCard {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.chartCard h3 {
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 20px 0;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.chartContainer {
  height: 400px;
  position: relative;
}

/* Insights Section */
.insightsSection {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  margin-bottom: 30px;
}

.insightsSection h3 {
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 20px 0;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.insightsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.insightCard {
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
  background: #f8fafc;
}

.insightCard.opportunity {
  border-left-color: #059669;
  background: #ecfdf5;
}

.insightCard.warning {
  border-left-color: #f59e0b;
  background: #fffbeb;
}

.insightCard.critical {
  border-left-color: #dc2626;
  background: #fef2f2;
}

.insightCard h4 {
  color: #374151;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 12px 0;
}

.insightCard p {
  color: #6b7280;
  font-size: 14px;
  margin: 0 0 12px 0;
  line-height: 1.5;
}

.recommendation {
  background: rgba(59, 130, 246, 0.1);
  padding: 12px;
  border-radius: 6px;
  margin-top: 12px;
  font-size: 14px;
  color: #1e40af;
}

.impact {
  background: rgba(16, 185, 129, 0.1);
  padding: 12px;
  border-radius: 6px;
  margin-top: 8px;
  font-size: 14px;
  color: #047857;
}

/* Model Metrics */
.modelMetrics {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.modelMetrics h3 {
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 20px 0;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.metricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.metricCard {
  background: #f9fafb;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  text-align: center;
}

.metricCard h4 {
  color: #6b7280;
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 12px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metricValue {
  color: #1f2937;
  font-size: 24px;
  font-weight: 700;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .chartsGrid {
    grid-template-columns: 1fr;
  }
  
  .chartContainer {
    height: 350px;
  }
}

@media (max-width: 768px) {
  .predictiveAnalytics {
    padding: 16px;
  }

  .header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .controls {
    justify-content: center;
    flex-wrap: wrap;
  }

  .summaryGrid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .summaryCard {
    padding: 20px;
  }

  .summaryValue {
    font-size: 28px;
  }

  .chartCard {
    padding: 20px;
  }

  .chartContainer {
    height: 250px;
  }

  .insightsGrid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .metricsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .predictiveAnalytics {
    padding: 12px;
  }

  .header h2 {
    font-size: 24px;
  }

  .controls {
    flex-direction: column;
    gap: 8px;
  }

  .modelSelect,
  .periodSelect,
  .confidenceSelect {
    width: 100%;
  }

  .summaryValue {
    font-size: 24px;
  }

  .chartContainer {
    height: 200px;
  }

  .metricsGrid {
    grid-template-columns: 1fr;
  }
}

/* Animation for data updates */
.summaryCard,
.chartCard,
.insightCard,
.metricCard {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus states for accessibility */
.modelSelect:focus,
.periodSelect:focus,
.confidenceSelect:focus,
.retryButton:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .predictiveAnalytics {
    background: white;
    padding: 0;
  }

  .controls {
    display: none;
  }

  .chartCard,
  .summaryCard,
  .insightCard,
  .metricCard {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #e5e7eb;
  }
}
