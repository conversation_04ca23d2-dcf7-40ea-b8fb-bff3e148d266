import { supabase } from '@/lib/supabase';
import { sendOneSignalPush, sendOneSignalEmail } from '@/lib/notifications-server';

/**
 * Artist Notification Service
 * Handles real-time booking alerts and notifications specifically for artists
 */

/**
 * Send new booking alert to artist
 */
export async function sendNewBookingAlert(booking, artist, customer, service) {
  try {
    console.log('Sending new booking alert to artist:', artist.id);

    // Get artist notification preferences
    const { data: preferences, error: prefError } = await supabase
      .from('notification_preferences')
      .select('*')
      .eq('user_id', artist.id)
      .single();

    if (prefError && prefError.code !== 'PGRST116') {
      throw prefError;
    }

    const prefs = preferences || {};
    const bookingAlerts = prefs.booking_alerts || {};

    // Check if artist wants new booking alerts
    if (bookingAlerts.new_booking === false) {
      console.log('Artist has disabled new booking alerts');
      return { success: true, skipped: true, reason: 'Artist disabled new booking alerts' };
    }

    const bookingDate = new Date(booking.start_time);
    const formattedDate = bookingDate.toLocaleDateString('en-AU');
    const formattedTime = bookingDate.toLocaleTimeString('en-AU', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });

    const title = '🎉 New Booking Received!';
    const message = `You have a new booking from ${customer.name} for ${service.name} on ${formattedDate} at ${formattedTime}.`;

    const notificationData = {
      type: 'new_booking',
      booking_id: booking.id,
      customer_id: customer.id,
      service_id: service.id,
      booking_date: booking.start_time,
      priority: 'normal'
    };

    const results = [];

    // Send push notification
    if (prefs.push_notifications !== false) {
      try {
        const pushResult = await sendOneSignalPush({
          userIds: [artist.id],
          title,
          message,
          data: notificationData,
          priority: 6 // Normal priority for new bookings
        });
        results.push({ type: 'push', success: true, result: pushResult });
      } catch (error) {
        console.error('Push notification failed:', error);
        results.push({ type: 'push', success: false, error: error.message });
      }
    }

    // Send email notification
    if (prefs.email_notifications !== false && artist.email) {
      try {
        const emailResult = await sendOneSignalEmail({
          email: artist.email,
          subject: title,
          message,
          htmlBody: generateNewBookingEmailHTML(booking, artist, customer, service),
          data: notificationData
        });
        results.push({ type: 'email', success: true, result: emailResult });
      } catch (error) {
        console.error('Email notification failed:', error);
        results.push({ type: 'email', success: false, error: error.message });
      }
    }

    // Record notification in database
    await supabase
      .from('notifications')
      .insert([
        {
          user_id: artist.id,
          title,
          message,
          notification_type: 'booking_alert',
          related_id: booking.id,
          is_read: false
        }
      ]);

    return {
      success: true,
      results,
      notification_data: notificationData
    };

  } catch (error) {
    console.error('Error sending new booking alert:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Send booking change alert to artist
 */
export async function sendBookingChangeAlert(booking, artist, customer, service, changeType, changes) {
  try {
    console.log('Sending booking change alert to artist:', artist.id);

    // Get artist notification preferences
    const { data: preferences, error: prefError } = await supabase
      .from('notification_preferences')
      .select('*')
      .eq('user_id', artist.id)
      .single();

    if (prefError && prefError.code !== 'PGRST116') {
      throw prefError;
    }

    const prefs = preferences || {};
    const bookingAlerts = prefs.booking_alerts || {};

    // Check if artist wants booking change alerts
    if (bookingAlerts.booking_changes === false) {
      console.log('Artist has disabled booking change alerts');
      return { success: true, skipped: true, reason: 'Artist disabled booking change alerts' };
    }

    const bookingDate = new Date(booking.start_time);
    const formattedDate = bookingDate.toLocaleDateString('en-AU');
    const formattedTime = bookingDate.toLocaleTimeString('en-AU', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });

    let title, message;
    
    switch (changeType) {
      case 'rescheduled':
        title = '📅 Booking Rescheduled';
        message = `${customer.name}'s booking for ${service.name} has been rescheduled to ${formattedDate} at ${formattedTime}.`;
        break;
      case 'modified':
        title = '✏️ Booking Modified';
        message = `${customer.name}'s booking for ${service.name} on ${formattedDate} has been modified.`;
        break;
      case 'cancelled':
        title = '❌ Booking Cancelled';
        message = `${customer.name}'s booking for ${service.name} on ${formattedDate} has been cancelled.`;
        break;
      default:
        title = '🔄 Booking Updated';
        message = `${customer.name}'s booking for ${service.name} has been updated.`;
    }

    const notificationData = {
      type: 'booking_change',
      booking_id: booking.id,
      customer_id: customer.id,
      service_id: service.id,
      change_type: changeType,
      changes,
      priority: changeType === 'cancelled' ? 'high' : 'normal'
    };

    const results = [];

    // Send push notification
    if (prefs.push_notifications !== false) {
      try {
        const pushResult = await sendOneSignalPush({
          userIds: [artist.id],
          title,
          message,
          data: notificationData,
          priority: changeType === 'cancelled' ? 8 : 6
        });
        results.push({ type: 'push', success: true, result: pushResult });
      } catch (error) {
        console.error('Push notification failed:', error);
        results.push({ type: 'push', success: false, error: error.message });
      }
    }

    // Send email notification
    if (prefs.email_notifications !== false && artist.email) {
      try {
        const emailResult = await sendOneSignalEmail({
          email: artist.email,
          subject: title,
          message,
          htmlBody: generateBookingChangeEmailHTML(booking, artist, customer, service, changeType, changes),
          data: notificationData
        });
        results.push({ type: 'email', success: true, result: emailResult });
      } catch (error) {
        console.error('Email notification failed:', error);
        results.push({ type: 'email', success: false, error: error.message });
      }
    }

    // Record notification in database
    await supabase
      .from('notifications')
      .insert([
        {
          user_id: artist.id,
          title,
          message,
          notification_type: 'booking_alert',
          related_id: booking.id,
          is_read: false
        }
      ]);

    return {
      success: true,
      results,
      notification_data: notificationData
    };

  } catch (error) {
    console.error('Error sending booking change alert:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Send revenue milestone notification to artist
 */
export async function sendRevenueMilestoneAlert(artist, milestone, currentRevenue, period) {
  try {
    console.log('Sending revenue milestone alert to artist:', artist.id);

    // Get artist notification preferences
    const { data: preferences, error: prefError } = await supabase
      .from('notification_preferences')
      .select('*')
      .eq('user_id', artist.id)
      .single();

    if (prefError && prefError.code !== 'PGRST116') {
      throw prefError;
    }

    const prefs = preferences || {};
    const revenueAlerts = prefs.revenue_alerts || {};

    // Check if artist wants milestone notifications
    if (revenueAlerts.milestone_notifications === false) {
      console.log('Artist has disabled milestone notifications');
      return { success: true, skipped: true, reason: 'Artist disabled milestone notifications' };
    }

    const title = '🎯 Revenue Milestone Achieved!';
    const message = `Congratulations! You've reached $${milestone} in ${period} revenue. Your current total is $${currentRevenue.toFixed(2)}.`;

    const notificationData = {
      type: 'revenue_milestone',
      milestone_amount: milestone,
      current_revenue: currentRevenue,
      period,
      priority: 'normal'
    };

    const results = [];

    // Send push notification
    if (prefs.push_notifications !== false) {
      try {
        const pushResult = await sendOneSignalPush({
          userIds: [artist.id],
          title,
          message,
          data: notificationData,
          priority: 6
        });
        results.push({ type: 'push', success: true, result: pushResult });
      } catch (error) {
        console.error('Push notification failed:', error);
        results.push({ type: 'push', success: false, error: error.message });
      }
    }

    // Send email notification
    if (prefs.email_notifications !== false && artist.email) {
      try {
        const emailResult = await sendOneSignalEmail({
          email: artist.email,
          subject: title,
          message,
          htmlBody: generateRevenueMilestoneEmailHTML(artist, milestone, currentRevenue, period),
          data: notificationData
        });
        results.push({ type: 'email', success: true, result: emailResult });
      } catch (error) {
        console.error('Email notification failed:', error);
        results.push({ type: 'email', success: false, error: error.message });
      }
    }

    // Record notification in database
    await supabase
      .from('notifications')
      .insert([
        {
          user_id: artist.id,
          title,
          message,
          notification_type: 'revenue_milestone',
          related_id: `milestone_${milestone}_${period}`,
          is_read: false
        }
      ]);

    return {
      success: true,
      results,
      notification_data: notificationData
    };

  } catch (error) {
    console.error('Error sending revenue milestone alert:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Generate HTML email template for new booking notifications
 */
function generateNewBookingEmailHTML(booking, artist, customer, service) {
  const bookingDate = new Date(booking.start_time);
  const formattedDate = bookingDate.toLocaleDateString('en-AU');
  const formattedTime = bookingDate.toLocaleTimeString('en-AU', { 
    hour: '2-digit', 
    minute: '2-digit' 
  });

  return `
    <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; background: white;">
      <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 20px; text-align: center; border-radius: 8px 8px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 24px;">🎉 New Booking Received!</h1>
        <p style="color: #a7f3d0; margin: 8px 0 0 0; font-size: 14px;">Ocean Soul Sparkles</p>
      </div>
      
      <div style="padding: 30px; background: #f0fdf4; border-radius: 0 0 8px 8px;">
        <h2 style="color: #1f2937; margin: 0 0 20px 0; font-size: 20px;">Hello ${artist.name}!</h2>
        
        <div style="background: white; padding: 20px; border-radius: 8px; border: 1px solid #d1fae5; margin-bottom: 20px;">
          <h3 style="color: #065f46; margin: 0 0 16px 0; font-size: 18px;">Booking Details</h3>
          <div style="display: grid; gap: 8px;">
            <p style="margin: 0; color: #374151;"><strong>Customer:</strong> ${customer.name}</p>
            <p style="margin: 0; color: #374151;"><strong>Service:</strong> ${service.name}</p>
            <p style="margin: 0; color: #374151;"><strong>Date:</strong> ${formattedDate}</p>
            <p style="margin: 0; color: #374151;"><strong>Time:</strong> ${formattedTime}</p>
            ${booking.location ? `<p style="margin: 0; color: #374151;"><strong>Location:</strong> ${booking.location}</p>` : ''}
          </div>
        </div>
        
        <div style="text-align: center; padding: 20px; background: white; border-radius: 6px; border: 1px solid #d1fae5;">
          <p style="color: #6b7280; margin: 0; font-size: 14px;">
            Time to create some magic! ✨<br>
            Ocean Soul Sparkles Team
          </p>
        </div>
      </div>
    </div>
  `;
}

/**
 * Generate HTML email template for booking change notifications
 */
function generateBookingChangeEmailHTML(booking, artist, customer, service, changeType, changes) {
  const bookingDate = new Date(booking.start_time);
  const formattedDate = bookingDate.toLocaleDateString('en-AU');
  const formattedTime = bookingDate.toLocaleTimeString('en-AU', { 
    hour: '2-digit', 
    minute: '2-digit' 
  });

  const colorScheme = changeType === 'cancelled' 
    ? { bg: '#fef2f2', border: '#fecaca', text: '#991b1b', accent: '#dc2626' }
    : { bg: '#fef3c7', border: '#fde68a', text: '#92400e', accent: '#f59e0b' };

  return `
    <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; background: white;">
      <div style="background: ${colorScheme.accent}; padding: 20px; text-align: center; border-radius: 8px 8px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 24px;">
          ${changeType === 'cancelled' ? '❌' : '🔄'} Booking ${changeType === 'cancelled' ? 'Cancelled' : 'Updated'}
        </h1>
        <p style="color: rgba(255,255,255,0.8); margin: 8px 0 0 0; font-size: 14px;">Ocean Soul Sparkles</p>
      </div>
      
      <div style="padding: 30px; background: ${colorScheme.bg}; border-radius: 0 0 8px 8px;">
        <h2 style="color: #1f2937; margin: 0 0 20px 0; font-size: 20px;">Hello ${artist.name}!</h2>
        
        <div style="background: white; padding: 20px; border-radius: 8px; border: 1px solid ${colorScheme.border}; margin-bottom: 20px;">
          <h3 style="color: ${colorScheme.text}; margin: 0 0 16px 0; font-size: 18px;">Updated Booking Details</h3>
          <div style="display: grid; gap: 8px;">
            <p style="margin: 0; color: #374151;"><strong>Customer:</strong> ${customer.name}</p>
            <p style="margin: 0; color: #374151;"><strong>Service:</strong> ${service.name}</p>
            <p style="margin: 0; color: #374151;"><strong>Date:</strong> ${formattedDate}</p>
            <p style="margin: 0; color: #374151;"><strong>Time:</strong> ${formattedTime}</p>
            <p style="margin: 0; color: #374151;"><strong>Status:</strong> ${changeType}</p>
          </div>
        </div>
        
        <div style="text-align: center; padding: 20px; background: white; border-radius: 6px; border: 1px solid ${colorScheme.border};">
          <p style="color: #6b7280; margin: 0; font-size: 14px;">
            Ocean Soul Sparkles Team
          </p>
        </div>
      </div>
    </div>
  `;
}

/**
 * Generate HTML email template for revenue milestone notifications
 */
function generateRevenueMilestoneEmailHTML(artist, milestone, currentRevenue, period) {
  return `
    <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; background: white;">
      <div style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); padding: 20px; text-align: center; border-radius: 8px 8px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 24px;">🎯 Milestone Achieved!</h1>
        <p style="color: #c4b5fd; margin: 8px 0 0 0; font-size: 14px;">Ocean Soul Sparkles</p>
      </div>
      
      <div style="padding: 30px; background: #faf5ff; border-radius: 0 0 8px 8px;">
        <h2 style="color: #1f2937; margin: 0 0 20px 0; font-size: 20px;">Congratulations ${artist.name}!</h2>
        
        <div style="background: white; padding: 20px; border-radius: 8px; border: 1px solid #e9d5ff; margin-bottom: 20px; text-align: center;">
          <h3 style="color: #7c3aed; margin: 0 0 16px 0; font-size: 24px;">$${milestone}</h3>
          <p style="color: #374151; margin: 0; font-size: 16px;">
            You've reached your ${period} revenue milestone!<br>
            Current total: <strong>$${currentRevenue.toFixed(2)}</strong>
          </p>
        </div>
        
        <div style="text-align: center; padding: 20px; background: white; border-radius: 6px; border: 1px solid #e9d5ff;">
          <p style="color: #6b7280; margin: 0; font-size: 14px;">
            Keep up the amazing work! 🌟<br>
            Ocean Soul Sparkles Team
          </p>
        </div>
      </div>
    </div>
  `;
}
