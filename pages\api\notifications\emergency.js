import { supabase } from '@/lib/supabase';
import { sendOneSignalPush, sendOneSignalEmail } from '@/lib/notifications-server';
import { authenticateAdminRequest } from '@/lib/admin-auth';

/**
 * Emergency Notification API Endpoint
 * Handles emergency broadcast notifications with priority routing
 */
export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed' 
    });
  }

  try {
    // Authenticate and authorize admin request
    const authResult = await authenticateAdminRequest(req);
    if (!authResult.success) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    // Only allow admin and dev roles to send emergency notifications
    if (!['admin', 'dev'].includes(authResult.user.role)) {
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions for emergency notifications'
      });
    }

    const {
      title,
      message,
      priority = 'high',
      target_audience = 'all',
      channels = ['push', 'email'],
      expires_at,
      requires_acknowledgment = false,
      sent_by
    } = req.body;

    // Validate required fields
    if (!title?.trim() || !message?.trim()) {
      return res.status(400).json({
        success: false,
        error: 'Title and message are required'
      });
    }

    // Validate priority level
    if (!['high', 'critical'].includes(priority)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid priority level'
      });
    }

    // Validate target audience
    const validAudiences = ['all', 'artists', 'customers', 'admins'];
    if (!validAudiences.includes(target_audience)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid target audience'
      });
    }

    // Get target users based on audience selection
    let targetUsers = [];
    let userQuery = supabase
      .from('user_profiles')
      .select(`
        id,
        name,
        email,
        role,
        notification_preferences(
          email_notifications,
          push_notifications,
          sms_notifications,
          phone_number,
          system_alerts
        )
      `);

    switch (target_audience) {
      case 'artists':
        userQuery = userQuery.eq('role', 'artist');
        break;
      case 'customers':
        userQuery = userQuery.eq('role', 'customer');
        break;
      case 'admins':
        userQuery = userQuery.in('role', ['admin', 'dev']);
        break;
      case 'all':
        // No filter needed
        break;
    }

    const { data: users, error: userError } = await userQuery;

    if (userError) {
      throw userError;
    }

    targetUsers = users || [];

    // Filter users based on their notification preferences
    // Emergency notifications override most preferences, but respect system_alerts setting
    const eligibleUsers = targetUsers.filter(user => {
      const prefs = user.notification_preferences || {};
      
      // Critical alerts always go through (emergency override)
      if (priority === 'critical') {
        return true;
      }
      
      // High priority respects system_alerts preference
      return prefs.system_alerts !== false;
    });

    if (eligibleUsers.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No eligible users found for the selected audience'
      });
    }

    // Create emergency notification record
    const { data: emergencyRecord, error: recordError } = await supabase
      .from('emergency_notifications')
      .insert([
        {
          title,
          message,
          priority,
          target_audience,
          channels,
          expires_at: expires_at || null,
          requires_acknowledgment,
          sent_by: authResult.user.id,
          sent_at: new Date().toISOString(),
          recipients_count: eligibleUsers.length,
          status: 'sending'
        }
      ])
      .select()
      .single();

    if (recordError) {
      throw recordError;
    }

    // Send notifications through selected channels
    const notificationResults = [];
    const notificationData = {
      type: 'emergency',
      priority,
      emergency_id: emergencyRecord.id,
      requires_acknowledgment,
      expires_at
    };

    // Prepare notification content with priority indicators
    const priorityEmoji = priority === 'critical' ? '🚨🚨🚨' : '⚠️';
    const formattedTitle = `${priorityEmoji} ${title}`;
    const formattedMessage = `${message}\n\n${priority === 'critical' ? 'CRITICAL ALERT' : 'HIGH PRIORITY'} - Immediate attention required.`;

    for (const user of eligibleUsers) {
      const userPrefs = user.notification_preferences || {};
      const userResults = [];

      // Send push notification
      if (channels.includes('push') && userPrefs.push_notifications !== false) {
        try {
          const pushResult = await sendOneSignalPush({
            userIds: [user.id],
            title: formattedTitle,
            message: formattedMessage,
            data: {
              ...notificationData,
              user_id: user.id
            },
            priority: priority === 'critical' ? 10 : 8, // High priority delivery
            ttl: priority === 'critical' ? 3600 : 1800 // Time to live in seconds
          });
          userResults.push({ type: 'push', success: true, result: pushResult });
        } catch (error) {
          console.error(`Push notification failed for user ${user.id}:`, error);
          userResults.push({ type: 'push', success: false, error: error.message });
        }
      }

      // Send email notification
      if (channels.includes('email') && userPrefs.email_notifications !== false && user.email) {
        try {
          const emailResult = await sendOneSignalEmail({
            email: user.email,
            subject: formattedTitle,
            message: formattedMessage,
            htmlBody: generateEmergencyEmailHTML(title, message, priority, emergencyRecord.id),
            data: {
              ...notificationData,
              user_id: user.id
            }
          });
          userResults.push({ type: 'email', success: true, result: emailResult });
        } catch (error) {
          console.error(`Email notification failed for user ${user.id}:`, error);
          userResults.push({ type: 'email', success: false, error: error.message });
        }
      }

      // Send SMS notification (if implemented and requested)
      if (channels.includes('sms') && userPrefs.sms_notifications && userPrefs.phone_number) {
        // SMS implementation would go here
        console.log(`SMS notification would be sent to ${user.id} at ${userPrefs.phone_number}`);
        userResults.push({ type: 'sms', success: false, error: 'SMS not implemented yet' });
      }

      notificationResults.push({
        user_id: user.id,
        user_name: user.name,
        user_email: user.email,
        results: userResults
      });

      // Record individual notification in database
      try {
        await supabase
          .from('notifications')
          .insert([
            {
              user_id: user.id,
              title: formattedTitle,
              message: formattedMessage,
              notification_type: 'emergency',
              related_id: emergencyRecord.id,
              priority,
              is_read: false,
              expires_at: expires_at || null,
              requires_acknowledgment
            }
          ]);
      } catch (error) {
        console.error(`Failed to record notification for user ${user.id}:`, error);
      }
    }

    // Update emergency record with completion status
    const successfulNotifications = notificationResults.filter(result => 
      result.results.some(r => r.success)
    ).length;

    await supabase
      .from('emergency_notifications')
      .update({
        status: 'sent',
        successful_deliveries: successfulNotifications,
        delivery_results: notificationResults,
        completed_at: new Date().toISOString()
      })
      .eq('id', emergencyRecord.id);

    return res.status(200).json({
      success: true,
      emergency_id: emergencyRecord.id,
      recipients_count: eligibleUsers.length,
      successful_deliveries: successfulNotifications,
      delivery_summary: {
        total_users: eligibleUsers.length,
        successful: successfulNotifications,
        failed: eligibleUsers.length - successfulNotifications
      }
    });

  } catch (error) {
    console.error('Emergency notification error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to send emergency notification: ' + error.message
    });
  }
}

/**
 * Generate HTML email template for emergency notifications
 */
function generateEmergencyEmailHTML(title, message, priority, emergencyId) {
  const priorityColor = priority === 'critical' ? '#dc2626' : '#f59e0b';
  const priorityBg = priority === 'critical' ? '#fef2f2' : '#fef3c7';
  const priorityText = priority === 'critical' ? 'CRITICAL ALERT' : 'HIGH PRIORITY';

  return `
    <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; background: white;">
      <div style="background: ${priorityBg}; border: 2px solid ${priorityColor}; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
        <div style="text-align: center; margin-bottom: 16px;">
          <h1 style="color: ${priorityColor}; margin: 0; font-size: 24px; font-weight: bold;">
            ${priority === 'critical' ? '🚨🚨🚨' : '⚠️'} EMERGENCY NOTIFICATION
          </h1>
          <div style="background: ${priorityColor}; color: white; padding: 4px 12px; border-radius: 4px; display: inline-block; font-size: 12px; font-weight: bold; margin-top: 8px;">
            ${priorityText}
          </div>
        </div>
        
        <h2 style="color: ${priorityColor}; margin: 0 0 12px 0; font-size: 20px;">${title}</h2>
        <div style="background: white; padding: 16px; border-radius: 6px; border: 1px solid ${priorityColor};">
          <p style="color: #374151; margin: 0; line-height: 1.6; font-size: 16px;">${message}</p>
        </div>
      </div>
      
      <div style="text-align: center; padding: 20px; background: #f9fafb; border-radius: 6px;">
        <p style="color: #6b7280; margin: 0; font-size: 14px;">
          This is an emergency notification from Ocean Soul Sparkles.<br>
          Emergency ID: ${emergencyId}<br>
          Sent: ${new Date().toLocaleString()}
        </p>
      </div>
    </div>
  `;
}
