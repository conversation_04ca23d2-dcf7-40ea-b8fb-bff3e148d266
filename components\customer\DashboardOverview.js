/**
 * Dashboard Overview Component - Phase 8: Advanced Customer Experience
 * Ocean Soul Sparkles - Customer Dashboard Overview
 */

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useMobileOptimization } from '@/lib/hooks/useMobileOptimization'
import { customerUtils } from '@/contexts/CustomerContext'
import styles from '@/styles/customer/DashboardOverview.module.css'

export default function DashboardOverview({ customer, dashboardData, onRefresh }) {
  const { isMobile, viewport } = useMobileOptimization()
  
  const [stats, setStats] = useState({
    totalBookings: 0,
    totalSpent: 0,
    favoriteService: null,
    memberSince: null
  })

  useEffect(() => {
    if (customer && dashboardData) {
      calculateStats()
    }
  }, [customer, dashboardData])

  const calculateStats = () => {
    const memberSince = customer.created_at ? new Date(customer.created_at) : null
    
    setStats({
      totalBookings: customer.booking_count || 0,
      totalSpent: customer.lifetime_value || 0,
      favoriteService: customer.preferences?.favorite_services?.[0] || null,
      memberSince
    })
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(amount)
  }

  const formatDate = (date) => {
    if (!date) return 'N/A'
    return new Date(date).toLocaleDateString('en-AU', {
      year: 'numeric',
      month: 'long'
    })
  }

  const getNextBooking = () => {
    return dashboardData.upcomingBookings?.[0] || null
  }

  const getLoyaltyProgress = () => {
    if (!dashboardData.loyaltyInfo) return null
    
    const tierInfo = customerUtils.getLoyaltyTierInfo(dashboardData.loyaltyInfo.tier_level)
    const progress = customerUtils.calculateLoyaltyProgress(customer)
    
    return {
      ...tierInfo,
      progress,
      currentPoints: dashboardData.loyaltyInfo.points_balance,
      lifetimePoints: dashboardData.loyaltyInfo.lifetime_points
    }
  }

  const nextBooking = getNextBooking()
  const loyaltyProgress = getLoyaltyProgress()

  return (
    <div className={styles.dashboardOverview}>
      {/* Stats Cards */}
      <div className={styles.statsGrid}>
        <div className={styles.statCard}>
          <div className={styles.statIcon}>📊</div>
          <div className={styles.statContent}>
            <div className={styles.statValue}>{stats.totalBookings}</div>
            <div className={styles.statLabel}>Total Bookings</div>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={styles.statIcon}>💰</div>
          <div className={styles.statContent}>
            <div className={styles.statValue}>{formatCurrency(stats.totalSpent)}</div>
            <div className={styles.statLabel}>Total Spent</div>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={styles.statIcon}>⭐</div>
          <div className={styles.statContent}>
            <div className={styles.statValue}>
              {loyaltyProgress ? loyaltyProgress.currentPoints : 0}
            </div>
            <div className={styles.statLabel}>Loyalty Points</div>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={styles.statIcon}>📅</div>
          <div className={styles.statContent}>
            <div className={styles.statValue}>{formatDate(stats.memberSince)}</div>
            <div className={styles.statLabel}>Member Since</div>
          </div>
        </div>
      </div>

      {/* Next Booking */}
      {nextBooking && (
        <div className={styles.nextBookingCard}>
          <div className={styles.cardHeader}>
            <h3 className={styles.cardTitle}>Next Appointment</h3>
            <Link 
              href={`/customer/bookings/${nextBooking.id}`}
              className={styles.viewDetailsLink}
            >
              View Details
            </Link>
          </div>
          
          <div className={styles.bookingInfo}>
            <div className={styles.bookingService}>
              <span className={styles.serviceIcon}>✨</span>
              <span className={styles.serviceName}>{nextBooking.service?.name}</span>
            </div>
            
            <div className={styles.bookingDateTime}>
              <span className={styles.dateIcon}>📅</span>
              <span className={styles.dateTime}>
                {new Date(nextBooking.start_time).toLocaleDateString('en-AU', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })} at {new Date(nextBooking.start_time).toLocaleTimeString('en-AU', {
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </span>
            </div>
            
            {nextBooking.artist && (
              <div className={styles.bookingArtist}>
                <span className={styles.artistIcon}>👤</span>
                <span className={styles.artistName}>
                  {nextBooking.artist.display_name || nextBooking.artist.artist_name}
                </span>
              </div>
            )}
          </div>

          <div className={styles.bookingActions}>
            <Link 
              href={`/customer/bookings/${nextBooking.id}/reschedule`}
              className={styles.rescheduleButton}
            >
              Reschedule
            </Link>
            <Link 
              href={`/customer/bookings/${nextBooking.id}/cancel`}
              className={styles.cancelButton}
            >
              Cancel
            </Link>
          </div>
        </div>
      )}

      {/* Loyalty Progress */}
      {loyaltyProgress && (
        <div className={styles.loyaltyCard}>
          <div className={styles.cardHeader}>
            <h3 className={styles.cardTitle}>Loyalty Status</h3>
            <Link 
              href="/customer/loyalty"
              className={styles.viewDetailsLink}
            >
              View Rewards
            </Link>
          </div>
          
          <div className={styles.loyaltyContent}>
            <div className={styles.tierBadge} style={{ backgroundColor: loyaltyProgress.color }}>
              {loyaltyProgress.name} Member
            </div>
            
            <div className={styles.pointsInfo}>
              <div className={styles.currentPoints}>
                {loyaltyProgress.currentPoints} points available
              </div>
              <div className={styles.lifetimePoints}>
                {loyaltyProgress.lifetimePoints} lifetime points
              </div>
            </div>
            
            {loyaltyProgress.nextTier && (
              <div className={styles.progressSection}>
                <div className={styles.progressLabel}>
                  Progress to {loyaltyProgress.nextTier}
                </div>
                <div className={styles.progressBar}>
                  <div 
                    className={styles.progressFill}
                    style={{ width: `${loyaltyProgress.progress}%` }}
                  />
                </div>
                <div className={styles.progressText}>
                  {Math.round(loyaltyProgress.progress)}% complete
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Recent Activity */}
      {dashboardData.recentHistory && dashboardData.recentHistory.length > 0 && (
        <div className={styles.recentActivityCard}>
          <div className={styles.cardHeader}>
            <h3 className={styles.cardTitle}>Recent Services</h3>
            <Link 
              href="/customer/history"
              className={styles.viewDetailsLink}
            >
              View All
            </Link>
          </div>
          
          <div className={styles.activityList}>
            {dashboardData.recentHistory.slice(0, 3).map(item => (
              <div key={item.id} className={styles.activityItem}>
                <div className={styles.activityIcon}>✨</div>
                <div className={styles.activityContent}>
                  <div className={styles.activityService}>{item.service?.name}</div>
                  <div className={styles.activityDate}>
                    {new Date(item.service_date).toLocaleDateString('en-AU')}
                  </div>
                </div>
                {item.customer_satisfaction_score && (
                  <div className={styles.activityRating}>
                    {'⭐'.repeat(item.customer_satisfaction_score)}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Quick Actions Summary */}
      <div className={styles.quickActionsSummary}>
        <h3 className={styles.cardTitle}>Quick Actions</h3>
        <div className={styles.actionsList}>
          <Link href="/book-online" className={styles.actionItem}>
            <span className={styles.actionIcon}>📅</span>
            <span className={styles.actionLabel}>Book New Appointment</span>
          </Link>
          
          <Link href="/customer/services/multi-booking" className={styles.actionItem}>
            <span className={styles.actionIcon}>✨</span>
            <span className={styles.actionLabel}>Multi-Service Booking</span>
          </Link>
          
          <Link href="/customer/messages" className={styles.actionItem}>
            <span className={styles.actionIcon}>💬</span>
            <span className={styles.actionLabel}>Messages</span>
            {dashboardData.unreadMessages > 0 && (
              <span className={styles.actionBadge}>{dashboardData.unreadMessages}</span>
            )}
          </Link>
          
          <Link href="/customer/profile" className={styles.actionItem}>
            <span className={styles.actionIcon}>👤</span>
            <span className={styles.actionLabel}>Update Profile</span>
          </Link>
        </div>
      </div>

      {/* Refresh Button */}
      <div className={styles.refreshSection}>
        <button onClick={onRefresh} className={styles.refreshButton}>
          <span className={styles.refreshIcon}>🔄</span>
          <span className={styles.refreshLabel}>Refresh Dashboard</span>
        </button>
      </div>
    </div>
  )
}
