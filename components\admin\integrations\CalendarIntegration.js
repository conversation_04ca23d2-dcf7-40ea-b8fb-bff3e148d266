/**
 * Calendar Integration Component for Ocean Soul Sparkles
 * Provides calendar integration management interface
 * 
 * Phase 7: Advanced Integrations & Ecosystem
 */

import { useState, useEffect } from 'react'
import { toast } from 'react-toastify'
import { useMobileOptimization } from '@/lib/hooks/useMobileOptimization'
import { authenticatedFetch } from '@/lib/auth-utils'
import styles from './CalendarIntegration.module.css'

export default function CalendarIntegration({ userId }) {
  const [integrations, setIntegrations] = useState([])
  const [syncStatus, setSyncStatus] = useState({})
  const [conflicts, setConflicts] = useState([])
  const [loading, setLoading] = useState(true)
  const [syncing, setSyncing] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')

  const {
    isMobile,
    isTablet,
    hapticFeedback,
    shouldReduceAnimations
  } = useMobileOptimization()

  useEffect(() => {
    loadIntegrationData()
  }, [userId])

  /**
   * Load integration data
   */
  const loadIntegrationData = async () => {
    try {
      setLoading(true)

      // Load integration status
      const statusResponse = await authenticatedFetch('/api/integrations/calendar/status')
      if (statusResponse.success) {
        setIntegrations(statusResponse.integrations || [])
        setSyncStatus(statusResponse.syncStatus || {})
      }

      // Load conflicts
      const conflictsResponse = await authenticatedFetch('/api/integrations/calendar/conflicts')
      if (conflictsResponse.success) {
        setConflicts(conflictsResponse.conflicts || [])
      }

    } catch (error) {
      console.error('Failed to load integration data:', error)
      toast.error('Failed to load calendar integration data')
    } finally {
      setLoading(false)
    }
  }

  /**
   * Connect to calendar provider
   */
  const connectCalendar = async (provider) => {
    try {
      hapticFeedback('light')

      const response = await authenticatedFetch(`/api/integrations/oauth/${provider}`)
      
      if (response.success && response.authUrl) {
        // Redirect to OAuth authorization
        window.location.href = response.authUrl
      } else {
        toast.error(response.message || 'Failed to initiate calendar connection')
      }

    } catch (error) {
      console.error('Failed to connect calendar:', error)
      toast.error('Failed to connect calendar')
    }
  }

  /**
   * Disconnect calendar provider
   */
  const disconnectCalendar = async (provider) => {
    try {
      hapticFeedback('medium')

      const confirmed = window.confirm(
        `Are you sure you want to disconnect ${provider}? This will stop syncing your bookings to this calendar.`
      )

      if (!confirmed) return

      const response = await authenticatedFetch(`/api/integrations/calendar/disconnect`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ provider })
      })

      if (response.success) {
        toast.success(`${provider} disconnected successfully`)
        await loadIntegrationData()
      } else {
        toast.error(response.message || 'Failed to disconnect calendar')
      }

    } catch (error) {
      console.error('Failed to disconnect calendar:', error)
      toast.error('Failed to disconnect calendar')
    }
  }

  /**
   * Trigger manual sync
   */
  const triggerSync = async () => {
    try {
      setSyncing(true)
      hapticFeedback('light')

      const response = await authenticatedFetch('/api/integrations/calendar/sync', {
        method: 'POST'
      })

      if (response.success) {
        toast.success('Calendar sync completed successfully')
        await loadIntegrationData()
      } else {
        toast.error(response.message || 'Calendar sync failed')
      }

    } catch (error) {
      console.error('Failed to sync calendars:', error)
      toast.error('Failed to sync calendars')
    } finally {
      setSyncing(false)
    }
  }

  /**
   * Update integration settings
   */
  const updateSettings = async (provider, settings) => {
    try {
      const response = await authenticatedFetch('/api/integrations/calendar/settings', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ provider, settings })
      })

      if (response.success) {
        toast.success('Settings updated successfully')
        await loadIntegrationData()
      } else {
        toast.error(response.message || 'Failed to update settings')
      }

    } catch (error) {
      console.error('Failed to update settings:', error)
      toast.error('Failed to update settings')
    }
  }

  /**
   * Resolve conflict
   */
  const resolveConflict = async (conflictId, resolution) => {
    try {
      hapticFeedback('medium')

      const response = await authenticatedFetch('/api/integrations/calendar/resolve-conflict', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ conflictId, resolution })
      })

      if (response.success) {
        toast.success('Conflict resolved successfully')
        await loadIntegrationData()
      } else {
        toast.error(response.message || 'Failed to resolve conflict')
      }

    } catch (error) {
      console.error('Failed to resolve conflict:', error)
      toast.error('Failed to resolve conflict')
    }
  }

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>Loading calendar integrations...</p>
      </div>
    )
  }

  return (
    <div className={`${styles.container} ${isMobile ? styles.mobile : ''}`}>
      {/* Header */}
      <div className={styles.header}>
        <h2>Calendar Integration</h2>
        <p>Sync your Ocean Soul Sparkles bookings with external calendars</p>
      </div>

      {/* Navigation Tabs */}
      <div className={styles.tabs}>
        {['overview', 'settings', 'conflicts'].map(tab => (
          <button
            key={tab}
            className={`${styles.tab} ${activeTab === tab ? styles.active : ''}`}
            onClick={() => {
              setActiveTab(tab)
              hapticFeedback('light')
            }}
          >
            {tab.charAt(0).toUpperCase() + tab.slice(1)}
            {tab === 'conflicts' && conflicts.length > 0 && (
              <span className={styles.badge}>{conflicts.length}</span>
            )}
          </button>
        ))}
      </div>

      {/* Overview Tab */}
      {activeTab === 'overview' && (
        <div className={styles.tabContent}>
          {/* Sync Status */}
          <div className={styles.syncStatus}>
            <div className={styles.syncHeader}>
              <h3>Sync Status</h3>
              <button
                className={`${styles.syncButton} ${syncing ? styles.syncing : ''}`}
                onClick={triggerSync}
                disabled={syncing}
              >
                {syncing ? 'Syncing...' : 'Sync Now'}
              </button>
            </div>
            
            {syncStatus.lastSync && (
              <p className={styles.lastSync}>
                Last sync: {new Date(syncStatus.lastSync).toLocaleString()}
              </p>
            )}
          </div>

          {/* Connected Calendars */}
          <div className={styles.calendars}>
            <h3>Connected Calendars</h3>
            
            {integrations.length === 0 ? (
              <div className={styles.noCalendars}>
                <p>No calendars connected yet</p>
                <button
                  className={styles.connectButton}
                  onClick={() => connectCalendar('google_calendar')}
                >
                  Connect Google Calendar
                </button>
              </div>
            ) : (
              <div className={styles.calendarList}>
                {integrations.map(integration => (
                  <div key={integration.provider} className={styles.calendarCard}>
                    <div className={styles.calendarInfo}>
                      <h4>{integration.name}</h4>
                      <p className={`${styles.status} ${styles[integration.status]}`}>
                        {integration.status === 'connected' ? '✓ Connected' : '⚠ Needs Attention'}
                      </p>
                      {integration.lastSync && (
                        <p className={styles.lastSyncTime}>
                          Last sync: {new Date(integration.lastSync).toLocaleString()}
                        </p>
                      )}
                    </div>
                    
                    <div className={styles.calendarActions}>
                      <button
                        className={styles.settingsButton}
                        onClick={() => setActiveTab('settings')}
                      >
                        Settings
                      </button>
                      <button
                        className={styles.disconnectButton}
                        onClick={() => disconnectCalendar(integration.provider)}
                      >
                        Disconnect
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Quick Stats */}
          {syncStatus.stats && (
            <div className={styles.stats}>
              <h3>Sync Statistics</h3>
              <div className={styles.statsGrid}>
                <div className={styles.statCard}>
                  <span className={styles.statNumber}>{syncStatus.stats.bookingsSynced}</span>
                  <span className={styles.statLabel}>Bookings Synced</span>
                </div>
                <div className={styles.statCard}>
                  <span className={styles.statNumber}>{syncStatus.stats.eventsImported}</span>
                  <span className={styles.statLabel}>Events Imported</span>
                </div>
                <div className={styles.statCard}>
                  <span className={styles.statNumber}>{conflicts.length}</span>
                  <span className={styles.statLabel}>Conflicts</span>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Settings Tab */}
      {activeTab === 'settings' && (
        <div className={styles.tabContent}>
          <div className={styles.settings}>
            <h3>Calendar Settings</h3>
            
            {integrations.map(integration => (
              <div key={integration.provider} className={styles.settingsCard}>
                <h4>{integration.name} Settings</h4>
                
                <div className={styles.settingGroup}>
                  <label>
                    <input
                      type="checkbox"
                      checked={integration.settings?.autoSync || false}
                      onChange={(e) => updateSettings(integration.provider, {
                        ...integration.settings,
                        autoSync: e.target.checked
                      })}
                    />
                    Auto-sync bookings to calendar
                  </label>
                </div>

                <div className={styles.settingGroup}>
                  <label>
                    Sync frequency:
                    <select
                      value={integration.settings?.syncFrequency || 'hourly'}
                      onChange={(e) => updateSettings(integration.provider, {
                        ...integration.settings,
                        syncFrequency: e.target.value
                      })}
                    >
                      <option value="realtime">Real-time</option>
                      <option value="hourly">Hourly</option>
                      <option value="daily">Daily</option>
                      <option value="manual">Manual only</option>
                    </select>
                  </label>
                </div>

                <div className={styles.settingGroup}>
                  <label>
                    Conflict resolution:
                    <select
                      value={integration.settings?.conflictResolution || 'manual'}
                      onChange={(e) => updateSettings(integration.provider, {
                        ...integration.settings,
                        conflictResolution: e.target.value
                      })}
                    >
                      <option value="manual">Manual review</option>
                      <option value="ocean_soul_sparkles_wins">Ocean Soul Sparkles wins</option>
                      <option value="external_wins">External calendar wins</option>
                    </select>
                  </label>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Conflicts Tab */}
      {activeTab === 'conflicts' && (
        <div className={styles.tabContent}>
          <div className={styles.conflicts}>
            <h3>Calendar Conflicts</h3>
            
            {conflicts.length === 0 ? (
              <div className={styles.noConflicts}>
                <p>No conflicts detected</p>
              </div>
            ) : (
              <div className={styles.conflictList}>
                {conflicts.map(conflict => (
                  <div key={conflict.id} className={styles.conflictCard}>
                    <div className={styles.conflictInfo}>
                      <h4>Booking Conflict</h4>
                      <p><strong>Date:</strong> {new Date(conflict.date).toLocaleString()}</p>
                      <p><strong>Service:</strong> {conflict.serviceName}</p>
                      <p><strong>Conflicts with:</strong> {conflict.externalEvent.summary}</p>
                    </div>
                    
                    <div className={styles.conflictActions}>
                      <button
                        className={styles.resolveButton}
                        onClick={() => resolveConflict(conflict.id, 'keep_booking')}
                      >
                        Keep Booking
                      </button>
                      <button
                        className={styles.resolveButton}
                        onClick={() => resolveConflict(conflict.id, 'reschedule')}
                      >
                        Reschedule
                      </button>
                      <button
                        className={styles.resolveButton}
                        onClick={() => resolveConflict(conflict.id, 'cancel')}
                      >
                        Cancel Booking
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
