import Head from 'next/head';
import AdminLayout from '@/components/admin/AdminLayout'; // Or a more generic Layout if appropriate
import ProtectedRoute from '@/components/admin/ProtectedRoute';
// Basic styling, can be enhanced with a CSS module if needed
// For now, inline styles or simple structure.

export default function ArtistPoliciesPage() {
  return (
    <ProtectedRoute allowedRoles={['artist', 'braider']}>
      <AdminLayout title="Induction Policies">
        <Head>
          <title>Induction Policies & Procedures | Artist Dashboard</title>
        </Head>
        <div style={{ padding: '20px', maxWidth: '800px', margin: 'auto' }}>
          <h1>Induction Policies & Procedures</h1>

          <p>Welcome to the Ocean Soul Sparkles team! We're thrilled to have you. Please take some time to review the following policies and procedures. Understanding and adhering to these guidelines will help ensure a smooth, professional, and enjoyable working experience for everyone.</p>

          <section style={{ marginBottom: '30px' }}>
            <h2>1. Code of Conduct</h2>
            <p>All team members are expected to maintain the highest standards of professionalism and respect when interacting with clients, colleagues, and partners. This includes punctuality, appropriate attire (details to be provided), and clear communication.</p>
            {/* Placeholder for more detailed Code of Conduct */}
            <p><em>Placeholder: More details on professional behavior, client interaction standards, conflict resolution, etc.</em></p>
          </section>

          <section style={{ marginBottom: '30px' }}>
            <h2>2. Service Standards & Quality</h2>
            <p>Ocean Soul Sparkles is committed to providing exceptional quality services. All artists and braiders must adhere to our established service protocols, hygiene standards, and quality benchmarks.</p>
            {/* Placeholder for more detailed Service Standards */}
            <p><em>Placeholder: Details on specific service techniques, approved products, hygiene protocols, client consultation process, quality checks, etc.</em></p>
          </section>

          <section style={{ marginBottom: '30px' }}>
            <h2>3. Booking & Schedule Management</h2>
            <p>Please manage your availability accurately through the provided artist dashboard. Adhere to confirmed booking times and communicate any potential scheduling conflicts as early as possible to the admin team.</p>
            {/* Placeholder for more detailed Booking Policies */}
            <p><em>Placeholder: Policies on accepting/declining bookings, managing availability, handling cancellations/reschedules, no-show procedures, etc.</em></p>
          </section>

          <section style={{ marginBottom: '30px' }}>
            <h2>4. Payment & Compensation</h2>
            <p>Details regarding service rates, commission structures, payment schedules, and expense reimbursements will be outlined in your individual contract and accessible via your dashboard.</p>
            {/* Placeholder for more detailed Payment Policies */}
            <p><em>Placeholder: Information on invoicing, payment processing, commission calculations, dispute resolution for payments, etc.</em></p>
          </section>

          <section style={{ marginBottom: '30px' }}>
            <h2>5. Health & Safety</h2>
            <p>The health and safety of our clients and team members are paramount. Follow all health guidelines, sanitation procedures, and report any incidents or concerns immediately.</p>
            {/* Placeholder for more detailed Health & Safety Policies */}
            <p><em>Placeholder: Specific hygiene rules, use of PPE, incident reporting, emergency contacts, workspace cleanliness standards, etc.</em></p>
          </section>

          <section style={{ marginBottom: '30px' }}>
            <h2>6. Confidentiality & Data Protection</h2>
            <p>You will have access to client information. All client data must be handled with strict confidentiality and in accordance with privacy laws and company policy.</p>
            {/* Placeholder for more detailed Confidentiality Policies */}
            <p><em>Placeholder: Guidelines on handling client data, non-disclosure agreements, social media policy regarding clients, GDPR/privacy compliance if applicable.</em></p>
          </section>

          <hr style={{ margin: '30px 0' }} />

          <p>By continuing to work with Ocean Soul Sparkles, you acknowledge that you have read, understood, and agree to comply with these policies and procedures. These may be updated from time to time, and you will be notified of any significant changes.</p>
          <p>If you have any questions regarding these policies, please reach out to the administration team.</p>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}
