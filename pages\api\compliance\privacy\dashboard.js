/**
 * Privacy Dashboard API Endpoint for Ocean Soul Sparkles
 * Returns comprehensive privacy data for user dashboard
 * 
 * Phase 9.2: Data Protection & Privacy Compliance
 */

import { withAdminAuth } from '@/lib/admin-auth'
import { privacyManager } from '@/lib/compliance/privacy-manager'

async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { userId, customerId } = req.body

    if (!userId && !customerId) {
      return res.status(400).json({ 
        error: 'Missing required fields',
        message: 'Either userId or customerId is required'
      })
    }

    // Get comprehensive privacy data
    const privacyData = await privacyManager.getPrivacyDashboardData(userId, customerId)

    res.status(200).json(privacyData)

  } catch (error) {
    console.error('Privacy dashboard error:', error)
    res.status(500).json({
      error: 'Failed to load privacy data',
      message: error.message
    })
  }
}

export default with<PERSON>d<PERSON><PERSON><PERSON>(handler)
