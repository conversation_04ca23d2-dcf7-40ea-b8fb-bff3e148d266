import { useState, useEffect, useCallback } from 'react';
import Head from 'next/head';
import AdminLayout from '@/components/admin/AdminLayout'; // Assuming AdminLayout can be used for artists too, or use a different Layout
import ProtectedRoute from '@/components/admin/ProtectedRoute'; // Assuming this can be configured for artist/braider roles
import ArtistProfileForm from '@/components/artists/ArtistProfileForm';
import AvailabilityScheduleManager from '@/components/artists/AvailabilityScheduleManager';
import ServiceSpecializationManager from '@/components/artists/ServiceSpecializationManager';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

export default function MyProfilePage() {
  const [profileData, setProfileData] = useState(null);
  const [isLoading, setIsLoading] = useState(true); // For profile form
  const [isSubmitting, setIsSubmitting] = useState(false); // For profile form submission
  const [error, setError] = useState(null); // For profile form

  const [availability, setAvailability] = useState([]);
  const [isAvailabilityLoading, setIsAvailabilityLoading] = useState(true);
  const [availabilityError, setAvailabilityError] = useState(null);
  const [isSubmittingAvailability, setIsSubmittingAvailability] = useState(false);

  const [isSubmittingSpecializations, setIsSubmittingSpecializations] = useState(false);

  const fetchProfile = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/artist/me/profile');
      if (!response.ok) {
        const errData = await response.json();
        throw new Error(errData.error || `Failed to fetch profile. Status: ${response.status}`);
      }
      const data = await response.json();
      setProfileData(data);
    } catch (err) {
      setError(err.message);
      toast.error(`Error fetching initial profile: ${err.message}`);
      setProfileData(null); // Clear profile data on error
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchAvailability = useCallback(async () => {
    setIsAvailabilityLoading(true);
    setAvailabilityError(null);
    try {
      const response = await fetch('/api/artist/me/availability'); // GET request
      if (!response.ok) {
        const errData = await response.json();
        throw new Error(errData.error || `Failed to fetch availability. Status: ${response.status}`);
      }
      const data = await response.json();
      setAvailability(data || []);
    } catch (err) {
      setAvailabilityError(err.message);
      toast.error(`Error fetching availability: ${err.message}`);
      setAvailability([]);
    } finally {
      setIsAvailabilityLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchProfile();
    fetchAvailability();
  }, [fetchProfile, fetchAvailability]);

  // This function will be passed to the form to handle updates
  const handleProfileUpdate = async (updatedData) => {
    setIsSubmitting(true);
    setError(null); // Clear previous errors
    try {
      const response = await fetch('/api/artist/me/profile', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedData),
      });

      if (!response.ok) {
        const errData = await response.json();
        throw new Error(errData.error || `Failed to update profile. Status: ${response.status}`);
      }

      const result = await response.json();

      // Update profileData state with the merged results from API
      // The API returns { message, artist_profile, user_profile }
      // Need to reconstruct the profileData structure used by the page/form
      setProfileData(prev => ({
        ...prev, // Keep existing data like email, specializations etc.
        display_name: result.artist_profile?.display_name ?? prev.display_name,
        bio: result.artist_profile?.bio ?? prev.bio,
        portfolio_urls: result.artist_profile?.portfolio_urls ?? prev.portfolio_urls,
        phone: result.user_profile?.phone ?? prev.phone,
        // If API returns more fields from artist_profile (like artist_name), update them too
        artist_name: result.artist_profile?.artist_name ?? prev.artist_name,
      }));

      toast.success(result.message || 'Profile updated successfully!');
    } catch (err) {
      setError(err.message); // Set page-level error for display if needed, or just rely on toast
      toast.error(`Error updating profile: ${err.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAvailabilityUpdate = async (newSchedule) => {
    setIsSubmittingAvailability(true);
    setAvailabilityError(null);
    try {
      const response = await fetch('/api/artist/me/availability', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newSchedule),
      });
      if (!response.ok) {
        const errData = await response.json();
        throw new Error(errData.error || `Failed to update availability. Status: ${response.status}`);
      }
      const result = await response.json();
      setAvailability(result.availability || []); // Update with the saved schedule
      toast.success(result.message || 'Availability updated successfully!');
    } catch (err) {
      setAvailabilityError(err.message);
      toast.error(`Error updating availability: ${err.message}`);
    } finally {
      setIsSubmittingAvailability(false);
    }
  };

  const handleSpecializationUpdate = async (newSpecializationIds) => {
    setIsSubmittingSpecializations(true);
    // setError(null); // Using general 'error' state if needed, or rely on toasts
    try {
      const response = await fetch('/api/artist/me/specializations', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ specializations: newSpecializationIds }),
      });
      if (!response.ok) {
        const errData = await response.json();
        throw new Error(errData.error || `Failed to update specializations. Status: ${response.status}`);
      }
      const result = await response.json();

      // Update profileData state with the new specializations from API response
      setProfileData(prev => ({
        ...prev,
        specializations: result.specializations || [],
      }));

      toast.success(result.message || 'Specializations updated successfully!');
    } catch (err) {
      // setError(err.message); // Optionally set a page-level error
      toast.error(`Error updating specializations: ${err.message}`);
    } finally {
      setIsSubmittingSpecializations(false);
    }
  };

  // Render logic
  let profileContent;
  if (isLoading) {
    profileContent = <p>Loading profile...</p>; // Replace with a proper spinner/loading component
  } else if (error) {
    // Display error message and a retry button
    profileContent = (
      <div>
        <p style={{ color: 'red' }}>Error: {error}</p>
        <button onClick={fetchProfile} disabled={isLoading}>Retry Fetching Profile</button>
      </div>
    );
  } else if (profileData) {
    profileContent = (
      <div>
        <ArtistProfileForm
          initialData={profileData}
          onSubmit={handleProfileUpdate}
          isSubmitting={isSubmitting}
        />
      </div>
    );
  } else {
    profileContent = <p>No profile data found. You might need to complete an application process if you haven't.</p>;
  }

  let availabilityContent;
  if (isAvailabilityLoading) {
    availabilityContent = <p>Loading availability schedule...</p>;
  } else if (availabilityError) {
    availabilityContent = <p style={{ color: 'red' }}>Error: {availabilityError} <button onClick={fetchAvailability} disabled={isAvailabilityLoading}>Retry</button></p>;
  } else {
    availabilityContent = (
      <AvailabilityScheduleManager
        currentAvailability={availability}
        onSubmit={handleAvailabilityUpdate}
        isSubmitting={isSubmittingAvailability}
      />
    );
  }

  return (
    // Wrap with ProtectedRoute to ensure only artists/braiders can access
    // Adjust allowedRoles as necessary.
    <ProtectedRoute allowedRoles={['artist', 'braider']}>
      <AdminLayout title="My Profile"> {/* Or a more generic ArtistLayout if it exists */}
        <Head>
          <title>My Profile | Artist Dashboard</title>
        </Head>
        <div style={{ padding: '20px' }}> {/* Basic styling */}
          <h1>My Profile</h1>
          {profileContent}

          <hr style={{ margin: '40px 0' }} /> {/* Separator */}

          <div id="availability-schedule"> {/* Added ID here */}
            <h2>Manage Weekly Availability</h2>
            {availabilityContent}
          </div>

          <hr style={{ margin: '40px 0' }} /> {/* Separator */}

          <div id="service-specializations"> {/* Added ID for potential direct linking */}
            <h2>Manage Service Specializations</h2>
            {/*
              No separate loading for specializations list as it's fetched within the component.
              The `profileData.specializations` are passed as current selections.
            */}
            {profileData && ( /* Ensure profileData is loaded before rendering this */
              <ServiceSpecializationManager
                currentSpecializations={profileData.specializations || []}
                onSubmit={handleSpecializationUpdate}
                isSubmitting={isSubmittingSpecializations}
              />
            )}
          </div>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}
