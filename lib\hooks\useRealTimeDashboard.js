/**
 * Real-time Dashboard Hook
 * Ocean Soul Sparkles - Artist Dashboard Real-time Features
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { getAuthToken } from '@/lib/auth-token-manager'
import websocketClient from '@/lib/websocket-client'
import { toast } from 'react-toastify'

export function useRealTimeDashboard(options = {}) {
  const {
    autoConnect = true,
    fallbackRefreshInterval = 5 * 60 * 1000, // 5 minutes
    enableNotifications = true
  } = options

  const { user, role } = useAuth()
  const [connectionStatus, setConnectionStatus] = useState('disconnected')
  const [dashboardData, setDashboardData] = useState(null)
  const [lastUpdated, setLastUpdated] = useState(null)
  const [error, setError] = useState(null)
  
  const fallbackIntervalRef = useRef(null)
  const reconnectTimeoutRef = useRef(null)

  /**
   * Initialize WebSocket connection
   */
  const connect = useCallback(async () => {
    if (!user?.id || !role || !['artist', 'braider'].includes(role)) {
      console.log('[useRealTimeDashboard] User not ready for WebSocket connection')
      return
    }

    try {
      const token = await getAuthToken()
      if (!token) {
        throw new Error('No authentication token available')
      }

      console.log('[useRealTimeDashboard] Connecting to WebSocket...')
      websocketClient.connect(user.id, role, token)
      
    } catch (error) {
      console.error('[useRealTimeDashboard] Connection error:', error)
      setError(error.message)
      setConnectionStatus('error')
    }
  }, [user?.id, role])

  /**
   * Disconnect WebSocket
   */
  const disconnect = useCallback(() => {
    console.log('[useRealTimeDashboard] Disconnecting...')
    websocketClient.disconnect()
    
    // Clear fallback interval
    if (fallbackIntervalRef.current) {
      clearInterval(fallbackIntervalRef.current)
      fallbackIntervalRef.current = null
    }
    
    // Clear reconnect timeout
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }
  }, [])

  /**
   * Request manual refresh
   */
  const refresh = useCallback(() => {
    console.log('[useRealTimeDashboard] Manual refresh requested')
    
    if (connectionStatus === 'connected') {
      websocketClient.requestRefresh()
    } else {
      // Fallback to HTTP request
      fetchDashboardDataHTTP()
    }
  }, [connectionStatus])

  /**
   * Fallback HTTP data fetch
   */
  const fetchDashboardDataHTTP = useCallback(async () => {
    try {
      const token = await getAuthToken()
      const response = await fetch('/api/artist/dashboard-enhanced', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        setDashboardData(data)
        setLastUpdated(new Date())
        setError(null)
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (error) {
      console.error('[useRealTimeDashboard] HTTP fetch error:', error)
      setError(error.message)
    }
  }, [])

  /**
   * Setup WebSocket event listeners
   */
  useEffect(() => {
    // Connection status updates
    const handleConnection = (data) => {
      console.log('[useRealTimeDashboard] Connection status:', data.status)
      setConnectionStatus(data.status)
      
      if (data.status === 'connected') {
        setError(null)
        // Subscribe to dashboard updates
        websocketClient.subscribe(['bookings', 'availability', 'metrics'])
        
        // Clear fallback interval since WebSocket is working
        if (fallbackIntervalRef.current) {
          clearInterval(fallbackIntervalRef.current)
          fallbackIntervalRef.current = null
        }
      } else if (data.status === 'disconnected') {
        // Start fallback polling
        startFallbackPolling()
      }
    }

    // Dashboard data updates
    const handleDashboardUpdate = (data) => {
      console.log('[useRealTimeDashboard] Dashboard update received')
      setDashboardData(data.data || data)
      setLastUpdated(new Date())
      setError(null)
    }

    // Booking notifications
    const handleBookingNotification = (data) => {
      console.log('[useRealTimeDashboard] Booking notification:', data)
      
      if (enableNotifications) {
        const { type, booking } = data
        
        switch (type) {
          case 'new_booking':
            toast.success(`New booking: ${booking.service_name} at ${new Date(booking.start_time).toLocaleTimeString()}`)
            break
          case 'booking_cancelled':
            toast.warning(`Booking cancelled: ${booking.service_name}`)
            break
          case 'booking_updated':
            toast.info(`Booking updated: ${booking.service_name}`)
            break
          case 'booking_reminder':
            toast.info(`Reminder: ${booking.service_name} in 10 minutes`)
            break
        }
      }
      
      // Refresh dashboard data
      refresh()
    }

    // Availability updates
    const handleAvailabilityUpdate = (data) => {
      console.log('[useRealTimeDashboard] Availability update:', data)
      
      // Update dashboard data with new availability
      setDashboardData(prev => prev ? {
        ...prev,
        profile: {
          ...prev.profile,
          is_available_today: data.is_available_today
        }
      } : null)
    }

    // Performance metrics updates
    const handlePerformanceUpdate = (data) => {
      console.log('[useRealTimeDashboard] Performance update:', data)
      
      // Update dashboard data with new metrics
      setDashboardData(prev => prev ? {
        ...prev,
        stats: {
          ...prev.stats,
          ...data
        }
      } : null)
    }

    // Reconnection needed
    const handleReconnectNeeded = async (data) => {
      console.log('[useRealTimeDashboard] Reconnection needed, attempt:', data.attempt)
      
      // Wait a bit then try to reconnect
      reconnectTimeoutRef.current = setTimeout(() => {
        connect()
      }, 2000)
    }

    // Max reconnect attempts reached
    const handleMaxReconnectAttempts = () => {
      console.warn('[useRealTimeDashboard] Max reconnection attempts reached')
      setError('Connection lost. Please refresh the page.')
      toast.error('Connection lost. Please refresh the page.')
      
      // Start fallback polling
      startFallbackPolling()
    }

    // Error handling
    const handleError = (error) => {
      console.error('[useRealTimeDashboard] WebSocket error:', error)
      setError(error.message || 'Connection error')
    }

    // Add event listeners
    websocketClient.on('connection', handleConnection)
    websocketClient.on('dashboardUpdate', handleDashboardUpdate)
    websocketClient.on('bookingNotification', handleBookingNotification)
    websocketClient.on('availabilityUpdate', handleAvailabilityUpdate)
    websocketClient.on('performanceUpdate', handlePerformanceUpdate)
    websocketClient.on('reconnectNeeded', handleReconnectNeeded)
    websocketClient.on('maxReconnectAttemptsReached', handleMaxReconnectAttempts)
    websocketClient.on('error', handleError)

    // Cleanup function
    return () => {
      websocketClient.off('connection', handleConnection)
      websocketClient.off('dashboardUpdate', handleDashboardUpdate)
      websocketClient.off('bookingNotification', handleBookingNotification)
      websocketClient.off('availabilityUpdate', handleAvailabilityUpdate)
      websocketClient.off('performanceUpdate', handlePerformanceUpdate)
      websocketClient.off('reconnectNeeded', handleReconnectNeeded)
      websocketClient.off('maxReconnectAttemptsReached', handleMaxReconnectAttempts)
      websocketClient.off('error', handleError)
    }
  }, [connect, refresh, enableNotifications])

  /**
   * Start fallback polling when WebSocket is not available
   */
  const startFallbackPolling = useCallback(() => {
    if (fallbackIntervalRef.current) return // Already polling
    
    console.log('[useRealTimeDashboard] Starting fallback polling')
    
    // Initial fetch
    fetchDashboardDataHTTP()
    
    // Set up interval
    fallbackIntervalRef.current = setInterval(() => {
      fetchDashboardDataHTTP()
    }, fallbackRefreshInterval)
  }, [fetchDashboardDataHTTP, fallbackRefreshInterval])

  /**
   * Auto-connect on mount
   */
  useEffect(() => {
    if (autoConnect && user?.id && role) {
      connect()
    }

    // Cleanup on unmount
    return () => {
      disconnect()
    }
  }, [autoConnect, user?.id, role, connect, disconnect])

  return {
    // Connection state
    connectionStatus,
    isConnected: connectionStatus === 'connected',
    error,
    
    // Data
    dashboardData,
    lastUpdated,
    
    // Actions
    connect,
    disconnect,
    refresh,
    
    // WebSocket client for advanced usage
    websocketClient
  }
}
