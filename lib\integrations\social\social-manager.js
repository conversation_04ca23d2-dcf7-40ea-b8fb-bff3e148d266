/**
 * Social Media Manager for Ocean Soul Sparkles
 * Universal social media interface supporting multiple providers
 * 
 * Phase 7.3: Social Media Integration Layer
 */

import InstagramBusinessClient from './instagram-api'
import FacebookBusinessClient from './facebook-api'
import { AuditLogger } from '../security-utils'
import { getIntegrationSettings, updateIntegrationSettings } from '@/lib/supabase'

/**
 * Supported Social Media Providers
 */
const SOCIAL_PROVIDERS = {
  instagram_business: {
    name: 'Instagram Business',
    clientClass: InstagramBusinessClient,
    features: ['posts', 'stories', 'analytics', 'hashtags', 'portfolio_sync']
  },
  facebook_business: {
    name: 'Facebook Business',
    clientClass: FacebookBusinessClient,
    features: ['posts', 'events', 'analytics', 'page_management', 'promotion']
  }
  // Future providers can be added here:
  // tiktok: { name: 'TikTok Business', clientClass: TikTokBusinessClient },
  // linkedin: { name: 'LinkedIn Business', clientClass: LinkedInBusinessClient }
}

/**
 * Social Media Manager Class
 * Provides unified interface for social media operations across providers
 */
export class SocialMediaManager {
  constructor(userId) {
    this.userId = userId
    this.clients = new Map()
  }

  /**
   * Get social media client for a specific provider
   */
  async getClient(provider) {
    if (!SOCIAL_PROVIDERS[provider]) {
      throw new Error(`Unsupported social media provider: ${provider}`)
    }

    // Return cached client if available
    if (this.clients.has(provider)) {
      return this.clients.get(provider)
    }

    // Create new client
    const ClientClass = SOCIAL_PROVIDERS[provider].clientClass
    const client = new ClientClass(this.userId)

    // Initialize client
    const initialized = await client.initialize()
    if (!initialized) {
      throw new Error(`Failed to initialize ${provider} client`)
    }

    // Cache client
    this.clients.set(provider, client)
    return client
  }

  /**
   * Get all connected social media providers for user
   */
  async getConnectedProviders() {
    const settings = await getIntegrationSettings(this.userId)
    
    return settings
      .filter(setting => 
        SOCIAL_PROVIDERS[setting.provider] && 
        setting.enabled
      )
      .map(setting => ({
        provider: setting.provider,
        name: SOCIAL_PROVIDERS[setting.provider].name,
        features: SOCIAL_PROVIDERS[setting.provider].features,
        settings: setting.settings
      }))
  }

  /**
   * Get profiles from all connected providers
   */
  async getAllProfiles() {
    const connectedProviders = await this.getConnectedProviders()
    const allProfiles = []

    for (const providerInfo of connectedProviders) {
      try {
        const client = await this.getClient(providerInfo.provider)
        
        let profile
        if (providerInfo.provider === 'instagram_business') {
          profile = await client.getProfile()
        } else if (providerInfo.provider === 'facebook_business') {
          profile = await client.getPageProfile()
        }
        
        if (profile) {
          allProfiles.push({
            ...profile,
            provider: providerInfo.provider,
            providerName: providerInfo.name
          })
        }
        
      } catch (error) {
        console.error(`Failed to get profile from ${providerInfo.provider}:`, error)
        
        await AuditLogger.logIntegrationActivity(
          this.userId,
          providerInfo.provider,
          'get_profile_failed',
          'error',
          { error: error.message }
        )
      }
    }

    return allProfiles
  }

  /**
   * Get posts from all connected providers
   */
  async getAllPosts(limit = 25) {
    const connectedProviders = await this.getConnectedProviders()
    const allPosts = []

    for (const providerInfo of connectedProviders) {
      try {
        const client = await this.getClient(providerInfo.provider)
        
        let posts
        if (providerInfo.provider === 'instagram_business') {
          const result = await client.getMedia(limit)
          posts = result.media
        } else if (providerInfo.provider === 'facebook_business') {
          const result = await client.getPosts(limit)
          posts = result.posts
        }
        
        if (posts) {
          const providerPosts = posts.map(post => ({
            ...post,
            provider: providerInfo.provider,
            providerName: providerInfo.name
          }))
          
          allPosts.push(...providerPosts)
        }
        
      } catch (error) {
        console.error(`Failed to get posts from ${providerInfo.provider}:`, error)
        
        await AuditLogger.logIntegrationActivity(
          this.userId,
          providerInfo.provider,
          'get_posts_failed',
          'error',
          { error: error.message }
        )
      }
    }

    // Sort posts by timestamp (newest first)
    return allPosts.sort((a, b) => new Date(b.timestamp || b.createdTime) - new Date(a.timestamp || a.createdTime))
  }

  /**
   * Create post on specified provider
   */
  async createPost(provider, postData) {
    const client = await this.getClient(provider)
    
    if (provider === 'instagram_business') {
      return await client.createMedia(postData)
    } else if (provider === 'facebook_business') {
      return await client.createPost(postData)
    }
    
    throw new Error(`Post creation not supported for provider: ${provider}`)
  }

  /**
   * Create post on multiple providers
   */
  async createMultiProviderPost(providers, postData) {
    const results = []

    for (const provider of providers) {
      try {
        const result = await this.createPost(provider, postData)
        results.push({
          provider,
          success: true,
          result
        })

        await AuditLogger.logIntegrationActivity(
          this.userId,
          provider,
          'multi_post_success',
          'success',
          { postId: result.id }
        )

      } catch (error) {
        console.error(`Failed to create post on ${provider}:`, error)
        
        results.push({
          provider,
          success: false,
          error: error.message
        })

        await AuditLogger.logIntegrationActivity(
          this.userId,
          provider,
          'multi_post_failed',
          'error',
          { error: error.message }
        )
      }
    }

    return results
  }

  /**
   * Get analytics from all connected providers
   */
  async getAllAnalytics(period = 'day') {
    const connectedProviders = await this.getConnectedProviders()
    const allAnalytics = {}

    for (const providerInfo of connectedProviders) {
      try {
        const client = await this.getClient(providerInfo.provider)
        const insights = await client.getInsights(null, period)
        
        allAnalytics[providerInfo.provider] = {
          provider: providerInfo.provider,
          providerName: providerInfo.name,
          insights,
          period
        }
        
      } catch (error) {
        console.error(`Failed to get analytics from ${providerInfo.provider}:`, error)
        
        allAnalytics[providerInfo.provider] = {
          provider: providerInfo.provider,
          providerName: providerInfo.name,
          error: error.message
        }
      }
    }

    return allAnalytics
  }

  /**
   * Sync Ocean Soul Sparkles portfolio to social media
   */
  async syncPortfolioToSocial(portfolioItems, settings = {}) {
    const connectedProviders = await this.getConnectedProviders()
    const syncResults = []

    for (const providerInfo of connectedProviders) {
      try {
        const providerSettings = settings[providerInfo.provider] || {}
        
        // Check if auto-sync is enabled for this provider
        if (!providerSettings.autoSync) {
          continue
        }

        const client = await this.getClient(providerInfo.provider)
        
        for (const item of portfolioItems) {
          try {
            // Format portfolio item for social media
            const postData = this.formatPortfolioItemForSocial(item, providerInfo.provider, providerSettings)
            
            let result
            if (providerInfo.provider === 'instagram_business') {
              result = await client.createMedia(postData)
            } else if (providerInfo.provider === 'facebook_business') {
              result = await client.createPost(postData)
            }

            syncResults.push({
              provider: providerInfo.provider,
              portfolioItemId: item.id,
              success: true,
              postId: result.id
            })

          } catch (error) {
            console.error(`Failed to sync portfolio item ${item.id} to ${providerInfo.provider}:`, error)
            
            syncResults.push({
              provider: providerInfo.provider,
              portfolioItemId: item.id,
              success: false,
              error: error.message
            })
          }
        }

        await AuditLogger.logIntegrationActivity(
          this.userId,
          providerInfo.provider,
          'portfolio_sync_completed',
          'success',
          { 
            itemCount: portfolioItems.length,
            successCount: syncResults.filter(r => r.success && r.provider === providerInfo.provider).length
          }
        )

      } catch (error) {
        console.error(`Failed to sync portfolio to ${providerInfo.provider}:`, error)
        
        await AuditLogger.logIntegrationActivity(
          this.userId,
          providerInfo.provider,
          'portfolio_sync_failed',
          'error',
          { error: error.message }
        )
      }
    }

    return syncResults
  }

  /**
   * Format portfolio item for social media posting
   */
  formatPortfolioItemForSocial(portfolioItem, provider, settings = {}) {
    const baseCaption = `${portfolioItem.title || 'Beautiful work'} by Ocean Soul Sparkles ✨\n\n${portfolioItem.description || ''}`
    
    // Add hashtags if configured
    const hashtags = settings.hashtags || ['#OceanSoulSparkles', '#BeautyArt', '#Braiding', '#HairArt']
    const caption = `${baseCaption}\n\n${hashtags.join(' ')}`

    if (provider === 'instagram_business') {
      return {
        imageUrl: portfolioItem.imageUrl,
        caption: caption.substring(0, 2200), // Instagram caption limit
        locationId: settings.locationId
      }
    } else if (provider === 'facebook_business') {
      return {
        message: caption,
        photoUrl: portfolioItem.imageUrl,
        link: portfolioItem.portfolioUrl
      }
    }

    return {
      caption,
      imageUrl: portfolioItem.imageUrl
    }
  }

  /**
   * Test connections to all social media providers
   */
  async testAllConnections() {
    const connectedProviders = await this.getConnectedProviders()
    const testResults = []

    for (const providerInfo of connectedProviders) {
      try {
        const client = await this.getClient(providerInfo.provider)
        const result = await client.testConnection()
        
        testResults.push({
          provider: providerInfo.provider,
          name: providerInfo.name,
          ...result
        })
      } catch (error) {
        testResults.push({
          provider: providerInfo.provider,
          name: providerInfo.name,
          success: false,
          error: error.message
        })
      }
    }

    return testResults
  }

  /**
   * Get available social media providers
   */
  static getAvailableProviders() {
    return Object.keys(SOCIAL_PROVIDERS).map(key => ({
      id: key,
      name: SOCIAL_PROVIDERS[key].name,
      features: SOCIAL_PROVIDERS[key].features
    }))
  }
}

export default SocialMediaManager
