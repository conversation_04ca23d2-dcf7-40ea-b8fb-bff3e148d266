/* PWA Install Prompt Styles */

.container {
  position: fixed;
  z-index: 9999;
  pointer-events: none;
}

/* Position variants */
.container.bottom-right {
  bottom: 20px;
  right: 20px;
}

.container.bottom-left {
  bottom: 20px;
  left: 20px;
}

.container.bottom-center {
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
}

.container.top-right {
  top: 20px;
  right: 20px;
}

.container.top-left {
  top: 20px;
  left: 20px;
}

.container.top-center {
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
}

.prompt {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(12px);
  border-radius: 16px;
  padding: 20px;
  max-width: 320px;
  box-shadow: 
    0 10px 25px rgba(0, 0, 0, 0.1),
    0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  pointer-events: auto;
  animation: slideIn 0.3s ease-out;
  position: relative;
}

.closeButton {
  position: absolute;
  top: 12px;
  right: 12px;
  background: none;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.closeButton:hover {
  background: rgba(107, 114, 128, 0.1);
  color: #374151;
}

.icon {
  text-align: center;
  margin-bottom: 16px;
}

.appIcon {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.content {
  margin-bottom: 20px;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  text-align: center;
}

.description {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
  margin: 0 0 16px 0;
  text-align: center;
}

.features {
  list-style: none;
  padding: 0;
  margin: 0;
}

.features li {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 0;
  font-size: 13px;
  color: #4b5563;
}

.featureIcon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.installButton {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-height: 44px;
}

.installButton:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.installButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.laterButton {
  background: transparent;
  color: #6b7280;
  border: 1px solid rgba(107, 114, 128, 0.3);
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.laterButton:hover {
  background: rgba(107, 114, 128, 0.05);
  color: #374151;
}

.installIcon {
  font-size: 16px;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.instructions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(107, 114, 128, 0.2);
}

.instructionText {
  font-size: 12px;
  color: #6b7280;
  text-align: center;
  margin: 0;
  font-style: italic;
}

/* Status Indicator */
.statusIndicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.statusDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.statusDot.online {
  background: #10b981;
}

.statusDot.offline {
  background: #ef4444;
}

.statusDetails {
  display: flex;
  align-items: center;
  gap: 8px;
}

.statusText {
  font-size: 12px;
  color: #374151;
  font-weight: 500;
}

.updateButton {
  background: #f59e0b;
  color: white;
  border: none;
  border-radius: 12px;
  padding: 4px 8px;
  font-size: 11px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.updateButton:hover {
  background: #d97706;
  transform: translateY(-1px);
}

/* Install Instructions */
.installInstructions {
  background: rgba(249, 250, 251, 0.8);
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
}

.installInstructions h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.installInstructions ol {
  margin: 0;
  padding-left: 20px;
}

.installInstructions li {
  font-size: 13px;
  color: #6b7280;
  line-height: 1.4;
  margin-bottom: 4px;
}

/* Animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .container {
    left: 16px !important;
    right: 16px !important;
    transform: none !important;
  }
  
  .container.bottom-center,
  .container.bottom-left,
  .container.bottom-right {
    bottom: 16px;
  }
  
  .container.top-center,
  .container.top-left,
  .container.top-right {
    top: 16px;
  }
  
  .prompt {
    max-width: none;
    margin: 0;
  }
  
  .title {
    font-size: 16px;
  }
  
  .description {
    font-size: 13px;
  }
  
  .features li {
    font-size: 12px;
  }
  
  .installButton,
  .laterButton {
    font-size: 13px;
    padding: 10px 14px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .prompt {
    background: rgba(31, 41, 55, 0.98);
    border: 1px solid rgba(75, 85, 99, 0.3);
  }
  
  .title {
    color: #f9fafb;
  }
  
  .description {
    color: #d1d5db;
  }
  
  .features li {
    color: #9ca3af;
  }
  
  .closeButton {
    color: #9ca3af;
  }
  
  .closeButton:hover {
    background: rgba(156, 163, 175, 0.1);
    color: #d1d5db;
  }
  
  .laterButton {
    color: #9ca3af;
    border-color: rgba(156, 163, 175, 0.3);
  }
  
  .laterButton:hover {
    background: rgba(156, 163, 175, 0.1);
    color: #d1d5db;
  }
  
  .statusIndicator {
    background: rgba(31, 41, 55, 0.9);
    border: 1px solid rgba(75, 85, 99, 0.3);
  }
  
  .statusText {
    color: #d1d5db;
  }
  
  .installInstructions {
    background: rgba(17, 24, 39, 0.8);
  }
  
  .installInstructions h4 {
    color: #f9fafb;
  }
  
  .installInstructions li {
    color: #d1d5db;
  }
}
