import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { authenticatedFetch } from '@/lib/auth-utils';
import { toast } from 'react-toastify';
import styles from '@/styles/admin/NotificationSettings.module.css';

/**
 * Comprehensive Notification Settings Component
 * Provides granular control over notification preferences for artists and customers
 */
export default function NotificationSettings({ userId, userRole = 'artist', onSettingsChange }) {
  const [preferences, setPreferences] = useState({
    email_notifications: true,
    sms_notifications: false,
    push_notifications: true,
    booking_reminders: true,
    reminder_minutes: 10,
    payment_notifications: true,
    event_notifications: true,
    marketing_notifications: false,
    phone_number: '',
    
    // Advanced preferences
    booking_alerts: {
      new_booking: true,
      booking_changes: true,
      cancellations: true,
      customer_inquiries: true,
      schedule_conflicts: true
    },
    
    revenue_alerts: {
      daily_summary: false,
      weekly_summary: true,
      milestone_notifications: true,
      payment_received: true
    },
    
    system_alerts: {
      maintenance_notifications: true,
      security_alerts: true,
      feature_updates: false,
      emergency_broadcasts: true
    },
    
    quiet_hours: {
      enabled: false,
      start_time: '22:00',
      end_time: '08:00',
      timezone: 'Australia/Sydney'
    },
    
    notification_channels: {
      urgent: ['push', 'email'],
      normal: ['push'],
      marketing: ['email']
    }
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testingNotification, setTestingNotification] = useState(false);
  const [activeTab, setActiveTab] = useState('general');

  useEffect(() => {
    loadNotificationPreferences();
  }, [userId]);

  const loadNotificationPreferences = async () => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('notification_preferences')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      if (data) {
        // Merge database preferences with default structure
        setPreferences(prev => ({
          ...prev,
          ...data,
          booking_alerts: data.booking_alerts || prev.booking_alerts,
          revenue_alerts: data.revenue_alerts || prev.revenue_alerts,
          system_alerts: data.system_alerts || prev.system_alerts,
          quiet_hours: data.quiet_hours || prev.quiet_hours,
          notification_channels: data.notification_channels || prev.notification_channels
        }));
      }
    } catch (error) {
      console.error('Error loading notification preferences:', error);
      toast.error('Failed to load notification preferences');
    } finally {
      setLoading(false);
    }
  };

  const savePreferences = async () => {
    try {
      setSaving(true);

      const { error } = await supabase
        .from('notification_preferences')
        .upsert({
          user_id: userId,
          ...preferences,
          updated_at: new Date().toISOString()
        });

      if (error) throw error;

      toast.success('Notification preferences saved successfully');
      
      if (onSettingsChange) {
        onSettingsChange(preferences);
      }
    } catch (error) {
      console.error('Error saving notification preferences:', error);
      toast.error('Failed to save notification preferences');
    } finally {
      setSaving(false);
    }
  };

  const testNotification = async () => {
    try {
      setTestingNotification(true);

      const response = await authenticatedFetch('/api/notifications/test', {
        method: 'POST',
        body: JSON.stringify({
          user_id: userId,
          title: 'Test Notification',
          message: 'This is a test notification to verify your settings are working correctly.',
          channels: preferences.notification_channels.normal
        })
      });

      if (response.success) {
        toast.success('Test notification sent! Check your devices.');
      } else {
        throw new Error(response.error || 'Failed to send test notification');
      }
    } catch (error) {
      console.error('Error sending test notification:', error);
      toast.error('Failed to send test notification');
    } finally {
      setTestingNotification(false);
    }
  };

  const updatePreference = (key, value) => {
    setPreferences(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const updateNestedPreference = (category, key, value) => {
    setPreferences(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }));
  };

  const updateChannelPreference = (priority, channels) => {
    setPreferences(prev => ({
      ...prev,
      notification_channels: {
        ...prev.notification_channels,
        [priority]: channels
      }
    }));
  };

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.spinner}></div>
        <p>Loading notification preferences...</p>
      </div>
    );
  }

  return (
    <div className={styles.notificationSettings}>
      <div className={styles.header}>
        <h2>Notification Preferences</h2>
        <p>Customize how and when you receive notifications</p>
      </div>

      <div className={styles.tabs}>
        <button
          className={`${styles.tab} ${activeTab === 'general' ? styles.active : ''}`}
          onClick={() => setActiveTab('general')}
        >
          General
        </button>
        <button
          className={`${styles.tab} ${activeTab === 'booking' ? styles.active : ''}`}
          onClick={() => setActiveTab('booking')}
        >
          Booking Alerts
        </button>
        {userRole === 'artist' && (
          <button
            className={`${styles.tab} ${activeTab === 'revenue' ? styles.active : ''}`}
            onClick={() => setActiveTab('revenue')}
          >
            Revenue Alerts
          </button>
        )}
        <button
          className={`${styles.tab} ${activeTab === 'system' ? styles.active : ''}`}
          onClick={() => setActiveTab('system')}
        >
          System Alerts
        </button>
        <button
          className={`${styles.tab} ${activeTab === 'advanced' ? styles.active : ''}`}
          onClick={() => setActiveTab('advanced')}
        >
          Advanced
        </button>
      </div>

      <div className={styles.tabContent}>
        {activeTab === 'general' && (
          <div className={styles.section}>
            <h3>General Notification Settings</h3>
            
            <div className={styles.settingGroup}>
              <label className={styles.toggleLabel}>
                <input
                  type="checkbox"
                  checked={preferences.email_notifications}
                  onChange={(e) => updatePreference('email_notifications', e.target.checked)}
                />
                <span className={styles.toggle}></span>
                Email Notifications
              </label>
              <p className={styles.description}>Receive notifications via email</p>
            </div>

            <div className={styles.settingGroup}>
              <label className={styles.toggleLabel}>
                <input
                  type="checkbox"
                  checked={preferences.push_notifications}
                  onChange={(e) => updatePreference('push_notifications', e.target.checked)}
                />
                <span className={styles.toggle}></span>
                Push Notifications
              </label>
              <p className={styles.description}>Receive instant push notifications on your devices</p>
            </div>

            <div className={styles.settingGroup}>
              <label className={styles.toggleLabel}>
                <input
                  type="checkbox"
                  checked={preferences.sms_notifications}
                  onChange={(e) => updatePreference('sms_notifications', e.target.checked)}
                />
                <span className={styles.toggle}></span>
                SMS Notifications
              </label>
              <p className={styles.description}>Receive notifications via text message</p>
              
              {preferences.sms_notifications && (
                <div className={styles.phoneInput}>
                  <label>Phone Number:</label>
                  <input
                    type="tel"
                    value={preferences.phone_number}
                    onChange={(e) => updatePreference('phone_number', e.target.value)}
                    placeholder="+61 4XX XXX XXX"
                  />
                </div>
              )}
            </div>

            <div className={styles.settingGroup}>
              <label className={styles.toggleLabel}>
                <input
                  type="checkbox"
                  checked={preferences.booking_reminders}
                  onChange={(e) => updatePreference('booking_reminders', e.target.checked)}
                />
                <span className={styles.toggle}></span>
                Booking Reminders
              </label>
              <p className={styles.description}>Receive reminders before your appointments</p>
              
              {preferences.booking_reminders && (
                <div className={styles.reminderTime}>
                  <label>Reminder Time:</label>
                  <select
                    value={preferences.reminder_minutes}
                    onChange={(e) => updatePreference('reminder_minutes', parseInt(e.target.value))}
                  >
                    <option value={5}>5 minutes before</option>
                    <option value={10}>10 minutes before</option>
                    <option value={15}>15 minutes before</option>
                    <option value={30}>30 minutes before</option>
                    <option value={60}>1 hour before</option>
                  </select>
                </div>
              )}
            </div>

            <div className={styles.settingGroup}>
              <label className={styles.toggleLabel}>
                <input
                  type="checkbox"
                  checked={preferences.marketing_notifications}
                  onChange={(e) => updatePreference('marketing_notifications', e.target.checked)}
                />
                <span className={styles.toggle}></span>
                Marketing & Promotions
              </label>
              <p className={styles.description}>Receive updates about special offers and promotions</p>
            </div>
          </div>
        )}

        {activeTab === 'booking' && (
          <div className={styles.section}>
            <h3>Booking Alert Settings</h3>

            <div className={styles.settingGroup}>
              <label className={styles.toggleLabel}>
                <input
                  type="checkbox"
                  checked={preferences.booking_alerts.new_booking}
                  onChange={(e) => updateNestedPreference('booking_alerts', 'new_booking', e.target.checked)}
                />
                <span className={styles.toggle}></span>
                New Booking Alerts
              </label>
              <p className={styles.description}>Get notified immediately when you receive a new booking</p>
            </div>

            <div className={styles.settingGroup}>
              <label className={styles.toggleLabel}>
                <input
                  type="checkbox"
                  checked={preferences.booking_alerts.booking_changes}
                  onChange={(e) => updateNestedPreference('booking_alerts', 'booking_changes', e.target.checked)}
                />
                <span className={styles.toggle}></span>
                Booking Changes
              </label>
              <p className={styles.description}>Receive alerts when bookings are modified or rescheduled</p>
            </div>

            <div className={styles.settingGroup}>
              <label className={styles.toggleLabel}>
                <input
                  type="checkbox"
                  checked={preferences.booking_alerts.cancellations}
                  onChange={(e) => updateNestedPreference('booking_alerts', 'cancellations', e.target.checked)}
                />
                <span className={styles.toggle}></span>
                Cancellation Alerts
              </label>
              <p className={styles.description}>Get notified when bookings are cancelled</p>
            </div>

            <div className={styles.settingGroup}>
              <label className={styles.toggleLabel}>
                <input
                  type="checkbox"
                  checked={preferences.booking_alerts.customer_inquiries}
                  onChange={(e) => updateNestedPreference('booking_alerts', 'customer_inquiries', e.target.checked)}
                />
                <span className={styles.toggle}></span>
                Customer Inquiries
              </label>
              <p className={styles.description}>Receive notifications for new customer messages and inquiries</p>
            </div>

            <div className={styles.settingGroup}>
              <label className={styles.toggleLabel}>
                <input
                  type="checkbox"
                  checked={preferences.booking_alerts.schedule_conflicts}
                  onChange={(e) => updateNestedPreference('booking_alerts', 'schedule_conflicts', e.target.checked)}
                />
                <span className={styles.toggle}></span>
                Schedule Conflict Warnings
              </label>
              <p className={styles.description}>Get warned about potential scheduling conflicts</p>
            </div>
          </div>
        )}

        {activeTab === 'revenue' && userRole === 'artist' && (
          <div className={styles.section}>
            <h3>Revenue Alert Settings</h3>

            <div className={styles.settingGroup}>
              <label className={styles.toggleLabel}>
                <input
                  type="checkbox"
                  checked={preferences.revenue_alerts.payment_received}
                  onChange={(e) => updateNestedPreference('revenue_alerts', 'payment_received', e.target.checked)}
                />
                <span className={styles.toggle}></span>
                Payment Received
              </label>
              <p className={styles.description}>Get notified when payments are received</p>
            </div>

            <div className={styles.settingGroup}>
              <label className={styles.toggleLabel}>
                <input
                  type="checkbox"
                  checked={preferences.revenue_alerts.daily_summary}
                  onChange={(e) => updateNestedPreference('revenue_alerts', 'daily_summary', e.target.checked)}
                />
                <span className={styles.toggle}></span>
                Daily Revenue Summary
              </label>
              <p className={styles.description}>Receive daily earnings summaries</p>
            </div>

            <div className={styles.settingGroup}>
              <label className={styles.toggleLabel}>
                <input
                  type="checkbox"
                  checked={preferences.revenue_alerts.weekly_summary}
                  onChange={(e) => updateNestedPreference('revenue_alerts', 'weekly_summary', e.target.checked)}
                />
                <span className={styles.toggle}></span>
                Weekly Revenue Summary
              </label>
              <p className={styles.description}>Receive weekly earnings reports</p>
            </div>

            <div className={styles.settingGroup}>
              <label className={styles.toggleLabel}>
                <input
                  type="checkbox"
                  checked={preferences.revenue_alerts.milestone_notifications}
                  onChange={(e) => updateNestedPreference('revenue_alerts', 'milestone_notifications', e.target.checked)}
                />
                <span className={styles.toggle}></span>
                Milestone Notifications
              </label>
              <p className={styles.description}>Get notified when you reach revenue milestones</p>
            </div>
          </div>
        )}

        {activeTab === 'system' && (
          <div className={styles.section}>
            <h3>System Alert Settings</h3>

            <div className={styles.settingGroup}>
              <label className={styles.toggleLabel}>
                <input
                  type="checkbox"
                  checked={preferences.system_alerts.emergency_broadcasts}
                  onChange={(e) => updateNestedPreference('system_alerts', 'emergency_broadcasts', e.target.checked)}
                />
                <span className={styles.toggle}></span>
                Emergency Broadcasts
              </label>
              <p className={styles.description}>Receive critical emergency notifications (cannot be disabled)</p>
            </div>

            <div className={styles.settingGroup}>
              <label className={styles.toggleLabel}>
                <input
                  type="checkbox"
                  checked={preferences.system_alerts.maintenance_notifications}
                  onChange={(e) => updateNestedPreference('system_alerts', 'maintenance_notifications', e.target.checked)}
                />
                <span className={styles.toggle}></span>
                Maintenance Notifications
              </label>
              <p className={styles.description}>Get notified about scheduled system maintenance</p>
            </div>

            <div className={styles.settingGroup}>
              <label className={styles.toggleLabel}>
                <input
                  type="checkbox"
                  checked={preferences.system_alerts.security_alerts}
                  onChange={(e) => updateNestedPreference('system_alerts', 'security_alerts', e.target.checked)}
                />
                <span className={styles.toggle}></span>
                Security Alerts
              </label>
              <p className={styles.description}>Receive notifications about security-related events</p>
            </div>

            <div className={styles.settingGroup}>
              <label className={styles.toggleLabel}>
                <input
                  type="checkbox"
                  checked={preferences.system_alerts.feature_updates}
                  onChange={(e) => updateNestedPreference('system_alerts', 'feature_updates', e.target.checked)}
                />
                <span className={styles.toggle}></span>
                Feature Updates
              </label>
              <p className={styles.description}>Get notified about new features and improvements</p>
            </div>
          </div>
        )}

        {activeTab === 'advanced' && (
          <div className={styles.section}>
            <h3>Advanced Settings</h3>

            <div className={styles.settingGroup}>
              <h4>Quiet Hours</h4>
              <label className={styles.toggleLabel}>
                <input
                  type="checkbox"
                  checked={preferences.quiet_hours.enabled}
                  onChange={(e) => updateNestedPreference('quiet_hours', 'enabled', e.target.checked)}
                />
                <span className={styles.toggle}></span>
                Enable Quiet Hours
              </label>
              <p className={styles.description}>Suppress non-urgent notifications during specified hours</p>

              {preferences.quiet_hours.enabled && (
                <div className={styles.quietHoursConfig}>
                  <div className={styles.timeInputs}>
                    <div>
                      <label>Start Time:</label>
                      <input
                        type="time"
                        value={preferences.quiet_hours.start_time}
                        onChange={(e) => updateNestedPreference('quiet_hours', 'start_time', e.target.value)}
                      />
                    </div>
                    <div>
                      <label>End Time:</label>
                      <input
                        type="time"
                        value={preferences.quiet_hours.end_time}
                        onChange={(e) => updateNestedPreference('quiet_hours', 'end_time', e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className={styles.settingGroup}>
              <h4>Notification Channels by Priority</h4>
              <p className={styles.description}>Choose which channels to use for different types of notifications</p>

              <div className={styles.channelConfig}>
                <div className={styles.priorityGroup}>
                  <label>Urgent Notifications:</label>
                  <div className={styles.channelOptions}>
                    {['push', 'email', 'sms'].map(channel => (
                      <label key={channel} className={styles.checkboxLabel}>
                        <input
                          type="checkbox"
                          checked={preferences.notification_channels.urgent.includes(channel)}
                          onChange={(e) => {
                            const channels = preferences.notification_channels.urgent;
                            if (e.target.checked) {
                              updateChannelPreference('urgent', [...channels, channel]);
                            } else {
                              updateChannelPreference('urgent', channels.filter(c => c !== channel));
                            }
                          }}
                        />
                        {channel.toUpperCase()}
                      </label>
                    ))}
                  </div>
                </div>

                <div className={styles.priorityGroup}>
                  <label>Normal Notifications:</label>
                  <div className={styles.channelOptions}>
                    {['push', 'email', 'sms'].map(channel => (
                      <label key={channel} className={styles.checkboxLabel}>
                        <input
                          type="checkbox"
                          checked={preferences.notification_channels.normal.includes(channel)}
                          onChange={(e) => {
                            const channels = preferences.notification_channels.normal;
                            if (e.target.checked) {
                              updateChannelPreference('normal', [...channels, channel]);
                            } else {
                              updateChannelPreference('normal', channels.filter(c => c !== channel));
                            }
                          }}
                        />
                        {channel.toUpperCase()}
                      </label>
                    ))}
                  </div>
                </div>

                <div className={styles.priorityGroup}>
                  <label>Marketing Notifications:</label>
                  <div className={styles.channelOptions}>
                    {['push', 'email'].map(channel => (
                      <label key={channel} className={styles.checkboxLabel}>
                        <input
                          type="checkbox"
                          checked={preferences.notification_channels.marketing.includes(channel)}
                          onChange={(e) => {
                            const channels = preferences.notification_channels.marketing;
                            if (e.target.checked) {
                              updateChannelPreference('marketing', [...channels, channel]);
                            } else {
                              updateChannelPreference('marketing', channels.filter(c => c !== channel));
                            }
                          }}
                        />
                        {channel.toUpperCase()}
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className={styles.actions}>
        <button
          className={styles.testButton}
          onClick={testNotification}
          disabled={testingNotification}
        >
          {testingNotification ? 'Sending...' : 'Send Test Notification'}
        </button>
        
        <button
          className={styles.saveButton}
          onClick={savePreferences}
          disabled={saving}
        >
          {saving ? 'Saving...' : 'Save Preferences'}
        </button>
      </div>
    </div>
  );
}
