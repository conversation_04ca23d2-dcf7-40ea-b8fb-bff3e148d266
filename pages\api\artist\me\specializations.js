import { supabase } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth'; // Placeholder for artist-specific auth

export default async function handler(req, res) {
  const authResult = await authenticateAdminRequest(req); // Replace with actual artist auth if different

  if (!authResult.authorized) {
    return res.status(401).json({ error: authResult.error?.message || 'Unauthorized' });
  }

  const { user, role } = authResult;

  if (role !== 'artist' && role !== 'braider') {
    return res.status(403).json({ error: 'Forbidden: Access restricted to artists and braiders.' });
  }

  const artistUserId = user.id; // This is auth.users.id

  if (req.method === 'GET') {
    try {
      const { data: artistProfile, error } = await supabase
        .from('artist_profiles')
        .select('specializations')
        .eq('user_id', artistUserId) // Ensure this matches the FK in artist_profiles to auth.users
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116: row not found
        throw error;
      }
      if (!artistProfile) {
        // If no profile, they have no specializations saved yet
        return res.status(200).json([]);
      }

      res.status(200).json(artistProfile.specializations || []);
    } catch (error) {
      console.error('Error fetching artist specializations:', error);
      res.status(500).json({ error: 'Failed to fetch specializations', details: error.message });
    }
  } else if (req.method === 'PUT') {
    const { specializations } = req.body; // Expects an array of service IDs (as strings or numbers)

    if (!Array.isArray(specializations)) {
      return res.status(400).json({ error: 'Request body must be an array of service specializations (IDs).' });
    }

    // Basic validation: ensure all elements are strings or numbers (if IDs are numbers)
    // For now, we'll assume they are passed as strings if they are UUIDs, or numbers if integer IDs.
    // The database column type for service IDs will determine this more strictly.
    // Assuming TEXT[] for specializations storing service IDs as text.
    const isValidArray = specializations.every(id => typeof id === 'string' || typeof id === 'number');
    if (!isValidArray) {
        return res.status(400).json({ error: 'Specializations array must contain valid service IDs.' });
    }

    // Convert all to string if they are service UUIDs, or ensure they are numbers if integer IDs
    // For this example, let's assume service IDs are stored as text in the array.
    const finalSpecializations = specializations.map(id => String(id));


    try {
      // Check if artist_profile exists, as we are updating it.
      // The approval workflow should have created one.
      const { data: existingProfile, error: fetchError } = await supabase
        .from('artist_profiles')
        .select('user_id') // just need to check existence
        .eq('user_id', artistUserId)
        .maybeSingle();

      if (fetchError) {
        console.error('Error checking for existing artist profile:', fetchError);
        return res.status(500).json({ error: 'Failed to verify artist profile.', details: fetchError.message });
      }

      if (!existingProfile) {
        // This implies an issue, as an artist should have an artist_profile record.
        // We could create one here, but it's better if the onboarding flow ensures it.
        // For now, error out if no profile to update.
        return res.status(404).json({ error: 'Artist profile not found. Cannot update specializations.'});
      }

      const { data: updatedProfile, error: updateError } = await supabase
        .from('artist_profiles')
        .update({
            specializations: finalSpecializations,
            updated_at: new Date().toISOString()
        })
        .eq('user_id', artistUserId)
        .select('specializations, updated_at') // Return the updated field
        .single();

      if (updateError) throw updateError;

      if (!updatedProfile) {
          return res.status(404).json({ error: 'Profile not found after update attempt.' });
      }

      res.status(200).json({
        success: true,
        message: 'Specializations updated successfully.',
        specializations: updatedProfile.specializations
      });

    } catch (error) {
      console.error('Error updating artist specializations:', error);
      res.status(500).json({ error: 'Failed to update specializations', details: error.message });
    }
  } else {
    res.setHeader('Allow', ['GET', 'PUT']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
