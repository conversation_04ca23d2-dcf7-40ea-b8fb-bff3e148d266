-- =============================================
-- PHASE 8: Advanced Customer Experience
-- Database Schema Enhancements
-- Ocean Soul Sparkles - Customer Portal Enhancement
-- =============================================

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================
-- 8.1 MULTI-SERVICE BOOKING SYSTEM
-- =============================================

-- Multi-service booking sessions
CREATE TABLE IF NOT EXISTS public.customer_multi_bookings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_id UUID REFERENCES public.customers(id) ON DELETE CASCADE,
  booking_session_id UUID NOT NULL,
  total_amount DECIMAL(10,2) DEFAULT 0,
  discount_amount DECIMAL(10,2) DEFAULT 0,
  final_amount DECIMAL(10,2) DEFAULT 0,
  payment_status TEXT DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')),
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'in_progress', 'completed', 'cancelled')),
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Individual services within a multi-booking session
CREATE TABLE IF NOT EXISTS public.multi_booking_services (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  multi_booking_id UUID REFERENCES public.customer_multi_bookings(id) ON DELETE CASCADE,
  service_id UUID REFERENCES public.services(id),
  artist_id UUID REFERENCES public.artist_profiles(id),
  start_time TIMESTAMPTZ NOT NULL,
  end_time TIMESTAMPTZ NOT NULL,
  service_amount DECIMAL(10,2) NOT NULL,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'in_progress', 'completed', 'cancelled')),
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- 8.2 GROUP BOOKING COORDINATION
-- =============================================

-- Group booking management
CREATE TABLE IF NOT EXISTS public.group_bookings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organizer_id UUID REFERENCES public.customers(id) ON DELETE CASCADE,
  group_name TEXT NOT NULL,
  event_date TIMESTAMPTZ NOT NULL,
  total_participants INTEGER DEFAULT 1,
  confirmed_participants INTEGER DEFAULT 0,
  total_amount DECIMAL(10,2) DEFAULT 0,
  status TEXT DEFAULT 'organizing' CHECK (status IN ('organizing', 'confirmed', 'in_progress', 'completed', 'cancelled')),
  special_requirements TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Individual participants in group bookings
CREATE TABLE IF NOT EXISTS public.group_booking_participants (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  group_booking_id UUID REFERENCES public.group_bookings(id) ON DELETE CASCADE,
  customer_id UUID REFERENCES public.customers(id),
  participant_name TEXT NOT NULL,
  participant_email TEXT,
  participant_phone TEXT,
  service_id UUID REFERENCES public.services(id),
  artist_id UUID REFERENCES public.artist_profiles(id),
  individual_amount DECIMAL(10,2) DEFAULT 0,
  payment_status TEXT DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed')),
  confirmation_status TEXT DEFAULT 'invited' CHECK (confirmation_status IN ('invited', 'confirmed', 'declined')),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- 8.3 SUBSCRIPTION-BASED RECURRING SERVICES
-- =============================================

-- Customer subscription management
CREATE TABLE IF NOT EXISTS public.customer_subscriptions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_id UUID REFERENCES public.customers(id) ON DELETE CASCADE,
  subscription_name TEXT NOT NULL,
  service_id UUID REFERENCES public.services(id),
  preferred_artist_id UUID REFERENCES public.artist_profiles(id),
  frequency TEXT NOT NULL CHECK (frequency IN ('weekly', 'bi_weekly', 'monthly', 'quarterly')),
  frequency_count INTEGER DEFAULT 1, -- Every X weeks/months
  preferred_day_of_week INTEGER, -- 0-6 (Sunday-Saturday)
  preferred_time_slot TIME,
  next_booking_date TIMESTAMPTZ,
  last_booking_date TIMESTAMPTZ,
  subscription_amount DECIMAL(10,2) NOT NULL,
  discount_percentage DECIMAL(5,2) DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  auto_book BOOLEAN DEFAULT TRUE,
  auto_pay BOOLEAN DEFAULT FALSE,
  payment_method_id TEXT, -- Square payment method reference
  total_bookings INTEGER DEFAULT 0,
  successful_bookings INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Subscription booking history
CREATE TABLE IF NOT EXISTS public.subscription_bookings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  subscription_id UUID REFERENCES public.customer_subscriptions(id) ON DELETE CASCADE,
  booking_id UUID REFERENCES public.bookings(id),
  scheduled_date TIMESTAMPTZ NOT NULL,
  actual_date TIMESTAMPTZ,
  status TEXT DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'confirmed', 'completed', 'cancelled', 'failed')),
  failure_reason TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- 8.4 DIGITAL GIFT CERTIFICATE MANAGEMENT
-- =============================================

-- Gift certificates
CREATE TABLE IF NOT EXISTS public.gift_certificates (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  code TEXT UNIQUE NOT NULL,
  purchaser_id UUID REFERENCES public.customers(id),
  purchaser_name TEXT NOT NULL,
  purchaser_email TEXT NOT NULL,
  recipient_name TEXT,
  recipient_email TEXT,
  recipient_phone TEXT,
  original_amount DECIMAL(10,2) NOT NULL,
  current_balance DECIMAL(10,2) NOT NULL,
  currency TEXT DEFAULT 'AUD',
  purchase_date TIMESTAMPTZ DEFAULT NOW(),
  expires_at TIMESTAMPTZ,
  is_active BOOLEAN DEFAULT TRUE,
  personal_message TEXT,
  delivery_method TEXT DEFAULT 'email' CHECK (delivery_method IN ('email', 'sms', 'physical')),
  delivery_date TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Gift certificate usage tracking
CREATE TABLE IF NOT EXISTS public.gift_certificate_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  gift_certificate_id UUID REFERENCES public.gift_certificates(id) ON DELETE CASCADE,
  booking_id UUID REFERENCES public.bookings(id),
  transaction_type TEXT NOT NULL CHECK (transaction_type IN ('purchase', 'redemption', 'refund', 'expiration')),
  amount DECIMAL(10,2) NOT NULL,
  balance_before DECIMAL(10,2) NOT NULL,
  balance_after DECIMAL(10,2) NOT NULL,
  transaction_date TIMESTAMPTZ DEFAULT NOW(),
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- 8.5 COMPREHENSIVE SERVICE HISTORY TRACKING
-- =============================================

-- Enhanced service history with detailed tracking
CREATE TABLE IF NOT EXISTS public.customer_service_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_id UUID REFERENCES public.customers(id) ON DELETE CASCADE,
  booking_id UUID REFERENCES public.bookings(id),
  service_id UUID REFERENCES public.services(id),
  artist_id UUID REFERENCES public.artist_profiles(id),
  service_date TIMESTAMPTZ NOT NULL,
  service_duration INTEGER, -- Minutes
  service_outcome TEXT,
  customer_satisfaction_score INTEGER CHECK (customer_satisfaction_score BETWEEN 1 AND 5),
  artist_notes TEXT,
  customer_notes TEXT,
  before_photos TEXT[], -- Array of photo URLs
  after_photos TEXT[], -- Array of photo URLs
  service_tags TEXT[], -- Array of tags for categorization
  follow_up_required BOOLEAN DEFAULT FALSE,
  follow_up_date TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- 8.6 CUSTOMER PREFERENCE MANAGEMENT
-- =============================================

-- Enhanced customer preferences
CREATE TABLE IF NOT EXISTS public.customer_advanced_preferences (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_id UUID REFERENCES public.customers(id) ON DELETE CASCADE UNIQUE,
  favorite_artists UUID[], -- Array of artist IDs
  favorite_services UUID[], -- Array of service IDs
  preferred_time_slots JSONB, -- Flexible time preferences
  preferred_locations TEXT[],
  service_customizations JSONB, -- Service-specific preferences
  communication_preferences JSONB, -- How they want to be contacted
  accessibility_requirements TEXT,
  allergies_sensitivities TEXT,
  style_preferences JSONB, -- Style and aesthetic preferences
  budget_preferences JSONB, -- Budget ranges for different services
  booking_lead_time INTEGER DEFAULT 7, -- Preferred days in advance
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- 8.7 LOYALTY PROGRAM INTEGRATION
-- =============================================

-- Customer loyalty program
CREATE TABLE IF NOT EXISTS public.customer_loyalty (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_id UUID REFERENCES public.customers(id) ON DELETE CASCADE UNIQUE,
  points_balance INTEGER DEFAULT 0,
  lifetime_points INTEGER DEFAULT 0,
  tier_level TEXT DEFAULT 'bronze' CHECK (tier_level IN ('bronze', 'silver', 'gold', 'platinum', 'diamond')),
  tier_start_date TIMESTAMPTZ DEFAULT NOW(),
  next_tier_points INTEGER,
  referral_count INTEGER DEFAULT 0,
  birthday_month INTEGER, -- 1-12 for birthday rewards
  anniversary_date TIMESTAMPTZ, -- Customer since date
  last_activity_date TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Loyalty points transactions
CREATE TABLE IF NOT EXISTS public.loyalty_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_id UUID REFERENCES public.customers(id) ON DELETE CASCADE,
  transaction_type TEXT NOT NULL CHECK (transaction_type IN ('earned', 'redeemed', 'expired', 'bonus', 'referral')),
  points_amount INTEGER NOT NULL,
  balance_before INTEGER NOT NULL,
  balance_after INTEGER NOT NULL,
  booking_id UUID REFERENCES public.bookings(id),
  description TEXT NOT NULL,
  expiry_date TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Loyalty rewards catalog
CREATE TABLE IF NOT EXISTS public.loyalty_rewards (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  reward_name TEXT NOT NULL,
  reward_description TEXT,
  points_required INTEGER NOT NULL,
  reward_type TEXT NOT NULL CHECK (reward_type IN ('discount', 'free_service', 'upgrade', 'gift')),
  reward_value DECIMAL(10,2),
  service_id UUID REFERENCES public.services(id), -- For service-specific rewards
  is_active BOOLEAN DEFAULT TRUE,
  tier_requirement TEXT CHECK (tier_requirement IN ('bronze', 'silver', 'gold', 'platinum', 'diamond')),
  usage_limit INTEGER, -- Max times this reward can be used
  expiry_days INTEGER, -- Days until reward expires after redemption
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Multi-booking indexes
CREATE INDEX IF NOT EXISTS idx_multi_bookings_customer ON public.customer_multi_bookings(customer_id);
CREATE INDEX IF NOT EXISTS idx_multi_bookings_session ON public.customer_multi_bookings(booking_session_id);
CREATE INDEX IF NOT EXISTS idx_multi_booking_services_multi_booking ON public.multi_booking_services(multi_booking_id);

-- Group booking indexes
CREATE INDEX IF NOT EXISTS idx_group_bookings_organizer ON public.group_bookings(organizer_id);
CREATE INDEX IF NOT EXISTS idx_group_participants_group ON public.group_booking_participants(group_booking_id);
CREATE INDEX IF NOT EXISTS idx_group_participants_customer ON public.group_booking_participants(customer_id);

-- Subscription indexes
CREATE INDEX IF NOT EXISTS idx_subscriptions_customer ON public.customer_subscriptions(customer_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_next_booking ON public.customer_subscriptions(next_booking_date) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_subscription_bookings_subscription ON public.subscription_bookings(subscription_id);

-- Gift certificate indexes
CREATE INDEX IF NOT EXISTS idx_gift_certificates_code ON public.gift_certificates(code);
CREATE INDEX IF NOT EXISTS idx_gift_certificates_purchaser ON public.gift_certificates(purchaser_id);
CREATE INDEX IF NOT EXISTS idx_gift_certificates_recipient ON public.gift_certificates(recipient_email);
CREATE INDEX IF NOT EXISTS idx_gift_certificate_transactions_cert ON public.gift_certificate_transactions(gift_certificate_id);

-- Service history indexes
CREATE INDEX IF NOT EXISTS idx_service_history_customer ON public.customer_service_history(customer_id);
CREATE INDEX IF NOT EXISTS idx_service_history_booking ON public.customer_service_history(booking_id);
CREATE INDEX IF NOT EXISTS idx_service_history_date ON public.customer_service_history(service_date);

-- Loyalty indexes
CREATE INDEX IF NOT EXISTS idx_loyalty_customer ON public.customer_loyalty(customer_id);
CREATE INDEX IF NOT EXISTS idx_loyalty_transactions_customer ON public.loyalty_transactions(customer_id);
CREATE INDEX IF NOT EXISTS idx_loyalty_transactions_booking ON public.loyalty_transactions(booking_id);

-- =============================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =============================================

-- Enable RLS on all new tables
ALTER TABLE public.customer_multi_bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.multi_booking_services ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.group_bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.group_booking_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscription_bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.gift_certificates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.gift_certificate_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_service_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_advanced_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_loyalty ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.loyalty_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.loyalty_rewards ENABLE ROW LEVEL SECURITY;

-- =============================================
-- FUNCTIONS AND TRIGGERS
-- =============================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers to all tables with updated_at column
CREATE TRIGGER update_customer_multi_bookings_updated_at BEFORE UPDATE ON public.customer_multi_bookings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_multi_booking_services_updated_at BEFORE UPDATE ON public.multi_booking_services FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_group_bookings_updated_at BEFORE UPDATE ON public.group_bookings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_group_booking_participants_updated_at BEFORE UPDATE ON public.group_booking_participants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customer_subscriptions_updated_at BEFORE UPDATE ON public.customer_subscriptions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_subscription_bookings_updated_at BEFORE UPDATE ON public.subscription_bookings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_gift_certificates_updated_at BEFORE UPDATE ON public.gift_certificates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customer_service_history_updated_at BEFORE UPDATE ON public.customer_service_history FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customer_advanced_preferences_updated_at BEFORE UPDATE ON public.customer_advanced_preferences FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customer_loyalty_updated_at BEFORE UPDATE ON public.customer_loyalty FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_loyalty_rewards_updated_at BEFORE UPDATE ON public.loyalty_rewards FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- INITIAL DATA SETUP
-- =============================================

-- Insert default loyalty rewards
INSERT INTO public.loyalty_rewards (reward_name, reward_description, points_required, reward_type, reward_value, is_active) VALUES
('5% Service Discount', 'Get 5% off your next service', 100, 'discount', 5.00, TRUE),
('10% Service Discount', 'Get 10% off your next service', 250, 'discount', 10.00, TRUE),
('Free Basic Service', 'Complimentary basic service of your choice', 500, 'free_service', 0.00, TRUE),
('Premium Service Upgrade', 'Upgrade any service to premium level', 300, 'upgrade', 0.00, TRUE),
('Birthday Special', 'Special birthday service package', 200, 'gift', 0.00, TRUE)
ON CONFLICT DO NOTHING;

-- =============================================
-- MIGRATION COMPLETE
-- =============================================

-- Log migration completion
DO $$
BEGIN
    RAISE NOTICE 'Phase 8 Customer Experience Enhancement migration completed successfully!';
    RAISE NOTICE 'New tables created: 13';
    RAISE NOTICE 'New indexes created: 15';
    RAISE NOTICE 'RLS policies enabled on all tables';
    RAISE NOTICE 'Triggers and functions configured';
    RAISE NOTICE 'Ready for Phase 8 implementation!';
END $$;
