/**
 * Multi-Service Booking Component - Phase 8: Advanced Customer Experience
 * Ocean Soul Sparkles - Advanced Booking Features
 */

import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import { useCustomer } from '@/contexts/CustomerContext'
import { useMobileOptimization } from '@/lib/hooks/useMobileOptimization'
import { toast } from 'react-toastify'
import styles from '@/styles/customer/MultiServiceBooking.module.css'

export default function MultiServiceBooking({ onComplete, onCancel }) {
  const router = useRouter()
  const { customer } = useCustomer()
  const { isMobile, viewport } = useMobileOptimization()
  
  const [step, setStep] = useState(1) // 1: Services, 2: Schedule, 3: Review, 4: Payment
  const [services, setServices] = useState([])
  const [selectedServices, setSelectedServices] = useState([])
  const [availableSlots, setAvailableSlots] = useState([])
  const [selectedDate, setSelectedDate] = useState('')
  const [serviceSchedule, setServiceSchedule] = useState({})
  const [totalAmount, setTotalAmount] = useState(0)
  const [discountAmount, setDiscountAmount] = useState(0)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  // Load available services
  useEffect(() => {
    loadServices()
  }, [])

  // Calculate total when services change
  useEffect(() => {
    calculateTotal()
  }, [selectedServices, discountAmount])

  const loadServices = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/public/services')
      const data = await response.json()
      
      if (data.success) {
        setServices(data.services || [])
      } else {
        throw new Error(data.error || 'Failed to load services')
      }
    } catch (error) {
      console.error('Error loading services:', error)
      setError('Failed to load services')
      toast.error('Failed to load services')
    } finally {
      setLoading(false)
    }
  }

  const calculateTotal = () => {
    const subtotal = selectedServices.reduce((total, service) => {
      return total + (parseFloat(service.price) || 0)
    }, 0)
    
    setTotalAmount(subtotal - discountAmount)
  }

  const handleServiceToggle = (service) => {
    setSelectedServices(prev => {
      const isSelected = prev.find(s => s.id === service.id)
      
      if (isSelected) {
        // Remove service
        return prev.filter(s => s.id !== service.id)
      } else {
        // Add service
        return [...prev, service]
      }
    })
  }

  const handleNextStep = async () => {
    if (step === 1) {
      if (selectedServices.length === 0) {
        toast.error('Please select at least one service')
        return
      }
      await loadAvailableSlots()
      setStep(2)
    } else if (step === 2) {
      if (!selectedDate || Object.keys(serviceSchedule).length !== selectedServices.length) {
        toast.error('Please schedule all selected services')
        return
      }
      setStep(3)
    } else if (step === 3) {
      await processBooking()
    }
  }

  const loadAvailableSlots = async () => {
    try {
      setLoading(true)
      
      const serviceIds = selectedServices.map(s => s.id).join(',')
      const response = await fetch(`/api/customer/multi-booking/availability?services=${serviceIds}&date=${selectedDate}`)
      const data = await response.json()
      
      if (data.success) {
        setAvailableSlots(data.slots || [])
      } else {
        throw new Error(data.error || 'Failed to load availability')
      }
    } catch (error) {
      console.error('Error loading availability:', error)
      toast.error('Failed to load availability')
    } finally {
      setLoading(false)
    }
  }

  const handleServiceSchedule = (serviceId, timeSlot, artistId) => {
    setServiceSchedule(prev => ({
      ...prev,
      [serviceId]: {
        timeSlot,
        artistId,
        startTime: timeSlot.start,
        endTime: timeSlot.end
      }
    }))
  }

  const processBooking = async () => {
    try {
      setLoading(true)
      
      const bookingData = {
        customer_id: customer.id,
        services: selectedServices.map(service => ({
          service_id: service.id,
          artist_id: serviceSchedule[service.id]?.artistId,
          start_time: serviceSchedule[service.id]?.startTime,
          end_time: serviceSchedule[service.id]?.endTime,
          service_amount: parseFloat(service.price)
        })),
        total_amount: totalAmount + discountAmount,
        discount_amount: discountAmount,
        final_amount: totalAmount,
        booking_date: selectedDate
      }

      const response = await fetch('/api/customer/multi-booking', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(bookingData)
      })

      const data = await response.json()
      
      if (data.success) {
        toast.success('Multi-service booking created successfully!')
        
        if (onComplete) {
          onComplete(data.booking)
        } else {
          router.push(`/customer/bookings/${data.booking.id}`)
        }
      } else {
        throw new Error(data.error || 'Failed to create booking')
      }
    } catch (error) {
      console.error('Error creating booking:', error)
      toast.error('Failed to create booking')
    } finally {
      setLoading(false)
    }
  }

  const renderServiceSelection = () => (
    <div className={styles.stepContent}>
      <h2 className={styles.stepTitle}>Select Services</h2>
      <p className={styles.stepDescription}>
        Choose multiple services to book in a single session
      </p>

      <div className={styles.serviceGrid}>
        {services.map(service => (
          <div
            key={service.id}
            className={`${styles.serviceCard} ${
              selectedServices.find(s => s.id === service.id) ? styles.selected : ''
            }`}
            onClick={() => handleServiceToggle(service)}
          >
            <div className={styles.serviceHeader}>
              <h3 className={styles.serviceName}>{service.name}</h3>
              <div className={styles.servicePrice}>
                ${parseFloat(service.price || 0).toFixed(2)}
              </div>
            </div>
            
            <p className={styles.serviceDescription}>
              {service.description}
            </p>
            
            <div className={styles.serviceMeta}>
              <span className={styles.serviceDuration}>
                ⏱️ {service.duration || 60} min
              </span>
              <span className={styles.serviceCategory}>
                {service.category}
              </span>
            </div>
            
            <div className={styles.serviceSelection}>
              {selectedServices.find(s => s.id === service.id) ? (
                <span className={styles.selectedIcon}>✓ Selected</span>
              ) : (
                <span className={styles.selectIcon}>+ Select</span>
              )}
            </div>
          </div>
        ))}
      </div>

      {selectedServices.length > 0 && (
        <div className={styles.selectionSummary}>
          <h3>Selected Services ({selectedServices.length})</h3>
          <div className={styles.selectedList}>
            {selectedServices.map(service => (
              <div key={service.id} className={styles.selectedItem}>
                <span>{service.name}</span>
                <span>${parseFloat(service.price || 0).toFixed(2)}</span>
              </div>
            ))}
          </div>
          <div className={styles.subtotal}>
            <strong>Subtotal: ${(totalAmount + discountAmount).toFixed(2)}</strong>
          </div>
        </div>
      )}
    </div>
  )

  const renderScheduling = () => (
    <div className={styles.stepContent}>
      <h2 className={styles.stepTitle}>Schedule Services</h2>
      <p className={styles.stepDescription}>
        Choose date and time for each service
      </p>

      <div className={styles.dateSelection}>
        <label htmlFor="booking-date">Select Date:</label>
        <input
          id="booking-date"
          type="date"
          value={selectedDate}
          onChange={(e) => setSelectedDate(e.target.value)}
          min={new Date().toISOString().split('T')[0]}
          className={styles.dateInput}
        />
      </div>

      {selectedDate && (
        <div className={styles.serviceScheduling}>
          {selectedServices.map(service => (
            <div key={service.id} className={styles.serviceScheduleCard}>
              <h3>{service.name}</h3>
              <p>Duration: {service.duration || 60} minutes</p>
              
              <div className={styles.timeSlots}>
                {availableSlots
                  .filter(slot => slot.service_id === service.id)
                  .map(slot => (
                    <button
                      key={`${slot.start}-${slot.artist_id}`}
                      className={`${styles.timeSlot} ${
                        serviceSchedule[service.id]?.timeSlot === slot ? styles.selected : ''
                      }`}
                      onClick={() => handleServiceSchedule(service.id, slot, slot.artist_id)}
                    >
                      <div className={styles.slotTime}>
                        {new Date(slot.start).toLocaleTimeString([], {
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </div>
                      <div className={styles.slotArtist}>
                        {slot.artist_name}
                      </div>
                    </button>
                  ))}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )

  const renderReview = () => (
    <div className={styles.stepContent}>
      <h2 className={styles.stepTitle}>Review Booking</h2>
      <p className={styles.stepDescription}>
        Please review your multi-service booking details
      </p>

      <div className={styles.reviewSummary}>
        <div className={styles.bookingDetails}>
          <h3>Booking Details</h3>
          <p><strong>Date:</strong> {new Date(selectedDate).toLocaleDateString()}</p>
          <p><strong>Customer:</strong> {customer.name}</p>
        </div>

        <div className={styles.servicesReview}>
          <h3>Services</h3>
          {selectedServices.map(service => {
            const schedule = serviceSchedule[service.id]
            return (
              <div key={service.id} className={styles.serviceReviewItem}>
                <div className={styles.serviceInfo}>
                  <h4>{service.name}</h4>
                  <p>Duration: {service.duration || 60} minutes</p>
                  {schedule && (
                    <p>
                      Time: {new Date(schedule.startTime).toLocaleTimeString([], {
                        hour: '2-digit',
                        minute: '2-digit'
                      })} - {new Date(schedule.endTime).toLocaleTimeString([], {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </p>
                  )}
                </div>
                <div className={styles.servicePrice}>
                  ${parseFloat(service.price || 0).toFixed(2)}
                </div>
              </div>
            )
          })}
        </div>

        <div className={styles.pricingSummary}>
          <div className={styles.pricingRow}>
            <span>Subtotal:</span>
            <span>${(totalAmount + discountAmount).toFixed(2)}</span>
          </div>
          {discountAmount > 0 && (
            <div className={styles.pricingRow}>
              <span>Discount:</span>
              <span>-${discountAmount.toFixed(2)}</span>
            </div>
          )}
          <div className={styles.pricingRow}>
            <strong>Total: ${totalAmount.toFixed(2)}</strong>
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <div className={styles.multiServiceBooking}>
      {/* Progress Indicator */}
      <div className={styles.progressIndicator}>
        <div className={styles.progressSteps}>
          {[1, 2, 3].map(stepNum => (
            <div
              key={stepNum}
              className={`${styles.progressStep} ${
                step >= stepNum ? styles.active : ''
              } ${step > stepNum ? styles.completed : ''}`}
            >
              <span className={styles.stepNumber}>{stepNum}</span>
              <span className={styles.stepLabel}>
                {stepNum === 1 ? 'Services' : stepNum === 2 ? 'Schedule' : 'Review'}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Step Content */}
      {error && (
        <div className={styles.error}>
          <p>{error}</p>
        </div>
      )}

      {step === 1 && renderServiceSelection()}
      {step === 2 && renderScheduling()}
      {step === 3 && renderReview()}

      {/* Navigation */}
      <div className={styles.navigation}>
        {step > 1 && (
          <button
            onClick={() => setStep(step - 1)}
            className={styles.backButton}
            disabled={loading}
          >
            ← Back
          </button>
        )}
        
        <div className={styles.navigationRight}>
          {onCancel && (
            <button
              onClick={onCancel}
              className={styles.cancelButton}
              disabled={loading}
            >
              Cancel
            </button>
          )}
          
          <button
            onClick={handleNextStep}
            className={styles.nextButton}
            disabled={loading || (step === 1 && selectedServices.length === 0)}
          >
            {loading ? 'Processing...' : step === 3 ? 'Book Services' : 'Next →'}
          </button>
        </div>
      </div>
    </div>
  )
}
