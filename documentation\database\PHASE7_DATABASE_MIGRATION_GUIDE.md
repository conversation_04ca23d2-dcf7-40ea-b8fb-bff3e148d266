# 📊 **Phase 7: Database Migration Guide**
## Ocean Soul Sparkles - Advanced Integrations & Ecosystem

### 🎯 **Overview**

This guide covers the database schema changes required to support Phase 7: Advanced Integrations & Ecosystem, including OAuth 2.0, calendar integration, social media management, and business management integrations.

---

## 📋 **Required Database Changes**

### ✅ **1. Integration Foundation Tables (Already Implemented)**

The following tables were created in Phase 7.1 and should already exist:

```sql
-- Core integration tables
✅ integration_credentials     -- OAuth tokens and credentials
✅ integration_logs           -- Audit trail for integration activities
✅ integration_settings       -- User preferences per provider
✅ integration_sync_status    -- Synchronization tracking
✅ security_logs             -- Security events and monitoring
✅ api_access_logs           -- API usage tracking
✅ rate_limit_requests       -- Rate limiting support
```

### 🔧 **2. Integration Columns for Existing Tables (New)**

The following columns need to be added to existing tables:

#### **Bookings Table Updates**
```sql
-- QuickBooks integration
ALTER TABLE bookings ADD COLUMN quickbooks_customer_id TEXT;
ALTER TABLE bookings ADD COLUMN quickbooks_invoice_id TEXT;
ALTER TABLE bookings ADD COLUMN accounting_sync_data JSONB;
ALTER TABLE bookings ADD COLUMN accounting_sync_status TEXT;

-- Calendar integration  
ALTER TABLE bookings ADD COLUMN calendar_event_id TEXT;
ALTER TABLE bookings ADD COLUMN calendar_sync_data JSONB;
ALTER TABLE bookings ADD COLUMN calendar_sync_status TEXT;
```

#### **Customers Table Updates**
```sql
-- QuickBooks integration
ALTER TABLE customers ADD COLUMN quickbooks_customer_id TEXT;
ALTER TABLE customers ADD COLUMN accounting_sync_data JSONB;

-- Mailchimp integration
ALTER TABLE customers ADD COLUMN mailchimp_subscriber_id TEXT;
ALTER TABLE customers ADD COLUMN mailchimp_list_id TEXT;
ALTER TABLE customers ADD COLUMN marketing_sync_data JSONB;
ALTER TABLE customers ADD COLUMN customer_segment TEXT;
ALTER TABLE customers ADD COLUMN customer_tags TEXT[];
```

#### **Portfolio Items Table Updates**
```sql
-- Social media integration
ALTER TABLE portfolio_items ADD COLUMN social_sync_data JSONB;
ALTER TABLE portfolio_items ADD COLUMN instagram_post_id TEXT;
ALTER TABLE portfolio_items ADD COLUMN facebook_post_id TEXT;
ALTER TABLE portfolio_items ADD COLUMN social_media_urls JSONB;
```

---

## 🚀 **Migration Implementation Options**

### **Option 1: Safe Production Migration (Recommended)**

For production environments with existing data:

1. **Apply the migration during maintenance window**
2. **Use the provided migration script**
3. **Verify each step before proceeding**

```bash
# 1. Backup database
pg_dump your_database > backup_before_phase7.sql

# 2. Apply migration
psql -d your_database -f db/migrations/phase7_integration_columns.sql

# 3. Verify migration
node scripts/verify-phase7-database-schema.mjs
```

### **Option 2: Development Environment Reset**

For development environments where data loss is acceptable:

1. **Reset Supabase project** (if using Supabase)
2. **Apply all migrations from scratch**
3. **Reseed with test data**

---

## 🔒 **Row Level Security (RLS)**

### **Existing RLS Coverage**

The new integration columns are automatically covered by existing RLS policies since they're added to existing tables.

### **Integration Tables RLS**

All integration tables have comprehensive RLS policies:

```sql
-- Users can only access their own integration data
CREATE POLICY "Users own integration data" ON integration_credentials
  FOR ALL USING (auth.uid() = user_id);

-- Admin/dev roles have full access
CREATE POLICY "Admin full access" ON integration_credentials  
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_profiles.user_id = auth.uid() 
      AND user_profiles.role IN ('admin', 'dev')
    )
  );
```

---

## 📊 **Performance Considerations**

### **Required Indexes**

The migration creates performance indexes for integration columns:

```sql
-- Booking integration indexes
CREATE INDEX idx_bookings_quickbooks_customer_id ON bookings(quickbooks_customer_id);
CREATE INDEX idx_bookings_calendar_event_id ON bookings(calendar_event_id);

-- Customer integration indexes  
CREATE INDEX idx_customers_quickbooks_customer_id ON customers(quickbooks_customer_id);
CREATE INDEX idx_customers_mailchimp_subscriber_id ON customers(mailchimp_subscriber_id);

-- Portfolio integration indexes
CREATE INDEX idx_portfolio_items_instagram_post_id ON portfolio_items(instagram_post_id);
```

### **JSONB Column Optimization**

Integration sync data is stored in JSONB columns for flexibility:

```sql
-- Example JSONB structure for accounting_sync_data
{
  "lastSyncAt": "2025-01-15T10:30:00Z",
  "syncResults": [
    {
      "provider": "quickbooks",
      "success": true,
      "invoiceId": "123",
      "customerId": "456"
    }
  ],
  "syncedProviders": ["quickbooks"]
}
```

---

## 🧪 **Testing & Verification**

### **1. Run Verification Script**

```bash
node scripts/verify-phase7-database-schema.mjs
```

Expected output:
```
✅ Database connection successful
✅ Table 'integration_credentials' - EXISTS
✅ Table 'integration_logs' - EXISTS
✅ Column 'quickbooks_customer_id' - EXISTS
✅ RLS for 'integration_credentials' - ENABLED
✅ Index 'idx_bookings_quickbooks_customer_id' - EXISTS
```

### **2. Test Integration Operations**

```javascript
// Test integration settings
const { data, error } = await supabase
  .from('integration_settings')
  .insert({
    user_id: userId,
    provider: 'quickbooks',
    settings: { autoSync: true },
    enabled: true
  })
```

### **3. Verify API Endpoints**

```bash
# Test OAuth endpoints
curl -X GET http://localhost:3000/api/integrations/oauth/quickbooks

# Test business endpoints  
curl -X GET http://localhost:3000/api/integrations/business/status
```

---

## ⚠️ **Migration Checklist**

### **Pre-Migration**
- [ ] Backup production database
- [ ] Test migration on staging environment
- [ ] Verify all integration tables exist
- [ ] Check current schema version

### **During Migration**
- [ ] Apply `phase7_integration_columns.sql`
- [ ] Verify no errors in migration log
- [ ] Check all new columns exist
- [ ] Verify indexes are created

### **Post-Migration**
- [ ] Run verification script
- [ ] Test integration API endpoints
- [ ] Verify RLS policies work
- [ ] Monitor database performance
- [ ] Update application configuration

---

## 🔧 **Troubleshooting**

### **Common Issues**

1. **Missing Integration Tables**
   ```sql
   -- Apply OAuth foundation first
   \i db/migrations/phase7_oauth_foundation.sql
   ```

2. **Column Already Exists Errors**
   ```sql
   -- Use IF NOT EXISTS in migration
   ALTER TABLE bookings ADD COLUMN IF NOT EXISTS quickbooks_customer_id TEXT;
   ```

3. **RLS Policy Conflicts**
   ```sql
   -- Drop and recreate policies if needed
   DROP POLICY IF EXISTS "existing_policy" ON table_name;
   ```

### **Rollback Procedure**

If migration fails:

```sql
-- Rollback integration columns
ALTER TABLE bookings DROP COLUMN IF EXISTS quickbooks_customer_id;
ALTER TABLE bookings DROP COLUMN IF EXISTS accounting_sync_data;
-- ... repeat for all new columns

-- Restore from backup
psql -d your_database < backup_before_phase7.sql
```

---

## 📈 **Performance Impact**

### **Expected Impact**
- **Minimal performance impact** - new columns are nullable
- **Index creation** may take 1-5 minutes on large tables
- **JSONB columns** provide efficient storage for sync data

### **Monitoring**
- Monitor query performance on integration columns
- Watch for slow queries involving JSONB operations
- Track database size growth from sync data

---

## 🎉 **Migration Complete**

After successful migration, your database will support:

✅ **OAuth 2.0 Integration** - Secure credential storage  
✅ **Calendar Sync** - Google Calendar integration  
✅ **Social Media** - Instagram/Facebook automation  
✅ **Business Management** - QuickBooks/Mailchimp integration  
✅ **Audit Logging** - Complete activity tracking  
✅ **Rate Limiting** - API usage protection  

The Ocean Soul Sparkles platform is now ready for enterprise-level business management! 🚀
