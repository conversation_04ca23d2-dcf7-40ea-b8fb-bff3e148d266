.card {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center; /* Center align content */
}

.title {
  font-size: 1.2em;
  font-weight: bold;
  margin-bottom: 15px;
  color: #333;
}

.statusText {
  font-size: 1.1em;
  margin-bottom: 8px;
  color: #555;
}

.statusValue { /* General value styling, specific colors below */
  font-weight: bold;
}

.available {
  color: #28a745; /* Green for available */
  font-weight: bold;
}

.unavailable {
  color: #dc3545; /* Red for unavailable */
  font-weight: bold;
}

.bookingsInfo {
  font-size: 0.95em;
  color: #666;
  margin-bottom: 20px;
}

.toggleButton {
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1em;
  transition: background-color 0.2s, opacity 0.2s;
  display: flex; /* For spinner alignment */
  align-items: center; /* For spinner alignment */
  justify-content: center; /* For spinner alignment */
  width: 100%; /* Make button full width of card */
  box-sizing: border-box; /* Include padding and border in the element's total width and height */
}

.setAvailableButton {
  background-color: #28a745; /* Green */
}
.setAvailableButton:hover {
  background-color: #218838;
}

.setUnavailableButton {
  background-color: #dc3545; /* Red */
}
.setUnavailableButton:hover {
  background-color: #c82333;
}

.toggleButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  opacity: 0.7;
}

.loadingSpinner {
  border: 2px solid rgba(255, 255, 255, 0.3); /* Light border for spinner on colored button */
  border-top: 2px solid #ffffff; /* White top border */
  border-radius: 50%;
  width: 16px;
  height: 16px;
  animation: spin 1s linear infinite;
  display: inline-block;
  margin-left: 8px; /* Space between text and spinner */
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
