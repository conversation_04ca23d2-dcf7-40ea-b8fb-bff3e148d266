import { supabase } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth'; // Placeholder for artist-specific auth

export default async function handler(req, res) {
  const authResult = await authenticateAdminRequest(req); // Replace with actual artist auth if different

  if (!authResult.authorized) {
    return res.status(401).json({ error: authResult.error?.message || 'Unauthorized' });
  }

  const { user, role } = authResult;

  if (role !== 'artist' && role !== 'braider') {
    return res.status(403).json({ error: 'Forbidden: Access restricted to artists and braiders.' });
  }

  const artistUserId = user.id;
  // We need artist_profiles.id for artist_weekly_availability.artist_id
  // Assuming user.id from auth is the same as artist_profiles.user_id and is what we use as `artist_id` in dependent tables.
  // However, the `artist_weekly_availability` table references `artist_profiles(id)`.
  // Let's fetch the `artist_profiles.id` based on `user_id`.

  let artistProfileId;
  try {
    const { data: profile, error: profileError } = await supabase
      .from('artist_profiles')
      .select('id') // Select the primary key of artist_profiles
      .eq('user_id', artistUserId)
      .single();

    if (profileError || !profile) {
      console.error(`Error fetching artist_profile id for user ${artistUserId}:`, profileError);
      return res.status(404).json({ error: 'Artist profile not found for the logged-in user.' });
    }
    artistProfileId = profile.id;
  } catch (e) {
    console.error('Exception fetching artist_profile id:', e);
    return res.status(500).json({ error: 'Failed to retrieve artist profile information.' });
  }


  if (req.method === 'GET') {
    try {
      const { data, error } = await supabase
        .from('artist_weekly_availability')
        .select('day_of_week, start_time, end_time')
        .eq('artist_id', artistProfileId) // Use the PK of artist_profiles
        .order('day_of_week', { ascending: true })
        .order('start_time', { ascending: true });

      if (error) throw error;
      res.status(200).json(data || []);
    } catch (error) {
      console.error('Error fetching weekly availability:', error);
      res.status(500).json({ error: 'Failed to fetch availability', details: error.message });
    }
  } else if (req.method === 'POST') {
    const newAvailabilitySlots = req.body; // Expects an array of slots

    if (!Array.isArray(newAvailabilitySlots)) {
      return res.status(400).json({ error: 'Request body must be an array of availability slots.' });
    }

    // Validate each slot
    for (const slot of newAvailabilitySlots) {
      if (slot.day_of_week === undefined || slot.day_of_week < 0 || slot.day_of_week > 6) {
        return res.status(400).json({ error: `Invalid day_of_week: ${slot.day_of_week}` });
      }
      if (!slot.start_time || !/^[0-2][0-9]:[0-5][0-9](:[0-5][0-9])?$/.test(slot.start_time)) {
        return res.status(400).json({ error: `Invalid start_time: ${slot.start_time}` });
      }
      if (!slot.end_time || !/^[0-2][0-9]:[0-5][0-9](:[0-5][0-9])?$/.test(slot.end_time)) {
        return res.status(400).json({ error: `Invalid end_time: ${slot.end_time}` });
      }
      if (slot.start_time >= slot.end_time) {
        return res.status(400).json({ error: `End time must be after start time for slot: ${JSON.stringify(slot)}` });
      }
    }

    try {
      // Perform operations in a transaction if possible, though Supabase JS client v2 might not directly expose transactions like this.
      // For now, execute as separate operations. If Supabase supports RPC for batch delete/insert, that's better.

      // 1. Delete existing availability for this artist
      const { error: deleteError } = await supabase
        .from('artist_weekly_availability')
        .delete()
        .eq('artist_id', artistProfileId); // Use the PK of artist_profiles

      if (deleteError) throw deleteError;

      // 2. Insert new availability slots if any are provided
      if (newAvailabilitySlots.length > 0) {
        const slotsToInsert = newAvailabilitySlots.map(slot => ({
          artist_id: artistProfileId, // Use the PK of artist_profiles
          day_of_week: slot.day_of_week,
          start_time: slot.start_time,
          end_time: slot.end_time,
        }));

        const { data: insertedSlots, error: insertError } = await supabase
          .from('artist_weekly_availability')
          .insert(slotsToInsert)
          .select();

        if (insertError) throw insertError;
        res.status(200).json({ success: true, message: 'Availability updated successfully.', availability: insertedSlots });
      } else {
        // If newAvailabilitySlots is empty, it means clear all availability
        res.status(200).json({ success: true, message: 'Availability cleared successfully.', availability: [] });
      }

    } catch (error) {
      console.error('Error updating weekly availability:', error);
      res.status(500).json({ error: 'Failed to update availability', details: error.message });
    }
  } else {
    res.setHeader('Allow', ['GET', 'POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
