import { Client, Environment } from 'square';
import { v4 as uuidv4 } from 'uuid';
import { sendOrderConfirmationEmail, sendAdminOrderNotificationEmail } from '../../../lib/email-service';
// Supabase client import removed as it's no longer used for Square config

// Reinstated environment variable usage for Square client initialization
const squareAccessToken = process.env.SQUARE_ACCESS_TOKEN;
const squareEnvironmentString = process.env.SQUARE_ENVIRONMENT?.toLowerCase();

let squareEnvironment;
if (squareEnvironmentString === 'sandbox') {
  squareEnvironment = Environment.Sandbox;
} else if (squareEnvironmentString === 'production') {
  squareEnvironment = Environment.Production;
} else {
  // Default to Sandbox if not set or invalid, and log a warning/error
  console.warn(`SQUARE_ENVIRONMENT is not set or invalid ('${process.env.SQUARE_ENVIRONMENT}'). Defaulting to Sandbox. Payment processing may not work as expected in production.`);
  squareEnvironment = Environment.Sandbox;
}

// Global Square client instance (can be outside handler if config doesn't change per request)
// However, checking for token presence should ideally be per request or at startup
let squareClient;
if (squareAccessToken) {
    squareClient = new Client({
        accessToken: squareAccessToken,
        environment: squareEnvironment,
    });
} else {
    console.error('SQUARE_ACCESS_TOKEN is not set. Square client cannot be initialized.');
    // squareClient will remain undefined, and subsequent checks should handle this.
}


export default async function handler(req, res) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }

  const { token, amount, currency = 'AUD', orderDetails = {} } = req.body;

  if (!token || !amount) {
    return res.status(400).json({ error: 'Missing required payment information: token and amount.' });
  }

  // Convert amount to cents (or smallest currency unit)
  const amountInCents = Math.round(parseFloat(amount) * 100);
  if (isNaN(amountInCents) || amountInCents <= 0) {
      return res.status(400).json({ error: 'Invalid amount.' });
  }

  // Check if Square client was initialized (i.e., access token was available)
  if (!squareClient) {
    console.error('Square client is not initialized due to missing SQUARE_ACCESS_TOKEN at startup.');
    return res.status(500).json({ error: 'Payment processor configuration error. Please contact support.' });
  }

  // Additional check for environment variables at request time, though squareClient init check is primary
  if (!process.env.SQUARE_ACCESS_TOKEN || !process.env.SQUARE_ENVIRONMENT) {
      console.error('Square environment variables (SQUARE_ACCESS_TOKEN or SQUARE_ENVIRONMENT) are not properly set at request time.');
      return res.status(500).json({ error: 'Payment processor server configuration error.' });
  }

  try {
    // Supabase fetch logic removed. Square client is now initialized globally or checked.
    const paymentsApi = squareClient.paymentsApi;
    const idempotencyKey = uuidv4();

    const paymentData = {
      sourceId: token,
      idempotencyKey,
      amountMoney: {
        amount: BigInt(amountInCents), // Square API expects amount in BigInt for cents
        currency: currency,
      },
      // Optionally, add more details:
      // locationId: process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID, // If specific location needed and not default
      // orderId: orderDetails.id, // If you have a pre-generated order ID
      // note: `Order for ${orderDetails.customerName || 'customer'}`
    };

    if (orderDetails.orderId) {
      paymentData.orderId = orderDetails.orderId;
    }
    if (orderDetails.note) {
      paymentData.note = orderDetails.note;
    }
     if (process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID) {
      paymentData.locationId = process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID;
    }


    console.log('Creating payment with data:', JSON.stringify(paymentData, (key, value) =>
      typeof value === 'bigint' ? value.toString() : value
    ));

    const { result, statusCode } = await paymentsApi.createPayment(paymentData);

    console.log('Square API response status code:', statusCode);
    console.log('Square API response result:', JSON.stringify(result, (key, value) =>
        typeof value === 'bigint' ? value.toString() : value
    ));

    if (statusCode === 200 || (result && result.payment && (result.payment.status === 'COMPLETED' || result.payment.status === 'APPROVED'))) {

      // --- Send Order Confirmation Email ---
      // Ensure customerEmail is passed in orderDetails from the client
      const customerEmail = orderDetails.customerEmail;
      if (customerEmail) {
        const emailOrderDetails = {
          orderId: result.payment.orderId || orderDetails.clientOrderId || 'N/A', // Use Square's orderId if available
          orderDate: result.payment.createdAt || new Date().toISOString(),
          customerName: orderDetails.customerName || 'Valued Customer',
          items: orderDetails.items || [], // Passed from client
          subtotal: orderDetails.subtotal || (Number(result.payment.amountMoney.amount) / 100), // Use client's subtotal or payment amount
          shipping: orderDetails.shippingCost !== undefined ? orderDetails.shippingCost : 0, // Expect from client
          // taxes: orderDetails.taxes, // Expect from client if applicable
          total: Number(result.payment.amountMoney.amount) / 100,
          shippingAddress: orderDetails.shippingAddress || null, // Expect from client
        };

        const emailPaymentDetails = {
          method: result.payment.cardDetails?.card?.cardBrand ? `Card (${result.payment.cardDetails.card.cardBrand})` : 'Card',
          last4: result.payment.cardDetails?.card?.last4,
          transactionId: result.payment.id,
          paymentDate: result.payment.createdAt || new Date().toISOString(),
        };

        try {
          const customerEmailResult = await sendOrderConfirmationEmail(customerEmail, emailOrderDetails, emailPaymentDetails);
          if (customerEmailResult.success) {
            console.log(`Order confirmation email sent successfully to ${customerEmail}. Message preview: ${customerEmailResult.message}`);
          } else {
            console.error(`Failed to send order confirmation email to ${customerEmail}:`, customerEmailResult.error);
          }
        } catch (emailError) {
          console.error(`Exception during customer email sending for order ${emailOrderDetails.orderId}:`, emailError);
        }

        // --- Send Admin Notification Email ---
        try {
          // Add customer phone to orderDetails for admin if available from original orderDetails
          if(orderDetails.customerPhone) {
            emailOrderDetails.customerPhone = orderDetails.customerPhone;
          }
          const adminEmailResult = await sendAdminOrderNotificationEmail(emailOrderDetails, emailPaymentDetails, result.payment);
          if (adminEmailResult.success) {
            console.log(`Admin order notification email sent successfully. Message preview: ${adminEmailResult.message}`);
          } else {
            console.error(`Failed to send admin order notification email:`, adminEmailResult.error);
          }
        } catch (adminEmailError) {
          console.error(`Exception during admin email sending for order ${emailOrderDetails.orderId}:`, adminEmailError);
        }
        // --- End Admin Email Sending ---

      } else {
        console.warn('No customer email provided in orderDetails. Skipping order confirmation email and admin notification.');
      }
      // --- End Customer Email Sending --- (Comment was slightly misplaced, it's end of customer email logic block)

      res.status(200).json({
        success: true,
        paymentId: result.payment.id,
        status: result.payment.status,
        receiptUrl: result.payment.receiptUrl,
        orderId: result.payment.orderId,
        paymentDetails: { // Include some details that might be useful for the client/order saving
            amount: Number(result.payment.amountMoney.amount) / 100,
            currency: result.payment.amountMoney.currency,
            cardBrand: result.payment.cardDetails?.card?.cardBrand,
            last4: result.payment.cardDetails?.card?.last4,
        }
      });
    } else {
      // Handle other statuses or errors from Square
      const squareError = result.errors && result.errors.length > 0 ? result.errors[0] : { detail: 'Unknown Square payment error' };
      console.error('Square payment error:', squareError);
      res.status(statusCode || 500).json({
        success: false,
        error: `Payment failed: ${squareError.detail}`,
        details: squareError,
      });
    }
  } catch (error) {
    console.error('Error processing payment with Square API:', error);
    // Check if it's a Square API error response
    if (error.response && error.response.data && error.response.data.errors) {
        const squareError = error.response.data.errors[0];
        return res.status(error.response.status || 500).json({
            success: false,
            error: `Payment processing error: ${squareError.detail}`,
            details: squareError,
        });
    }
    // Generic server error
    res.status(500).json({
      success: false,
      error: 'Internal server error while processing payment.',
      details: error.message
    });
  }
}
