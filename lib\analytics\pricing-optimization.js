/**
 * Dynamic Pricing Optimization Engine for Ocean Soul Sparkles
 * Implements intelligent pricing strategies based on demand, competition, and market conditions
 */

/**
 * Dynamic Pricing Engine
 */
export class DynamicPricingEngine {
  constructor(config = {}) {
    this.config = {
      basePriceMultiplier: config.basePriceMultiplier || 1.0,
      demandSensitivity: config.demandSensitivity || 0.3,
      competitionWeight: config.competitionWeight || 0.2,
      seasonalWeight: config.seasonalWeight || 0.25,
      artistSkillWeight: config.artistSkillWeight || 0.25,
      maxPriceIncrease: config.maxPriceIncrease || 0.5, // 50% max increase
      maxPriceDecrease: config.maxPriceDecrease || 0.3, // 30% max decrease
      ...config
    };
  }

  calculateOptimalPrice(basePrice, factors) {
    const {
      demandLevel,
      competitionPricing,
      seasonalMultiplier,
      artistSkillLevel,
      timeToEvent,
      historicalConversionRate,
      inventoryLevel
    } = factors;

    // Calculate demand-based adjustment
    const demandAdjustment = this.calculateDemandAdjustment(demandLevel, timeToEvent);
    
    // Calculate competition-based adjustment
    const competitionAdjustment = this.calculateCompetitionAdjustment(basePrice, competitionPricing);
    
    // Calculate seasonal adjustment
    const seasonalAdjustment = seasonalMultiplier || 1.0;
    
    // Calculate artist skill adjustment
    const skillAdjustment = this.calculateSkillAdjustment(artistSkillLevel);
    
    // Calculate inventory pressure adjustment
    const inventoryAdjustment = this.calculateInventoryAdjustment(inventoryLevel);
    
    // Calculate conversion rate adjustment
    const conversionAdjustment = this.calculateConversionAdjustment(historicalConversionRate);

    // Combine all factors with weights
    const totalAdjustment = 
      (demandAdjustment * this.config.demandSensitivity) +
      (competitionAdjustment * this.config.competitionWeight) +
      (seasonalAdjustment * this.config.seasonalWeight) +
      (skillAdjustment * this.config.artistSkillWeight) +
      (inventoryAdjustment * 0.1) +
      (conversionAdjustment * 0.1);

    // Apply adjustment to base price
    let optimizedPrice = basePrice * (1 + totalAdjustment);

    // Apply min/max constraints
    const maxPrice = basePrice * (1 + this.config.maxPriceIncrease);
    const minPrice = basePrice * (1 - this.config.maxPriceDecrease);
    
    optimizedPrice = Math.max(minPrice, Math.min(maxPrice, optimizedPrice));

    return {
      optimizedPrice: Math.round(optimizedPrice * 100) / 100, // Round to 2 decimal places
      basePrice,
      adjustmentFactors: {
        demand: demandAdjustment,
        competition: competitionAdjustment,
        seasonal: seasonalAdjustment,
        skill: skillAdjustment,
        inventory: inventoryAdjustment,
        conversion: conversionAdjustment
      },
      totalAdjustment,
      confidence: this.calculatePricingConfidence(factors)
    };
  }

  calculateDemandAdjustment(demandLevel, timeToEvent) {
    // Higher demand = higher prices
    // Closer to event = higher urgency pricing
    const demandMultiplier = Math.max(0.5, Math.min(2.0, demandLevel));
    const urgencyMultiplier = timeToEvent < 7 ? 1.2 : timeToEvent < 14 ? 1.1 : 1.0;
    
    return (demandMultiplier - 1) * urgencyMultiplier;
  }

  calculateCompetitionAdjustment(basePrice, competitionPricing) {
    if (!competitionPricing || competitionPricing.length === 0) return 0;
    
    const avgCompetitorPrice = competitionPricing.reduce((sum, price) => sum + price, 0) / competitionPricing.length;
    const priceRatio = basePrice / avgCompetitorPrice;
    
    // If we're significantly higher than competition, suggest decrease
    // If we're significantly lower, suggest increase
    if (priceRatio > 1.2) return -0.1; // Decrease price
    if (priceRatio < 0.8) return 0.1;  // Increase price
    
    return 0; // Competitive pricing
  }

  calculateSkillAdjustment(artistSkillLevel) {
    // Artist skill levels: 1 (beginner) to 5 (expert)
    const skillMultipliers = {
      1: -0.2, // 20% discount for beginners
      2: -0.1, // 10% discount for intermediate
      3: 0,    // Base price for standard
      4: 0.15, // 15% premium for advanced
      5: 0.3   // 30% premium for expert
    };
    
    return skillMultipliers[artistSkillLevel] || 0;
  }

  calculateInventoryAdjustment(inventoryLevel) {
    // Low inventory = higher prices (scarcity pricing)
    // High inventory = lower prices (clearance pricing)
    if (inventoryLevel < 0.2) return 0.1;  // Low inventory, increase price
    if (inventoryLevel > 0.8) return -0.05; // High inventory, decrease price
    
    return 0; // Normal inventory levels
  }

  calculateConversionAdjustment(conversionRate) {
    // Low conversion rate might indicate price is too high
    // High conversion rate might indicate room for price increase
    if (conversionRate < 0.1) return -0.05; // Very low conversion, decrease price
    if (conversionRate > 0.3) return 0.05;  // High conversion, increase price
    
    return 0; // Normal conversion rates
  }

  calculatePricingConfidence(factors) {
    let confidence = 0.5; // Base confidence
    
    // Increase confidence with more data points
    if (factors.historicalConversionRate !== undefined) confidence += 0.1;
    if (factors.competitionPricing && factors.competitionPricing.length > 0) confidence += 0.1;
    if (factors.demandLevel !== undefined) confidence += 0.1;
    if (factors.seasonalMultiplier !== undefined) confidence += 0.1;
    if (factors.artistSkillLevel !== undefined) confidence += 0.1;
    
    return Math.min(0.9, confidence);
  }
}

/**
 * Market Analysis Engine
 */
export class MarketAnalysisEngine {
  constructor() {
    this.competitorData = [];
    this.marketTrends = [];
  }

  analyzeMarketPosition(serviceData, competitorData) {
    const analysis = {
      pricePosition: this.calculatePricePosition(serviceData, competitorData),
      marketShare: this.estimateMarketShare(serviceData, competitorData),
      competitiveAdvantages: this.identifyCompetitiveAdvantages(serviceData, competitorData),
      pricingOpportunities: this.identifyPricingOpportunities(serviceData, competitorData),
      recommendations: []
    };

    analysis.recommendations = this.generateRecommendations(analysis);
    
    return analysis;
  }

  calculatePricePosition(serviceData, competitorData) {
    if (!competitorData || competitorData.length === 0) {
      return { position: 'unknown', percentile: 50 };
    }

    const allPrices = [...competitorData.map(c => c.price), serviceData.price].sort((a, b) => a - b);
    const ourPriceIndex = allPrices.indexOf(serviceData.price);
    const percentile = (ourPriceIndex / (allPrices.length - 1)) * 100;

    let position;
    if (percentile < 25) position = 'budget';
    else if (percentile < 50) position = 'value';
    else if (percentile < 75) position = 'premium';
    else position = 'luxury';

    return { position, percentile };
  }

  estimateMarketShare(serviceData, competitorData) {
    // Simplified market share estimation based on bookings and pricing
    const totalBookings = competitorData.reduce((sum, c) => sum + (c.bookings || 0), 0) + (serviceData.bookings || 0);
    
    if (totalBookings === 0) return 0;
    
    return ((serviceData.bookings || 0) / totalBookings) * 100;
  }

  identifyCompetitiveAdvantages(serviceData, competitorData) {
    const advantages = [];
    
    // Price advantage
    const avgCompetitorPrice = competitorData.reduce((sum, c) => sum + c.price, 0) / competitorData.length;
    if (serviceData.price < avgCompetitorPrice * 0.9) {
      advantages.push({ type: 'price', description: 'Significantly lower pricing than competitors' });
    }

    // Quality advantage (based on ratings)
    const avgCompetitorRating = competitorData.reduce((sum, c) => sum + (c.rating || 0), 0) / competitorData.length;
    if ((serviceData.rating || 0) > avgCompetitorRating + 0.5) {
      advantages.push({ type: 'quality', description: 'Higher customer satisfaction ratings' });
    }

    // Booking volume advantage
    const avgCompetitorBookings = competitorData.reduce((sum, c) => sum + (c.bookings || 0), 0) / competitorData.length;
    if ((serviceData.bookings || 0) > avgCompetitorBookings * 1.2) {
      advantages.push({ type: 'popularity', description: 'Higher booking volume than competitors' });
    }

    return advantages;
  }

  identifyPricingOpportunities(serviceData, competitorData) {
    const opportunities = [];
    
    const pricePosition = this.calculatePricePosition(serviceData, competitorData);
    
    // If we're in budget category but have high ratings, we can increase prices
    if (pricePosition.position === 'budget' && (serviceData.rating || 0) > 4.0) {
      opportunities.push({
        type: 'price_increase',
        description: 'High quality service priced below market - opportunity to increase prices',
        suggestedIncrease: 0.15
      });
    }

    // If we're in luxury category but have low bookings, we might need to decrease prices
    if (pricePosition.position === 'luxury' && (serviceData.bookings || 0) < 10) {
      opportunities.push({
        type: 'price_decrease',
        description: 'Premium pricing with low demand - consider price reduction',
        suggestedDecrease: 0.1
      });
    }

    // Gap in market pricing
    const sortedPrices = competitorData.map(c => c.price).sort((a, b) => a - b);
    for (let i = 0; i < sortedPrices.length - 1; i++) {
      const gap = sortedPrices[i + 1] - sortedPrices[i];
      if (gap > sortedPrices[i] * 0.3) { // 30% gap
        opportunities.push({
          type: 'market_gap',
          description: `Pricing gap between $${sortedPrices[i]} and $${sortedPrices[i + 1]}`,
          suggestedPrice: (sortedPrices[i] + sortedPrices[i + 1]) / 2
        });
      }
    }

    return opportunities;
  }

  generateRecommendations(analysis) {
    const recommendations = [];

    // Price position recommendations
    if (analysis.pricePosition.position === 'budget' && analysis.marketShare > 20) {
      recommendations.push({
        priority: 'high',
        type: 'pricing',
        action: 'Consider gradual price increases to capture more value',
        impact: 'revenue_increase'
      });
    }

    // Competitive advantage recommendations
    analysis.competitiveAdvantages.forEach(advantage => {
      if (advantage.type === 'quality') {
        recommendations.push({
          priority: 'medium',
          type: 'marketing',
          action: 'Emphasize quality advantage in marketing materials',
          impact: 'brand_positioning'
        });
      }
    });

    // Pricing opportunity recommendations
    analysis.pricingOpportunities.forEach(opportunity => {
      if (opportunity.type === 'price_increase') {
        recommendations.push({
          priority: 'high',
          type: 'pricing',
          action: `Increase prices by ${(opportunity.suggestedIncrease * 100).toFixed(0)}%`,
          impact: 'revenue_increase'
        });
      }
    });

    return recommendations;
  }
}

/**
 * Price Elasticity Calculator
 */
export class PriceElasticityCalculator {
  constructor() {
    this.elasticityData = [];
  }

  calculateElasticity(priceChanges, demandChanges) {
    if (priceChanges.length !== demandChanges.length || priceChanges.length < 2) {
      throw new Error('Invalid data for elasticity calculation');
    }

    const elasticities = [];
    
    for (let i = 1; i < priceChanges.length; i++) {
      const priceChange = (priceChanges[i] - priceChanges[i-1]) / priceChanges[i-1];
      const demandChange = (demandChanges[i] - demandChanges[i-1]) / demandChanges[i-1];
      
      if (priceChange !== 0) {
        elasticities.push(demandChange / priceChange);
      }
    }

    const avgElasticity = elasticities.reduce((sum, e) => sum + e, 0) / elasticities.length;
    
    return {
      elasticity: avgElasticity,
      interpretation: this.interpretElasticity(avgElasticity),
      confidence: this.calculateElasticityConfidence(elasticities)
    };
  }

  interpretElasticity(elasticity) {
    const absElasticity = Math.abs(elasticity);
    
    if (absElasticity > 1) {
      return {
        type: 'elastic',
        description: 'Demand is sensitive to price changes',
        recommendation: 'Consider price reductions to increase total revenue'
      };
    } else if (absElasticity < 1) {
      return {
        type: 'inelastic',
        description: 'Demand is not very sensitive to price changes',
        recommendation: 'Consider price increases to maximize revenue'
      };
    } else {
      return {
        type: 'unit_elastic',
        description: 'Demand changes proportionally with price',
        recommendation: 'Current pricing is optimal for revenue'
      };
    }
  }

  calculateElasticityConfidence(elasticities) {
    if (elasticities.length < 3) return 0.3;
    
    const variance = this.calculateVariance(elasticities);
    const mean = elasticities.reduce((sum, e) => sum + e, 0) / elasticities.length;
    const coefficientOfVariation = Math.sqrt(variance) / Math.abs(mean || 1);
    
    return Math.max(0.1, Math.min(0.9, 1 - coefficientOfVariation));
  }

  calculateVariance(data) {
    const mean = data.reduce((sum, value) => sum + value, 0) / data.length;
    return data.reduce((sum, value) => sum + Math.pow(value - mean, 2), 0) / data.length;
  }

  predictDemandAtPrice(currentPrice, currentDemand, newPrice, elasticity) {
    const priceChange = (newPrice - currentPrice) / currentPrice;
    const demandChange = elasticity * priceChange;
    
    return currentDemand * (1 + demandChange);
  }

  findOptimalPrice(currentPrice, currentDemand, elasticity, costPerUnit = 0) {
    // Find price that maximizes profit using elasticity
    const prices = [];
    const profits = [];
    
    // Test price range from 50% to 200% of current price
    for (let multiplier = 0.5; multiplier <= 2.0; multiplier += 0.05) {
      const testPrice = currentPrice * multiplier;
      const predictedDemand = this.predictDemandAtPrice(currentPrice, currentDemand, testPrice, elasticity);
      const profit = (testPrice - costPerUnit) * Math.max(0, predictedDemand);
      
      prices.push(testPrice);
      profits.push(profit);
    }
    
    const maxProfitIndex = profits.indexOf(Math.max(...profits));
    
    return {
      optimalPrice: prices[maxProfitIndex],
      expectedDemand: this.predictDemandAtPrice(currentPrice, currentDemand, prices[maxProfitIndex], elasticity),
      expectedProfit: profits[maxProfitIndex],
      priceChange: (prices[maxProfitIndex] - currentPrice) / currentPrice
    };
  }
}

/**
 * A/B Testing Framework for Pricing
 */
export class PricingABTestFramework {
  constructor() {
    this.activeTests = [];
    this.completedTests = [];
  }

  createPricingTest(testConfig) {
    const test = {
      id: this.generateTestId(),
      name: testConfig.name,
      startDate: new Date(),
      endDate: testConfig.endDate,
      controlPrice: testConfig.controlPrice,
      testPrice: testConfig.testPrice,
      trafficSplit: testConfig.trafficSplit || 0.5,
      minimumSampleSize: testConfig.minimumSampleSize || 100,
      status: 'active',
      results: {
        control: { bookings: 0, revenue: 0, conversions: 0, views: 0 },
        test: { bookings: 0, revenue: 0, conversions: 0, views: 0 }
      }
    };

    this.activeTests.push(test);
    return test;
  }

  recordTestResult(testId, variant, result) {
    const test = this.activeTests.find(t => t.id === testId);
    if (!test) return false;

    const variantData = test.results[variant];
    if (!variantData) return false;

    variantData.views += result.views || 0;
    variantData.bookings += result.bookings || 0;
    variantData.revenue += result.revenue || 0;
    variantData.conversions += result.conversions || 0;

    return true;
  }

  analyzeTestResults(testId) {
    const test = this.activeTests.find(t => t.id === testId);
    if (!test) return null;

    const control = test.results.control;
    const testVariant = test.results.test;

    // Calculate conversion rates
    const controlConversion = control.views > 0 ? control.conversions / control.views : 0;
    const testConversion = testVariant.views > 0 ? testVariant.conversions / testVariant.views : 0;

    // Calculate average order values
    const controlAOV = control.bookings > 0 ? control.revenue / control.bookings : 0;
    const testAOV = testVariant.bookings > 0 ? testVariant.revenue / testVariant.bookings : 0;

    // Calculate statistical significance (simplified)
    const significance = this.calculateStatisticalSignificance(control, testVariant);

    return {
      testId,
      testName: test.name,
      duration: Math.floor((Date.now() - test.startDate) / (1000 * 60 * 60 * 24)),
      sampleSize: control.views + testVariant.views,
      results: {
        control: {
          price: test.controlPrice,
          conversionRate: controlConversion,
          averageOrderValue: controlAOV,
          totalRevenue: control.revenue,
          bookings: control.bookings
        },
        test: {
          price: test.testPrice,
          conversionRate: testConversion,
          averageOrderValue: testAOV,
          totalRevenue: testVariant.revenue,
          bookings: testVariant.bookings
        }
      },
      improvements: {
        conversionRate: ((testConversion - controlConversion) / controlConversion) * 100,
        averageOrderValue: ((testAOV - controlAOV) / controlAOV) * 100,
        revenue: ((testVariant.revenue - control.revenue) / control.revenue) * 100
      },
      significance,
      recommendation: this.generateTestRecommendation(control, testVariant, test, significance)
    };
  }

  calculateStatisticalSignificance(control, test) {
    // Simplified statistical significance calculation
    const controlRate = control.views > 0 ? control.conversions / control.views : 0;
    const testRate = test.views > 0 ? test.conversions / test.views : 0;
    
    const pooledRate = (control.conversions + test.conversions) / (control.views + test.views);
    const standardError = Math.sqrt(pooledRate * (1 - pooledRate) * (1/control.views + 1/test.views));
    
    if (standardError === 0) return { significant: false, confidence: 0 };
    
    const zScore = Math.abs(testRate - controlRate) / standardError;
    const confidence = this.zScoreToConfidence(zScore);
    
    return {
      significant: confidence > 0.95,
      confidence,
      zScore
    };
  }

  zScoreToConfidence(zScore) {
    // Simplified z-score to confidence conversion
    if (zScore > 2.58) return 0.99;
    if (zScore > 1.96) return 0.95;
    if (zScore > 1.64) return 0.90;
    return 0.5 + (zScore / 3.29) * 0.4; // Approximate for lower z-scores
  }

  generateTestRecommendation(control, test, testConfig, significance) {
    if (!significance.significant) {
      return {
        action: 'continue',
        reason: 'Test needs more data to reach statistical significance',
        confidence: 'low'
      };
    }

    const testRevenue = test.revenue;
    const controlRevenue = control.revenue;

    if (testRevenue > controlRevenue * 1.05) { // 5% improvement threshold
      return {
        action: 'implement',
        reason: `Test price of $${testConfig.testPrice} shows significant revenue improvement`,
        confidence: 'high'
      };
    } else if (testRevenue < controlRevenue * 0.95) { // 5% decline threshold
      return {
        action: 'reject',
        reason: `Test price of $${testConfig.testPrice} shows significant revenue decline`,
        confidence: 'high'
      };
    } else {
      return {
        action: 'neutral',
        reason: 'No significant difference in performance',
        confidence: 'medium'
      };
    }
  }

  generateTestId() {
    return 'test_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }
}
