/**
 * Social Media Dashboard Component for Ocean Soul Sparkles
 * Provides social media integration management interface
 * 
 * Phase 7.3: Social Media Integration Layer
 */

import { useState, useEffect } from 'react'
import { toast } from 'react-toastify'
import { useMobileOptimization } from '@/lib/hooks/useMobileOptimization'
import { authenticatedFetch } from '@/lib/auth-utils'
import styles from './SocialMediaDashboard.module.css'

export default function SocialMediaDashboard({ userId }) {
  const [integrations, setIntegrations] = useState([])
  const [profiles, setProfiles] = useState([])
  const [posts, setPosts] = useState([])
  const [analytics, setAnalytics] = useState({})
  const [loading, setLoading] = useState(true)
  const [posting, setPosting] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')
  const [newPost, setNewPost] = useState({
    message: '',
    imageUrl: '',
    providers: [],
    scheduledTime: ''
  })

  const {
    isMobile,
    isTablet,
    hapticFeedback,
    shouldReduceAnimations
  } = useMobileOptimization()

  useEffect(() => {
    loadSocialMediaData()
  }, [userId])

  /**
   * Load social media integration data
   */
  const loadSocialMediaData = async () => {
    try {
      setLoading(true)

      // Load integration status
      const statusResponse = await authenticatedFetch('/api/integrations/social/status')
      if (statusResponse.success) {
        setIntegrations(statusResponse.integrations || [])
      }

      // Load profiles
      const profilesResponse = await authenticatedFetch('/api/integrations/social/profiles')
      if (profilesResponse.success) {
        setProfiles(profilesResponse.profiles || [])
      }

      // Load recent posts
      const postsResponse = await authenticatedFetch('/api/integrations/social/posts?limit=10')
      if (postsResponse.success) {
        setPosts(postsResponse.posts || [])
      }

      // Load analytics
      const analyticsResponse = await authenticatedFetch('/api/integrations/social/analytics')
      if (analyticsResponse.success) {
        setAnalytics(analyticsResponse.analytics || {})
      }

    } catch (error) {
      console.error('Failed to load social media data:', error)
      toast.error('Failed to load social media data')
    } finally {
      setLoading(false)
    }
  }

  /**
   * Connect to social media provider
   */
  const connectProvider = async (provider) => {
    try {
      hapticFeedback('light')

      const response = await authenticatedFetch(`/api/integrations/oauth/${provider}`)
      
      if (response.success && response.authUrl) {
        // Redirect to OAuth authorization
        window.location.href = response.authUrl
      } else {
        toast.error(response.message || 'Failed to initiate social media connection')
      }

    } catch (error) {
      console.error('Failed to connect social media:', error)
      toast.error('Failed to connect social media')
    }
  }

  /**
   * Disconnect social media provider
   */
  const disconnectProvider = async (provider) => {
    try {
      hapticFeedback('medium')

      const confirmed = window.confirm(
        `Are you sure you want to disconnect ${provider}? This will stop automated posting and portfolio sync.`
      )

      if (!confirmed) return

      const response = await authenticatedFetch(`/api/integrations/social/disconnect`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ provider })
      })

      if (response.success) {
        toast.success(`${provider} disconnected successfully`)
        await loadSocialMediaData()
      } else {
        toast.error(response.message || 'Failed to disconnect social media')
      }

    } catch (error) {
      console.error('Failed to disconnect social media:', error)
      toast.error('Failed to disconnect social media')
    }
  }

  /**
   * Create new post
   */
  const createPost = async () => {
    try {
      setPosting(true)
      hapticFeedback('light')

      if (!newPost.message.trim()) {
        toast.error('Please enter a message for your post')
        return
      }

      if (newPost.providers.length === 0) {
        toast.error('Please select at least one platform to post to')
        return
      }

      const response = await authenticatedFetch('/api/integrations/social/post', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          providers: newPost.providers,
          postData: {
            message: newPost.message,
            imageUrl: newPost.imageUrl || null,
            scheduledTime: newPost.scheduledTime || null
          }
        })
      })

      if (response.success) {
        const successCount = response.results.filter(r => r.success).length
        const totalCount = response.results.length
        
        if (successCount === totalCount) {
          toast.success(`Post created successfully on all ${totalCount} platform(s)`)
        } else {
          toast.warning(`Post created on ${successCount} of ${totalCount} platforms`)
        }

        // Reset form
        setNewPost({
          message: '',
          imageUrl: '',
          providers: [],
          scheduledTime: ''
        })

        // Reload data
        await loadSocialMediaData()
      } else {
        toast.error(response.message || 'Failed to create post')
      }

    } catch (error) {
      console.error('Failed to create post:', error)
      toast.error('Failed to create post')
    } finally {
      setPosting(false)
    }
  }

  /**
   * Sync portfolio to social media
   */
  const syncPortfolio = async () => {
    try {
      hapticFeedback('medium')

      const confirmed = window.confirm(
        'This will post your latest portfolio items to connected social media platforms. Continue?'
      )

      if (!confirmed) return

      const response = await authenticatedFetch('/api/integrations/social/sync-portfolio', {
        method: 'POST'
      })

      if (response.success) {
        const successCount = response.results.filter(r => r.success).length
        toast.success(`Portfolio synced successfully. ${successCount} items posted.`)
        await loadSocialMediaData()
      } else {
        toast.error(response.message || 'Failed to sync portfolio')
      }

    } catch (error) {
      console.error('Failed to sync portfolio:', error)
      toast.error('Failed to sync portfolio')
    }
  }

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>Loading social media integrations...</p>
      </div>
    )
  }

  return (
    <div className={`${styles.container} ${isMobile ? styles.mobile : ''}`}>
      {/* Header */}
      <div className={styles.header}>
        <h2>Social Media Integration</h2>
        <p>Manage your social media presence and automate content sharing</p>
      </div>

      {/* Navigation Tabs */}
      <div className={styles.tabs}>
        {['overview', 'post', 'analytics', 'settings'].map(tab => (
          <button
            key={tab}
            className={`${styles.tab} ${activeTab === tab ? styles.active : ''}`}
            onClick={() => {
              setActiveTab(tab)
              hapticFeedback('light')
            }}
          >
            {tab.charAt(0).toUpperCase() + tab.slice(1)}
          </button>
        ))}
      </div>

      {/* Overview Tab */}
      {activeTab === 'overview' && (
        <div className={styles.tabContent}>
          {/* Connected Platforms */}
          <div className={styles.platforms}>
            <h3>Connected Platforms</h3>
            
            {integrations.length === 0 ? (
              <div className={styles.noPlatforms}>
                <p>No social media platforms connected yet</p>
                <div className={styles.connectButtons}>
                  <button
                    className={styles.connectButton}
                    onClick={() => connectProvider('instagram_business')}
                  >
                    📸 Connect Instagram Business
                  </button>
                  <button
                    className={styles.connectButton}
                    onClick={() => connectProvider('facebook_business')}
                  >
                    📘 Connect Facebook Business
                  </button>
                </div>
              </div>
            ) : (
              <div className={styles.platformList}>
                {integrations.map(integration => (
                  <div key={integration.provider} className={styles.platformCard}>
                    <div className={styles.platformInfo}>
                      <h4>
                        {integration.provider === 'instagram_business' ? '📸' : '📘'} 
                        {integration.name}
                      </h4>
                      <p className={`${styles.status} ${styles[integration.status]}`}>
                        {integration.status === 'connected' ? '✓ Connected' : '⚠ Needs Attention'}
                      </p>
                      {integration.profile && (
                        <div className={styles.profileInfo}>
                          <p><strong>@{integration.profile.username || integration.profile.name}</strong></p>
                          <p>{integration.profile.followersCount || integration.profile.fanCount} followers</p>
                        </div>
                      )}
                    </div>
                    
                    <div className={styles.platformActions}>
                      <button
                        className={styles.settingsButton}
                        onClick={() => setActiveTab('settings')}
                      >
                        Settings
                      </button>
                      <button
                        className={styles.disconnectButton}
                        onClick={() => disconnectProvider(integration.provider)}
                      >
                        Disconnect
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Recent Posts */}
          {posts.length > 0 && (
            <div className={styles.recentPosts}>
              <h3>Recent Posts</h3>
              <div className={styles.postGrid}>
                {posts.slice(0, 6).map(post => (
                  <div key={`${post.provider}_${post.id}`} className={styles.postCard}>
                    {post.mediaUrl && (
                      <img src={post.mediaUrl} alt="Post" className={styles.postImage} />
                    )}
                    <div className={styles.postContent}>
                      <p className={styles.postText}>
                        {(post.message || post.caption || '').substring(0, 100)}...
                      </p>
                      <div className={styles.postMeta}>
                        <span className={styles.platform}>
                          {post.provider === 'instagram_business' ? '📸' : '📘'}
                        </span>
                        <span className={styles.engagement}>
                          ❤️ {post.likesCount || post.likeCount || 0}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Quick Actions */}
          <div className={styles.quickActions}>
            <h3>Quick Actions</h3>
            <div className={styles.actionButtons}>
              <button
                className={styles.actionButton}
                onClick={() => setActiveTab('post')}
                disabled={integrations.length === 0}
              >
                ✍️ Create Post
              </button>
              <button
                className={styles.actionButton}
                onClick={syncPortfolio}
                disabled={integrations.length === 0}
              >
                🎨 Sync Portfolio
              </button>
              <button
                className={styles.actionButton}
                onClick={() => setActiveTab('analytics')}
                disabled={integrations.length === 0}
              >
                📊 View Analytics
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Post Tab */}
      {activeTab === 'post' && (
        <div className={styles.tabContent}>
          <div className={styles.postCreator}>
            <h3>Create New Post</h3>
            
            <div className={styles.postForm}>
              <div className={styles.formGroup}>
                <label>Message</label>
                <textarea
                  value={newPost.message}
                  onChange={(e) => setNewPost({...newPost, message: e.target.value})}
                  placeholder="What would you like to share?"
                  rows={4}
                  className={styles.messageInput}
                />
              </div>

              <div className={styles.formGroup}>
                <label>Image URL (optional)</label>
                <input
                  type="url"
                  value={newPost.imageUrl}
                  onChange={(e) => setNewPost({...newPost, imageUrl: e.target.value})}
                  placeholder="https://example.com/image.jpg"
                  className={styles.urlInput}
                />
              </div>

              <div className={styles.formGroup}>
                <label>Platforms</label>
                <div className={styles.platformSelector}>
                  {integrations.map(integration => (
                    <label key={integration.provider} className={styles.platformOption}>
                      <input
                        type="checkbox"
                        checked={newPost.providers.includes(integration.provider)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setNewPost({
                              ...newPost,
                              providers: [...newPost.providers, integration.provider]
                            })
                          } else {
                            setNewPost({
                              ...newPost,
                              providers: newPost.providers.filter(p => p !== integration.provider)
                            })
                          }
                        }}
                      />
                      <span>
                        {integration.provider === 'instagram_business' ? '📸' : '📘'} 
                        {integration.name}
                      </span>
                    </label>
                  ))}
                </div>
              </div>

              <div className={styles.formActions}>
                <button
                  className={styles.postButton}
                  onClick={createPost}
                  disabled={posting || integrations.length === 0}
                >
                  {posting ? 'Posting...' : 'Create Post'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Analytics Tab */}
      {activeTab === 'analytics' && (
        <div className={styles.tabContent}>
          <div className={styles.analytics}>
            <h3>Social Media Analytics</h3>
            
            {Object.keys(analytics).length === 0 ? (
              <div className={styles.noAnalytics}>
                <p>No analytics data available yet</p>
              </div>
            ) : (
              <div className={styles.analyticsGrid}>
                {Object.entries(analytics).map(([provider, data]) => (
                  <div key={provider} className={styles.analyticsCard}>
                    <h4>
                      {provider === 'instagram_business' ? '📸' : '📘'} 
                      {data.providerName}
                    </h4>
                    
                    {data.error ? (
                      <p className={styles.error}>Failed to load analytics</p>
                    ) : (
                      <div className={styles.metrics}>
                        {Object.entries(data.insights || {}).map(([metric, value]) => (
                          <div key={metric} className={styles.metric}>
                            <span className={styles.metricValue}>
                              {typeof value === 'object' ? value.value : value}
                            </span>
                            <span className={styles.metricLabel}>
                              {metric.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                            </span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Settings Tab */}
      {activeTab === 'settings' && (
        <div className={styles.tabContent}>
          <div className={styles.settings}>
            <h3>Social Media Settings</h3>
            
            <div className={styles.settingsInfo}>
              <p>Social media settings and automation preferences will be available here.</p>
              <p>Configure posting schedules, hashtags, and portfolio sync options.</p>
            </div>
          </div>
        </div>
      )}

      {/* Post Tab */}
      {activeTab === 'post' && (
        <div className={styles.tabContent}>
          <div className={styles.postCreator}>
            <h3>Create New Post</h3>

            <div className={styles.postForm}>
              <div className={styles.formGroup}>
                <label>Message</label>
                <textarea
                  value={newPost.message}
                  onChange={(e) => setNewPost({...newPost, message: e.target.value})}
                  placeholder="What would you like to share?"
                  rows={4}
                  className={styles.messageInput}
                />
              </div>

              <div className={styles.formGroup}>
                <label>Image URL (optional)</label>
                <input
                  type="url"
                  value={newPost.imageUrl}
                  onChange={(e) => setNewPost({...newPost, imageUrl: e.target.value})}
                  placeholder="https://example.com/image.jpg"
                  className={styles.urlInput}
                />
              </div>

              <div className={styles.formGroup}>
                <label>Platforms</label>
                <div className={styles.platformSelector}>
                  {integrations.map(integration => (
                    <label key={integration.provider} className={styles.platformOption}>
                      <input
                        type="checkbox"
                        checked={newPost.providers.includes(integration.provider)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setNewPost({
                              ...newPost,
                              providers: [...newPost.providers, integration.provider]
                            })
                          } else {
                            setNewPost({
                              ...newPost,
                              providers: newPost.providers.filter(p => p !== integration.provider)
                            })
                          }
                        }}
                      />
                      <span>
                        {integration.provider === 'instagram_business' ? '📸' : '📘'}
                        {integration.name}
                      </span>
                    </label>
                  ))}
                </div>
              </div>

              <div className={styles.formActions}>
                <button
                  className={styles.postButton}
                  onClick={createPost}
                  disabled={posting || integrations.length === 0}
                >
                  {posting ? 'Posting...' : 'Create Post'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Analytics Tab */}
      {activeTab === 'analytics' && (
        <div className={styles.tabContent}>
          <div className={styles.analytics}>
            <h3>Social Media Analytics</h3>

            {Object.keys(analytics).length === 0 ? (
              <div className={styles.noAnalytics}>
                <p>No analytics data available yet</p>
              </div>
            ) : (
              <div className={styles.analyticsGrid}>
                {Object.entries(analytics).map(([provider, data]) => (
                  <div key={provider} className={styles.analyticsCard}>
                    <h4>
                      {provider === 'instagram_business' ? '📸' : '📘'}
                      {data.providerName}
                    </h4>

                    {data.error ? (
                      <p className={styles.error}>Failed to load analytics</p>
                    ) : (
                      <div className={styles.metrics}>
                        {Object.entries(data.insights || {}).map(([metric, value]) => (
                          <div key={metric} className={styles.metric}>
                            <span className={styles.metricValue}>
                              {typeof value === 'object' ? value.value : value}
                            </span>
                            <span className={styles.metricLabel}>
                              {metric.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                            </span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Settings Tab */}
      {activeTab === 'settings' && (
        <div className={styles.tabContent}>
          <div className={styles.settings}>
            <h3>Social Media Settings</h3>

            <div className={styles.settingsInfo}>
              <p>Social media settings and automation preferences will be available here.</p>
              <p>Configure posting schedules, hashtags, and portfolio sync options.</p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
