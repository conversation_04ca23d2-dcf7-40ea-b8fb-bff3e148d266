/**
 * Facebook Business API Integration for Ocean Soul Sparkles
 * Provides Facebook Business API integration with page management and event promotion
 * 
 * Phase 7.3: Social Media Integration Layer
 */

import oauthManager from '../oauth-manager'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, AuditLogger } from '../security-utils'

/**
 * Facebook Business API Client
 */
export class FacebookBusinessClient {
  constructor(userId) {
    this.userId = userId
    this.baseUrl = 'https://graph.facebook.com/v18.0'
    this.accessToken = null
    this.pageId = null
    this.pageAccessToken = null
  }

  /**
   * Initialize Facebook Business client with user credentials
   */
  async initialize() {
    try {
      // Get user's OAuth tokens
      const tokens = await oauthManager.getTokens(this.userId, 'facebook_business')
      
      if (!tokens) {
        throw new Error('No Facebook Business credentials found for user')
      }

      // Check if tokens need refresh
      if (oauthManager.needsRefresh(tokens.expires_at)) {
        const refreshedTokens = await oauthManager.refreshTokens(this.userId, 'facebook_business')
        tokens.access_token = refreshedTokens.access_token
      }

      this.accessToken = tokens.access_token

      // Get page information and access token
      await this.getPageInfo()

      return true
    } catch (error) {
      console.error('Failed to initialize Facebook Business client:', error)
      
      await AuditLogger.logIntegrationActivity(
        this.userId,
        'facebook_business',
        'initialization_failed',
        'error',
        { error: error.message }
      )
      
      return false
    }
  }

  /**
   * Get Facebook page information and access token
   */
  async getPageInfo() {
    return await RetryHandler.withRetry(async () => {
      const response = await fetch(
        `${this.baseUrl}/me/accounts?fields=id,name,access_token,category,about,fan_count,link&access_token=${this.accessToken}`
      )
      
      if (!response.ok) {
        throw new Error(`Failed to get pages: ${response.statusText}`)
      }

      const data = await response.json()
      
      if (!data.data || data.data.length === 0) {
        throw new Error('No Facebook pages found')
      }

      // Use the first page (in production, you might want to let user choose)
      const page = data.data[0]
      this.pageId = page.id
      this.pageAccessToken = page.access_token

      return {
        id: page.id,
        name: page.name,
        category: page.category,
        about: page.about,
        fanCount: page.fan_count,
        link: page.link
      }
    })
  }

  /**
   * Get Facebook page profile information
   */
  async getPageProfile() {
    if (!this.pageId || !this.pageAccessToken) {
      throw new Error('Facebook Business client not initialized')
    }

    return await RetryHandler.withRetry(async () => {
      const response = await fetch(
        `${this.baseUrl}/${this.pageId}?fields=id,name,about,category,fan_count,link,picture,cover,website,phone,location&access_token=${this.pageAccessToken}`
      )

      if (!response.ok) {
        throw new Error(`Failed to get page profile: ${response.statusText}`)
      }

      const data = await response.json()
      return {
        id: data.id,
        name: data.name,
        about: data.about,
        category: data.category,
        fanCount: data.fan_count,
        link: data.link,
        picture: data.picture?.data?.url,
        cover: data.cover?.source,
        website: data.website,
        phone: data.phone,
        location: data.location
      }
    })
  }

  /**
   * Get Facebook page posts
   */
  async getPosts(limit = 25, after = null) {
    if (!this.pageId || !this.pageAccessToken) {
      throw new Error('Facebook Business client not initialized')
    }

    return await RetryHandler.withRetry(async () => {
      let url = `${this.baseUrl}/${this.pageId}/posts?fields=id,message,story,created_time,type,link,picture,full_picture,likes.summary(true),comments.summary(true),shares&limit=${limit}&access_token=${this.pageAccessToken}`
      
      if (after) {
        url += `&after=${after}`
      }

      const response = await fetch(url)

      if (!response.ok) {
        throw new Error(`Failed to get posts: ${response.statusText}`)
      }

      const data = await response.json()
      
      return {
        posts: data.data?.map(post => ({
          id: post.id,
          message: post.message,
          story: post.story,
          createdTime: post.created_time,
          type: post.type,
          link: post.link,
          picture: post.picture,
          fullPicture: post.full_picture,
          likesCount: post.likes?.summary?.total_count || 0,
          commentsCount: post.comments?.summary?.total_count || 0,
          sharesCount: post.shares?.count || 0
        })) || [],
        paging: data.paging
      }
    })
  }

  /**
   * Create Facebook post
   */
  async createPost(postData) {
    if (!this.pageId || !this.pageAccessToken) {
      throw new Error('Facebook Business client not initialized')
    }

    return await RetryHandler.withRetry(async () => {
      const formData = new URLSearchParams({
        access_token: this.pageAccessToken
      })

      // Add message
      if (postData.message) {
        formData.append('message', postData.message)
      }

      // Add link
      if (postData.link) {
        formData.append('link', postData.link)
      }

      // Add photo
      if (postData.photoUrl) {
        formData.append('url', postData.photoUrl)
      }

      // Add scheduled publish time
      if (postData.scheduledPublishTime) {
        formData.append('scheduled_publish_time', Math.floor(new Date(postData.scheduledPublishTime).getTime() / 1000))
        formData.append('published', 'false')
      }

      const endpoint = postData.photoUrl ? 'photos' : 'feed'
      const response = await fetch(
        `${this.baseUrl}/${this.pageId}/${endpoint}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: formData
        }
      )

      if (!response.ok) {
        const error = await response.text()
        throw new Error(`Failed to create post: ${error}`)
      }

      const result = await response.json()

      await AuditLogger.logIntegrationActivity(
        this.userId,
        'facebook_business',
        'post_created',
        'success',
        {
          postId: result.id,
          hasMessage: !!postData.message,
          hasPhoto: !!postData.photoUrl,
          hasLink: !!postData.link,
          isScheduled: !!postData.scheduledPublishTime
        }
      )

      return {
        id: result.id,
        postId: result.post_id
      }
    })
  }

  /**
   * Create Facebook event
   */
  async createEvent(eventData) {
    if (!this.pageId || !this.pageAccessToken) {
      throw new Error('Facebook Business client not initialized')
    }

    return await RetryHandler.withRetry(async () => {
      const formData = new URLSearchParams({
        name: eventData.name,
        description: eventData.description,
        start_time: eventData.startTime,
        access_token: this.pageAccessToken
      })

      // Add optional fields
      if (eventData.endTime) {
        formData.append('end_time', eventData.endTime)
      }

      if (eventData.location) {
        formData.append('place', eventData.location)
      }

      if (eventData.coverPhoto) {
        formData.append('cover_url', eventData.coverPhoto)
      }

      if (eventData.isOnline) {
        formData.append('online_event_format', 'messenger_room')
      }

      const response = await fetch(
        `${this.baseUrl}/${this.pageId}/events`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: formData
        }
      )

      if (!response.ok) {
        const error = await response.text()
        throw new Error(`Failed to create event: ${error}`)
      }

      const result = await response.json()

      await AuditLogger.logIntegrationActivity(
        this.userId,
        'facebook_business',
        'event_created',
        'success',
        {
          eventId: result.id,
          eventName: eventData.name,
          startTime: eventData.startTime,
          hasLocation: !!eventData.location,
          isOnline: !!eventData.isOnline
        }
      )

      return {
        id: result.id
      }
    })
  }

  /**
   * Get Facebook page insights (analytics)
   */
  async getInsights(period = 'day', metrics = null) {
    if (!this.pageId || !this.pageAccessToken) {
      throw new Error('Facebook Business client not initialized')
    }

    return await RetryHandler.withRetry(async () => {
      const defaultMetrics = [
        'page_impressions',
        'page_reach',
        'page_views_total',
        'page_likes',
        'page_engaged_users',
        'page_post_engagements'
      ]

      const metricsToUse = metrics || defaultMetrics
      const params = new URLSearchParams({
        metric: metricsToUse.join(','),
        period,
        access_token: this.pageAccessToken
      })

      const response = await fetch(`${this.baseUrl}/${this.pageId}/insights?${params}`)

      if (!response.ok) {
        throw new Error(`Failed to get insights: ${response.statusText}`)
      }

      const data = await response.json()
      
      return data.data?.reduce((acc, insight) => {
        acc[insight.name] = {
          value: insight.values?.[0]?.value || 0,
          period: insight.period,
          title: insight.title,
          description: insight.description
        }
        return acc
      }, {}) || {}
    })
  }

  /**
   * Get Facebook events
   */
  async getEvents(limit = 25) {
    if (!this.pageId || !this.pageAccessToken) {
      throw new Error('Facebook Business client not initialized')
    }

    return await RetryHandler.withRetry(async () => {
      const response = await fetch(
        `${this.baseUrl}/${this.pageId}/events?fields=id,name,description,start_time,end_time,place,cover,attending_count,interested_count&limit=${limit}&access_token=${this.pageAccessToken}`
      )

      if (!response.ok) {
        throw new Error(`Failed to get events: ${response.statusText}`)
      }

      const data = await response.json()
      
      return data.data?.map(event => ({
        id: event.id,
        name: event.name,
        description: event.description,
        startTime: event.start_time,
        endTime: event.end_time,
        place: event.place,
        cover: event.cover?.source,
        attendingCount: event.attending_count,
        interestedCount: event.interested_count
      })) || []
    })
  }

  /**
   * Test connection to Facebook Business API
   */
  async testConnection() {
    try {
      if (!await this.initialize()) {
        return { success: false, error: 'Failed to initialize Facebook Business client' }
      }

      // Try to get page profile as a test
      const profile = await this.getPageProfile()
      
      await AuditLogger.logIntegrationActivity(
        this.userId,
        'facebook_business',
        'connection_test',
        'success',
        { pageName: profile.name, fanCount: profile.fanCount }
      )

      return { 
        success: true, 
        profile,
        message: 'Facebook Business connection successful'
      }
    } catch (error) {
      await AuditLogger.logIntegrationActivity(
        this.userId,
        'facebook_business',
        'connection_test',
        'error',
        { error: error.message }
      )

      return { 
        success: false, 
        error: error.message 
      }
    }
  }
}

export default FacebookBusinessClient
