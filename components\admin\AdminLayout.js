import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { useAuth } from '@/contexts/AuthContext'
import AuthErrorMonitor from './AuthErrorMonitor'
import styles from '@/styles/admin/AdminLayout.module.css'

export default function AdminLayout({ children, title, collapseSidebar = false }) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [sidebarCollapsed, setSidebarCollapsed] = useState(true) // Default to collapsed
  const { user, role, signOut, hasAdminAccess, isDev } = useAuth()
  const router = useRouter()

  // Load sidebar preference from localStorage on mount
  useEffect(() => {
    const savedState = localStorage.getItem('adminSidebarCollapsed')
    if (savedState !== null) {
      setSidebarCollapsed(JSON.parse(savedState))
    }
    // For POS Terminal, always force collapsed state
    if (collapseSidebar) {
      setSidebarCollapsed(true)
      setSidebarOpen(false)
    }
  }, [collapseSidebar])

  // Toggle sidebar collapse state and save preference
  const toggleSidebarCollapse = () => {
    const newState = !sidebarCollapsed
    setSidebarCollapsed(newState)
    localStorage.setItem('adminSidebarCollapsed', JSON.stringify(newState))
  }

  const handleSignOut = async () => {
    await signOut()
    // Use replace to prevent back button issues after logout
    router.replace('/admin/login')
  }

  return (    <div className={styles.adminLayout}>      <aside className={`${styles.sidebar} ${sidebarOpen ? styles.open : ''} ${sidebarCollapsed ? styles.collapsed : ''}`}>
        <div className={styles.sidebarHeader}>
          <div className={styles.logoContainer}>
            {sidebarCollapsed ? (
              <div className={styles.compactLogo} title="Ocean Soul Sparkles">
                <span className={styles.logoInitials}>OSS</span>
              </div>
            ) : (
              <img
                src="/images/bannerlogo.PNG"
                alt="OceanSoulSparkles Logo"
                className={styles.logo}
              />
            )}
          </div>

          {/* Collapse/Expand Toggle Button - Always show except when forced collapsed (POS Terminal) */}
          {!collapseSidebar && (
            <button
              className={styles.collapseToggle}
              onClick={toggleSidebarCollapse}
              aria-label={sidebarCollapsed ? "Expand sidebar" : "Collapse sidebar"}
              title={sidebarCollapsed ? "Expand sidebar" : "Collapse sidebar"}
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                {sidebarCollapsed ? (
                  <>
                    <polyline points="9 18 15 12 9 6"></polyline>
                  </>
                ) : (
                  <>
                    <polyline points="15 18 9 12 15 6"></polyline>
                  </>
                )}
              </svg>
            </button>
          )}

          <button
            className={styles.closeSidebar}
            onClick={() => setSidebarOpen(false)}
            aria-label="Close sidebar"
          >
            ×
          </button>
        </div>

        <nav className={styles.sidebarNav}>
          <Link
            href="/admin"
            className={`${styles.navLink} ${router.pathname === '/admin' ? styles.active : ''}`}
            title={sidebarCollapsed ? "Dashboard" : ""}
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
              <polyline points="9 22 9 12 15 12 15 22"></polyline>
            </svg>
            {!sidebarCollapsed && <span className={styles.navText}>Dashboard</span>}
          </Link>

          <Link
            href="/admin/bookings"
            className={`${styles.navLink} ${router.pathname.startsWith('/admin/bookings') ? styles.active : ''}`}
            title={sidebarCollapsed ? "Bookings" : ""}
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="16" y1="2" x2="16" y2="6"></line>
              <line x1="8" y1="2" x2="8" y2="6"></line>
              <line x1="3" y1="10" x2="21" y2="10"></line>
            </svg>
            {!sidebarCollapsed && <span className={styles.navText}>Bookings</span>}
          </Link>

          <Link
            href="/admin/customers"
            className={`${styles.navLink} ${router.pathname.startsWith('/admin/customers') ? styles.active : ''}`}
            title={sidebarCollapsed ? "Customers" : ""}
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
            {!sidebarCollapsed && <span className={styles.navText}>Customers</span>}
          </Link>

          <Link
            href="/admin/pos"
            className={`${styles.navLink} ${router.pathname.startsWith('/admin/pos') ? styles.active : ''}`}
            title={sidebarCollapsed ? "POS Terminal" : ""}
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
              <line x1="8" y1="21" x2="16" y2="21"></line>
              <line x1="12" y1="17" x2="12" y2="21"></line>
            </svg>
            {!sidebarCollapsed && <span className={styles.navText}>POS Terminal</span>}
          </Link>

          <Link
            href="/admin/payments"
            className={`${styles.navLink} ${router.pathname.startsWith('/admin/payments') ? styles.active : ''}`}
            title={sidebarCollapsed ? "Payments" : ""}
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <rect x="1" y="4" width="22" height="16" rx="2" ry="2"></rect>
              <line x1="1" y1="10" x2="23" y2="10"></line>
            </svg>
            {!sidebarCollapsed && <span className={styles.navText}>Payments</span>}
          </Link>

          <Link
            href="/admin/inventory"
            className={router.pathname.startsWith('/admin/inventory') ? styles.active : ''}
            title={sidebarCollapsed ? "Services & Shop" : ""}
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
              <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
              <line x1="12" y1="22.08" x2="12" y2="12"></line>
            </svg>
            Services & Shop
          </Link>

          <Link
            href="/admin/gallery"
            className={router.pathname.startsWith('/admin/gallery') ? styles.active : ''}
            title={sidebarCollapsed ? "Gallery" : ""}
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <circle cx="8.5" cy="8.5" r="1.5"></circle>
              <polyline points="21 15 16 10 5 21"></polyline>
            </svg>
            Gallery
          </Link>

          <Link
            href="/admin/analytics"
            className={router.pathname.startsWith('/admin/analytics') ? styles.active : ''}
            title={sidebarCollapsed ? "Analytics" : ""}
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="18" y1="20" x2="18" y2="10"></line>
              <line x1="12" y1="20" x2="12" y2="4"></line>
              <line x1="6" y1="20" x2="6" y2="14"></line>
            </svg>
            Analytics
          </Link>

          <Link
            href="/admin/marketing"
            className={router.pathname.startsWith('/admin/marketing') ? styles.active : ''}
            title={sidebarCollapsed ? "Marketing" : ""}
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M12 19l9 2-9-18-9 18 9-2z"></path>
            </svg>
            Marketing
          </Link>

          <Link
            href="/admin/events"
            className={router.pathname.startsWith('/admin/events') ? styles.active : ''}
            title={sidebarCollapsed ? "Events" : ""}
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="16" y1="2" x2="16" y2="6"></line>
              <line x1="8" y1="2" x2="8" y2="6"></line>
              <line x1="3" y1="10" x2="21" y2="10"></line>
              <path d="M8 14l2 2 4-4"></path>
            </svg>
            Events
          </Link>

          {hasAdminAccess && (
            <>
              <Link
                href="/admin/users"
                className={router.pathname.startsWith('/admin/users') ? styles.active : ''}
                title={sidebarCollapsed ? "User Management" : ""}
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                  <circle cx="9" cy="7" r="4"></circle>
                  <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                  <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                </svg>
                User Management
              </Link>

              {isDev && (
                <Link
                  href="/admin/user-profiles"
                  className={router.pathname.startsWith('/admin/user-profiles') ? styles.active : ''}
                  title={sidebarCollapsed ? "User Profiles" : ""}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M16 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="10" cy="7" r="4"></circle>
                    <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                    <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                  </svg>
                  User Profiles
                </Link>
              )}

              <Link
                href="/admin/settings"
                className={router.pathname.startsWith('/admin/settings') ? styles.active : ''}
                title={sidebarCollapsed ? "Settings" : ""}
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="3"></circle>
                  <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                </svg>
                Settings
              </Link>

              <Link
                href="/admin/diagnostics"
                className={router.pathname.startsWith('/admin/diagnostics') ? styles.active : ''}
                title={sidebarCollapsed ? "Diagnostics" : ""}
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M18 8h1a4 4 0 0 1 0 8h-1"></path>
                  <path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z"></path>
                  <line x1="6" y1="1" x2="6" y2="4"></line>
                  <line x1="10" y1="1" x2="10" y2="4"></line>
                  <line x1="14" y1="1" x2="14" y2="4"></line>
                </svg>
                Diagnostics
              </Link>
            </>
          )}
        </nav>        <div className={styles.sidebarFooter}>
          {!sidebarCollapsed && (
            <div className={styles.userInfo}>
              <span className={styles.userName}>{user?.email}</span>
              <span className={styles.userRole}>{role}</span>
            </div>
          )}
          <button
            className={styles.signOutButton}
            onClick={handleSignOut}
            title={sidebarCollapsed ? "Sign Out" : ""}
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
              <polyline points="16 17 21 12 16 7"></polyline>
              <line x1="21" y1="12" x2="9" y2="12"></line>
            </svg>
            {!sidebarCollapsed && <span>Sign Out</span>}
          </button>
        </div>
      </aside>

      <main className={`${styles.mainContent} ${sidebarCollapsed ? styles.collapsedLayout : styles.expandedLayout}`}>
        <header className={styles.header}>
          <button
            className={styles.menuButton}
            onClick={() => setSidebarOpen(true)}
            aria-label="Open sidebar"
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="3" y1="12" x2="21" y2="12"></line>
              <line x1="3" y1="6" x2="21" y2="6"></line>
              <line x1="3" y1="18" x2="21" y2="18"></line>
            </svg>
          </button>
          <h1 className={styles.pageTitle}>{title}</h1>
          <div className={styles.headerActions}>
            {/* Notifications, etc. can go here */}
          </div>
        </header>

        <div className={styles.content}>
          {children}
        </div>
      </main>

      {/* Authentication Error Monitor - only show in development or when errors occur */}
      {(process.env.NODE_ENV === 'development' || process.env.NEXT_PUBLIC_DEBUG_AUTH === 'true') && (
        <AuthErrorMonitor />
      )}
    </div>
  )
}
