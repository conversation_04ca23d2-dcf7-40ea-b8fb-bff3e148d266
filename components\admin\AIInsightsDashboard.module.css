/* AI Insights Dashboard Styles */

.aiDashboard {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
  color: #333;
  max-width: 100%;
  overflow: hidden;
}

.mobile {
  padding: 1rem;
  border-radius: 8px;
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.title {
  margin: 0;
  font-size: 1.75rem;
  font-weight: 700;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.dateSelector {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.dateInput,
.typeSelector {
  padding: 0.5rem 0.75rem;
  border: 2px solid #dee2e6;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  color: #495057;
  transition: border-color 0.2s ease;
}

.dateInput:focus,
.typeSelector:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.refreshButton {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.refreshButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Loading State */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
}

.loadingSpinner {
  width: 48px;
  height: 48px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.loading p {
  margin: 0;
  opacity: 0.8;
}

/* Error State */
.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
  border-radius: 12px;
}

.errorIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.error h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.error p {
  margin: 0 0 1.5rem 0;
  opacity: 0.9;
}

.retryButton {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid white;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retryButton:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

/* Cards */
.summaryCard,
.alertsCard,
.recommendationsCard,
.bookingInsights {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.summaryCard:hover,
.alertsCard:hover,
.recommendationsCard:hover,
.bookingInsights:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.cardTitle {
  margin: 0 0 1.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Summary Card */
.summaryHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.performanceIndicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  background: rgba(0, 0, 0, 0.05);
  font-weight: 500;
}

.statusIcon {
  font-size: 1.2rem;
}

.statusText {
  font-size: 0.875rem;
}

.summaryGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.summaryMetric {
  text-align: center;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.metricValue {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #2c3e50;
}

.metricValue.positive {
  color: #28a745;
}

.metricValue.negative {
  color: #dc3545;
}

.metricLabel {
  display: block;
  font-size: 0.875rem;
  color: #6c757d;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.narrative {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 1.5rem;
  border-radius: 8px;
  margin-top: 1rem;
}

.narrative p {
  margin: 0;
  line-height: 1.6;
  font-weight: 500;
}

/* Alerts */
.alertsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.alert {
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid;
  background: #f8f9fa;
}

.alert.severityHigh {
  border-left-color: #dc3545;
  background: rgba(220, 53, 69, 0.05);
}

.alert.severityMedium {
  border-left-color: #ffc107;
  background: rgba(255, 193, 7, 0.05);
}

.alert.severityLow {
  border-left-color: #17a2b8;
  background: rgba(23, 162, 184, 0.05);
}

.alertHeader {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.alertType,
.alertCategory,
.alertSeverity {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.alertType {
  background: #6c757d;
  color: white;
}

.alertCategory {
  background: #007bff;
  color: white;
}

.alertSeverity {
  background: #28a745;
  color: white;
}

.severityHigh .alertSeverity {
  background: #dc3545;
}

.severityMedium .alertSeverity {
  background: #ffc107;
  color: #212529;
}

.alertMessage {
  margin: 0 0 1rem 0;
  font-weight: 500;
  color: #2c3e50;
}

.alertRecommendation {
  margin: 0 0 1rem 0;
  font-style: italic;
  color: #495057;
}

.alertMetrics {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.alertMetric {
  font-size: 0.75rem;
  background: rgba(0, 0, 0, 0.05);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 500;
}

/* Recommendations */
.recommendationsList {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.recommendation {
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid;
  background: #f8f9fa;
}

.recommendation.priorityHigh {
  border-color: #dc3545;
  background: rgba(220, 53, 69, 0.05);
}

.recommendation.priorityMedium {
  border-color: #ffc107;
  background: rgba(255, 193, 7, 0.05);
}

.recommendation.priorityLow {
  border-color: #28a745;
  background: rgba(40, 167, 69, 0.05);
}

.recommendationHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  gap: 1rem;
}

.recommendationTitle {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  flex: 1;
}

.recommendationMeta {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.recommendationCategory,
.recommendationPriority {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.recommendationCategory {
  background: #007bff;
  color: white;
}

.recommendationPriority {
  background: #6c757d;
  color: white;
}

.priorityHigh .recommendationPriority {
  background: #dc3545;
}

.priorityMedium .recommendationPriority {
  background: #ffc107;
  color: #212529;
}

.priorityLow .recommendationPriority {
  background: #28a745;
}

.recommendationDescription {
  margin: 0 0 1rem 0;
  color: #495057;
  line-height: 1.6;
}

.recommendationImpact {
  margin: 0 0 1rem 0;
  font-weight: 500;
  color: #28a745;
}

.actionItems {
  margin-bottom: 1rem;
}

.actionItems h5 {
  margin: 0 0 0.5rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #2c3e50;
}

.actionItems ul {
  margin: 0;
  padding-left: 1.5rem;
}

.actionItems li {
  margin-bottom: 0.25rem;
  color: #495057;
}

.recommendationMetrics {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.recommendationMetric {
  font-size: 0.75rem;
  background: rgba(0, 0, 0, 0.05);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 500;
}

/* Booking Insights */
.insightGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.insightItem {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.insightItem h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Peak Hours */
.peakHoursList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.peakHour {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: white;
  border-radius: 6px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.peakTime {
  font-weight: 600;
  color: #667eea;
}

.peakBookings {
  font-size: 0.875rem;
  color: #6c757d;
}

/* Services */
.servicesList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.serviceItem {
  padding: 0.75rem;
  background: white;
  border-radius: 6px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.serviceName {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.serviceStats {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: #6c757d;
  flex-wrap: wrap;
}

/* Customer Stats */
.customerStats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1rem;
}

.customerStat {
  text-align: center;
  padding: 1rem;
  background: white;
  border-radius: 6px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.statValue {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 0.25rem;
}

.statLabel {
  display: block;
  font-size: 0.75rem;
  color: #6c757d;
  font-weight: 500;
  text-transform: uppercase;
}

/* Footer */
.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  flex-wrap: wrap;
  gap: 1rem;
}

.footerInfo {
  display: flex;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #6c757d;
  flex-wrap: wrap;
}

.confidenceLevel {
  font-size: 0.75rem;
  color: #6c757d;
  font-weight: 500;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .controls {
    justify-content: center;
  }
  
  .summaryGrid {
    grid-template-columns: 1fr;
  }
  
  .insightGrid {
    grid-template-columns: 1fr;
  }
  
  .alertHeader,
  .recommendationHeader {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .recommendationMeta {
    align-self: stretch;
  }
  
  .footer {
    flex-direction: column;
    text-align: center;
  }
  
  .customerStats {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .aiDashboard {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: #ecf0f1;
  }
  
  .summaryCard,
  .alertsCard,
  .recommendationsCard,
  .bookingInsights {
    background: #34495e;
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .dateInput,
  .typeSelector {
    background: #2c3e50;
    color: #ecf0f1;
    border-color: rgba(255, 255, 255, 0.2);
  }
  
  .summaryMetric,
  .insightItem {
    background: linear-gradient(135deg, #2c3e50, #34495e);
  }
  
  .peakHour,
  .serviceItem,
  .customerStat {
    background: #2c3e50;
    border-color: rgba(255, 255, 255, 0.1);
  }
}
