/**
 * Mobile Dashboard Navigation Component
 * Ocean Soul Sparkles - Enhanced Mobile Experience
 */

import { useState, useEffect } from 'react'
import { useSwipeable } from 'react-swipeable'
import styles from '@/styles/admin/MobileDashboardNavigation.module.css'

export default function MobileDashboardNavigation({ 
  sections, 
  activeSection, 
  onSectionChange,
  className = '' 
}) {
  const [isVisible, setIsVisible] = useState(true)
  const [lastScrollY, setLastScrollY] = useState(0)

  // Auto-hide navigation on scroll down, show on scroll up
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY
      
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setIsVisible(false) // Hide when scrolling down
      } else {
        setIsVisible(true) // Show when scrolling up
      }
      
      setLastScrollY(currentScrollY)
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [lastScrollY])

  // Swipe handlers for section navigation
  const swipeHandlers = useSwipeable({
    onSwipedLeft: () => {
      const currentIndex = sections.findIndex(section => section.id === activeSection)
      const nextIndex = Math.min(currentIndex + 1, sections.length - 1)
      if (nextIndex !== currentIndex) {
        onSectionChange(sections[nextIndex].id)
      }
    },
    onSwipedRight: () => {
      const currentIndex = sections.findIndex(section => section.id === activeSection)
      const prevIndex = Math.max(currentIndex - 1, 0)
      if (prevIndex !== currentIndex) {
        onSectionChange(sections[prevIndex].id)
      }
    },
    trackMouse: false,
    trackTouch: true,
    delta: 50
  })

  const handleSectionClick = (sectionId) => {
    onSectionChange(sectionId)
    
    // Smooth scroll to section
    const element = document.getElementById(`section-${sectionId}`)
    if (element) {
      element.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'start',
        inline: 'nearest'
      })
    }
  }

  return (
    <div 
      className={`${styles.container} ${isVisible ? styles.visible : styles.hidden} ${className}`}
      {...swipeHandlers}
    >
      <div className={styles.navigation}>
        <div className={styles.sectionTabs}>
          {sections.map((section, index) => (
            <button
              key={section.id}
              className={`${styles.sectionTab} ${
                activeSection === section.id ? styles.active : ''
              }`}
              onClick={() => handleSectionClick(section.id)}
              aria-label={`Navigate to ${section.label}`}
            >
              <span className={styles.sectionIcon}>{section.icon}</span>
              <span className={styles.sectionLabel}>{section.label}</span>
              {section.badge && (
                <span className={styles.sectionBadge}>{section.badge}</span>
              )}
            </button>
          ))}
        </div>
        
        {/* Swipe indicator */}
        <div className={styles.swipeIndicator}>
          <div className={styles.swipeHint}>
            <span className={styles.swipeIcon}>👈</span>
            <span className={styles.swipeText}>Swipe to navigate</span>
            <span className={styles.swipeIcon}>👉</span>
          </div>
        </div>
      </div>
      
      {/* Progress indicator */}
      <div className={styles.progressContainer}>
        <div className={styles.progressTrack}>
          {sections.map((section, index) => (
            <div
              key={section.id}
              className={`${styles.progressDot} ${
                activeSection === section.id ? styles.activeDot : ''
              }`}
            />
          ))}
        </div>
      </div>
    </div>
  )
}

/**
 * Mobile Section Container Component
 */
export function MobileDashboardSection({ 
  id, 
  isActive, 
  children, 
  className = '' 
}) {
  return (
    <div
      id={`section-${id}`}
      className={`${styles.section} ${isActive ? styles.activeSection : ''} ${className}`}
      role="tabpanel"
      aria-hidden={!isActive}
    >
      {children}
    </div>
  )
}

/**
 * Mobile Quick Actions Floating Button
 */
export function MobileQuickActionsFAB({ 
  actions, 
  onActionClick,
  className = '' 
}) {
  const [isExpanded, setIsExpanded] = useState(false)

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded)
  }

  const handleActionClick = (action) => {
    onActionClick(action)
    setIsExpanded(false) // Close after action
  }

  return (
    <div className={`${styles.fabContainer} ${className}`}>
      {/* Backdrop */}
      {isExpanded && (
        <div 
          className={styles.fabBackdrop}
          onClick={() => setIsExpanded(false)}
        />
      )}
      
      {/* Action buttons */}
      {isExpanded && (
        <div className={styles.fabActions}>
          {actions.map((action, index) => (
            <button
              key={action.id}
              className={`${styles.fabAction} ${styles[action.type] || ''}`}
              onClick={() => handleActionClick(action)}
              style={{ 
                animationDelay: `${index * 50}ms`,
                transform: `translateY(-${(index + 1) * 60}px)`
              }}
              aria-label={action.label}
            >
              <span className={styles.fabActionIcon}>{action.icon}</span>
              <span className={styles.fabActionLabel}>{action.label}</span>
            </button>
          ))}
        </div>
      )}
      
      {/* Main FAB button */}
      <button
        className={`${styles.fab} ${isExpanded ? styles.fabExpanded : ''}`}
        onClick={toggleExpanded}
        aria-label={isExpanded ? 'Close quick actions' : 'Open quick actions'}
        aria-expanded={isExpanded}
      >
        <span className={styles.fabIcon}>
          {isExpanded ? '✕' : '⚡'}
        </span>
      </button>
    </div>
  )
}

/**
 * Mobile Touch-Optimized Card Component
 */
export function MobileTouchCard({ 
  title, 
  children, 
  onTap,
  onLongPress,
  className = '',
  ...props 
}) {
  const [isPressed, setIsPressed] = useState(false)
  const [pressTimer, setPressTimer] = useState(null)

  const handleTouchStart = (e) => {
    setIsPressed(true)
    
    // Long press detection
    if (onLongPress) {
      const timer = setTimeout(() => {
        onLongPress(e)
        setIsPressed(false)
      }, 500) // 500ms for long press
      
      setPressTimer(timer)
    }
  }

  const handleTouchEnd = (e) => {
    setIsPressed(false)
    
    if (pressTimer) {
      clearTimeout(pressTimer)
      setPressTimer(null)
    }
    
    if (onTap) {
      onTap(e)
    }
  }

  const handleTouchCancel = () => {
    setIsPressed(false)
    
    if (pressTimer) {
      clearTimeout(pressTimer)
      setPressTimer(null)
    }
  }

  return (
    <div
      className={`${styles.touchCard} ${isPressed ? styles.pressed : ''} ${className}`}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      onTouchCancel={handleTouchCancel}
      {...props}
    >
      {title && <h3 className={styles.touchCardTitle}>{title}</h3>}
      <div className={styles.touchCardContent}>
        {children}
      </div>
    </div>
  )
}
