/**
 * QuickBooks API Integration for Ocean Soul Sparkles
 * Provides QuickBooks Online API integration with financial data synchronization
 * 
 * Phase 7.4: Business Management Integrations
 */

import oauthManager from '../oauth-manager'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, AuditLogger } from '../security-utils'

/**
 * QuickBooks API Client
 */
export class QuickBooksClient {
  constructor(userId) {
    this.userId = userId
    this.baseUrl = 'https://sandbox-quickbooks.api.intuit.com' // Use sandbox for development
    this.discoveryUrl = 'https://developer.api.intuit.com/.well-known/connect_to_quickbooks'
    this.accessToken = null
    this.companyId = null
  }

  /**
   * Initialize QuickBooks client with user credentials
   */
  async initialize() {
    try {
      // Get user's OAuth tokens
      const tokens = await oauthManager.getTokens(this.userId, 'quickbooks')
      
      if (!tokens) {
        throw new Error('No QuickBooks credentials found for user')
      }

      // Check if tokens need refresh
      if (oauthManager.needsRefresh(tokens.expires_at)) {
        const refreshedTokens = await oauthManager.refreshTokens(this.userId, 'quickbooks')
        tokens.access_token = refreshedTokens.access_token
      }

      this.accessToken = tokens.access_token
      this.companyId = tokens.realmId || tokens.company_id

      if (!this.companyId) {
        throw new Error('No QuickBooks company ID found')
      }

      return true
    } catch (error) {
      console.error('Failed to initialize QuickBooks client:', error)
      
      await AuditLogger.logIntegrationActivity(
        this.userId,
        'quickbooks',
        'initialization_failed',
        'error',
        { error: error.message }
      )
      
      return false
    }
  }

  /**
   * Make authenticated API request to QuickBooks
   */
  async makeRequest(endpoint, method = 'GET', data = null) {
    return await RetryHandler.withRetry(async () => {
      const url = `${this.baseUrl}/v3/company/${this.companyId}/${endpoint}`
      
      const options = {
        method,
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      }

      if (data && (method === 'POST' || method === 'PUT')) {
        options.body = JSON.stringify(data)
      }

      const response = await fetch(url, options)

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`QuickBooks API error: ${response.status} - ${errorText}`)
      }

      return await response.json()
    })
  }

  /**
   * Get company information
   */
  async getCompanyInfo() {
    const response = await this.makeRequest('companyinfo/1')
    return response.QueryResponse?.CompanyInfo?.[0] || null
  }

  /**
   * Get customers
   */
  async getCustomers(limit = 100, offset = 0) {
    const query = `SELECT * FROM Customer MAXRESULTS ${limit} STARTPOSITION ${offset + 1}`
    const response = await this.makeRequest(`query?query=${encodeURIComponent(query)}`)
    
    return {
      customers: response.QueryResponse?.Customer || [],
      hasMore: (response.QueryResponse?.Customer?.length || 0) === limit
    }
  }

  /**
   * Create customer
   */
  async createCustomer(customerData) {
    const customer = {
      Name: customerData.name,
      CompanyName: customerData.companyName || customerData.name,
      PrimaryEmailAddr: customerData.email ? { Address: customerData.email } : undefined,
      PrimaryPhone: customerData.phone ? { FreeFormNumber: customerData.phone } : undefined,
      BillAddr: customerData.address ? {
        Line1: customerData.address.line1,
        City: customerData.address.city,
        CountrySubDivisionCode: customerData.address.state,
        PostalCode: customerData.address.postalCode,
        Country: customerData.address.country || 'US'
      } : undefined,
      Notes: customerData.notes
    }

    const response = await this.makeRequest('customer', 'POST', { Customer: customer })
    
    await AuditLogger.logIntegrationActivity(
      this.userId,
      'quickbooks',
      'customer_created',
      'success',
      { customerId: response.Customer?.Id, customerName: customerData.name }
    )

    return response.Customer
  }

  /**
   * Get items (services/products)
   */
  async getItems(limit = 100) {
    const query = `SELECT * FROM Item WHERE Type='Service' OR Type='Inventory' MAXRESULTS ${limit}`
    const response = await this.makeRequest(`query?query=${encodeURIComponent(query)}`)
    
    return response.QueryResponse?.Item || []
  }

  /**
   * Create service item
   */
  async createServiceItem(itemData) {
    const item = {
      Name: itemData.name,
      Description: itemData.description,
      Type: 'Service',
      UnitPrice: itemData.price,
      IncomeAccountRef: {
        value: itemData.incomeAccountId || '1', // Default income account
        name: 'Services'
      }
    }

    const response = await this.makeRequest('item', 'POST', { Item: item })
    
    await AuditLogger.logIntegrationActivity(
      this.userId,
      'quickbooks',
      'service_item_created',
      'success',
      { itemId: response.Item?.Id, itemName: itemData.name }
    )

    return response.Item
  }

  /**
   * Get invoices
   */
  async getInvoices(limit = 100, offset = 0) {
    const query = `SELECT * FROM Invoice MAXRESULTS ${limit} STARTPOSITION ${offset + 1} ORDERBY TxnDate DESC`
    const response = await this.makeRequest(`query?query=${encodeURIComponent(query)}`)
    
    return {
      invoices: response.QueryResponse?.Invoice || [],
      hasMore: (response.QueryResponse?.Invoice?.length || 0) === limit
    }
  }

  /**
   * Create invoice
   */
  async createInvoice(invoiceData) {
    const invoice = {
      CustomerRef: {
        value: invoiceData.customerId
      },
      Line: invoiceData.lineItems.map(item => ({
        Amount: item.amount,
        DetailType: 'SalesItemLineDetail',
        SalesItemLineDetail: {
          ItemRef: {
            value: item.itemId,
            name: item.name
          },
          Qty: item.quantity || 1,
          UnitPrice: item.unitPrice || item.amount
        }
      })),
      DueDate: invoiceData.dueDate,
      TxnDate: invoiceData.invoiceDate || new Date().toISOString().split('T')[0],
      PrivateNote: invoiceData.notes,
      CustomerMemo: invoiceData.memo ? { value: invoiceData.memo } : undefined
    }

    const response = await this.makeRequest('invoice', 'POST', { Invoice: invoice })
    
    await AuditLogger.logIntegrationActivity(
      this.userId,
      'quickbooks',
      'invoice_created',
      'success',
      { 
        invoiceId: response.Invoice?.Id,
        customerId: invoiceData.customerId,
        amount: invoiceData.lineItems.reduce((sum, item) => sum + item.amount, 0)
      }
    )

    return response.Invoice
  }

  /**
   * Get expenses
   */
  async getExpenses(limit = 100, offset = 0) {
    const query = `SELECT * FROM Purchase WHERE PaymentType='Cash' OR PaymentType='CreditCard' MAXRESULTS ${limit} STARTPOSITION ${offset + 1} ORDERBY TxnDate DESC`
    const response = await this.makeRequest(`query?query=${encodeURIComponent(query)}`)
    
    return {
      expenses: response.QueryResponse?.Purchase || [],
      hasMore: (response.QueryResponse?.Purchase?.length || 0) === limit
    }
  }

  /**
   * Create expense
   */
  async createExpense(expenseData) {
    const expense = {
      PaymentType: expenseData.paymentType || 'Cash',
      AccountRef: {
        value: expenseData.expenseAccountId || '1' // Default expense account
      },
      TxnDate: expenseData.date || new Date().toISOString().split('T')[0],
      Line: [{
        Amount: expenseData.amount,
        DetailType: 'AccountBasedExpenseLineDetail',
        AccountBasedExpenseLineDetail: {
          AccountRef: {
            value: expenseData.categoryAccountId || expenseData.expenseAccountId || '1'
          }
        },
        Description: expenseData.description
      }],
      PrivateNote: expenseData.notes
    }

    const response = await this.makeRequest('purchase', 'POST', { Purchase: expense })
    
    await AuditLogger.logIntegrationActivity(
      this.userId,
      'quickbooks',
      'expense_created',
      'success',
      { 
        expenseId: response.Purchase?.Id,
        amount: expenseData.amount,
        description: expenseData.description
      }
    )

    return response.Purchase
  }

  /**
   * Get chart of accounts
   */
  async getAccounts() {
    const query = `SELECT * FROM Account WHERE Active=true`
    const response = await this.makeRequest(`query?query=${encodeURIComponent(query)}`)
    
    return response.QueryResponse?.Account || []
  }

  /**
   * Get profit and loss report
   */
  async getProfitLossReport(startDate, endDate) {
    const params = new URLSearchParams({
      start_date: startDate,
      end_date: endDate,
      summarize_column_by: 'Month'
    })

    const response = await this.makeRequest(`reports/ProfitAndLoss?${params}`)
    return response
  }

  /**
   * Get sales report
   */
  async getSalesReport(startDate, endDate) {
    const params = new URLSearchParams({
      start_date: startDate,
      end_date: endDate,
      summarize_column_by: 'Month'
    })

    const response = await this.makeRequest(`reports/SalesByCustomer?${params}`)
    return response
  }

  /**
   * Sync Ocean Soul Sparkles booking to QuickBooks invoice
   */
  async syncBookingToInvoice(bookingData) {
    try {
      // First, ensure customer exists
      let customerId = bookingData.quickbooksCustomerId
      
      if (!customerId) {
        const customer = await this.createCustomer({
          name: bookingData.customerName,
          email: bookingData.customerEmail,
          phone: bookingData.customerPhone,
          notes: `Ocean Soul Sparkles customer - Booking #${bookingData.bookingId}`
        })
        customerId = customer.Id
      }

      // Create invoice for the booking
      const invoice = await this.createInvoice({
        customerId,
        lineItems: [{
          itemId: bookingData.serviceItemId || '1', // Default service item
          name: bookingData.serviceName,
          amount: bookingData.totalAmount,
          quantity: 1,
          unitPrice: bookingData.totalAmount
        }],
        dueDate: bookingData.serviceDate,
        invoiceDate: bookingData.bookingDate,
        notes: `Ocean Soul Sparkles booking #${bookingData.bookingId}`,
        memo: `${bookingData.serviceName} - ${bookingData.artistName}`
      })

      return {
        success: true,
        invoiceId: invoice.Id,
        customerId,
        invoiceNumber: invoice.DocNumber
      }

    } catch (error) {
      console.error('Failed to sync booking to QuickBooks:', error)
      
      await AuditLogger.logIntegrationActivity(
        this.userId,
        'quickbooks',
        'booking_sync_failed',
        'error',
        { 
          bookingId: bookingData.bookingId,
          error: error.message
        }
      )

      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * Test connection to QuickBooks API
   */
  async testConnection() {
    try {
      if (!await this.initialize()) {
        return { success: false, error: 'Failed to initialize QuickBooks client' }
      }

      // Try to get company info as a test
      const companyInfo = await this.getCompanyInfo()
      
      await AuditLogger.logIntegrationActivity(
        this.userId,
        'quickbooks',
        'connection_test',
        'success',
        { companyName: companyInfo?.Name }
      )

      return { 
        success: true, 
        companyInfo,
        message: 'QuickBooks connection successful'
      }
    } catch (error) {
      await AuditLogger.logIntegrationActivity(
        this.userId,
        'quickbooks',
        'connection_test',
        'error',
        { error: error.message }
      )

      return { 
        success: false, 
        error: error.message 
      }
    }
  }
}

export default QuickBooksClient
