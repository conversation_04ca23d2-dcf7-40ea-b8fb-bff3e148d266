import { useState, useEffect, useCallback } from 'react'
import Head from 'next/head'
import AdminLayout from '@/components/admin/AdminLayout' // Assuming AdminLayout exists
import ApplicationsList from '@/components/admin/users/ApplicationsList'
import ApplicationReviewModal from '@/components/admin/users/ApplicationReviewModal'
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// Placeholder for API call, will be refined in a later step
// async function fetchApplications(filters) {
//   // const query = new URLSearchParams(filters).toString();
//   // const response = await fetch(`/api/admin/users/applications?${query}`);
//   // if (!response.ok) {
//   //   throw new Error('Failed to fetch applications');
//   // }
//   // return response.json();
//   console.log("TODO: Implement fetchApplications with filters:", filters);
//   return { applications: [], totalCount: 0, error: null }; // Placeholder
// }

export default function ManageApplicationsPage() {
  const [applications, setApplications] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)
  const [applicationStats, setApplicationStats] = useState({
    pending: 0,
    under_review: 0,
    approved: 0,
    rejected: 0,
    total: 0,
  });
  const [filters, setFilters] = useState({
    status: 'all',
    type: 'all',
    search: '',
    sortBy: 'created_at',
    sortOrder: 'desc',
    page: 1,
    limit: 15, // Or your preferred default limit
  })
  const [totalApplications, setTotalApplications] = useState(0) // For pagination

  const [selectedApplication, setSelectedApplication] = useState(null)
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false)

  const loadApplications = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const queryParams = new URLSearchParams({
        status: filters.status,
        type: filters.type,
        search: filters.search,
        sortBy: filters.sortBy,
        sortOrder: filters.sortOrder,
        limit: filters.limit.toString(),
        offset: ((filters.page - 1) * filters.limit).toString(),
      });
      const response = await fetch(`/api/admin/users/applications?${queryParams.toString()}`);
      if (!response.ok) {
        const errData = await response.json();
        throw new Error(errData.error || `Failed to fetch applications. Status: ${response.status}`);
      }
      const data = await response.json();
      setApplications(data.applications || []);
      setTotalApplications(data.pagination?.total || 0);
      setApplicationStats(data.stats || { pending: 0, under_review: 0, approved: 0, rejected: 0, total: 0 });
    } catch (err) {
      setError(err.message);
      toast.error(`Error loading applications: ${err.message}`);
      setApplications([]); // Clear applications on error
      setTotalApplications(0);
    } finally {
      setIsLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    loadApplications();
  }, [loadApplications]);

  const handleFilterChange = (newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters, page: 1 /* Reset to page 1 on filter change */ }))
  }

  const handleReviewApplication = (application) => {
    setSelectedApplication(application)
    setIsReviewModalOpen(true)
  }

  const handleCloseReviewModal = () => {
    setIsReviewModalOpen(false)
    setSelectedApplication(null)
  }

  const handleSubmitReview = async (applicationId, reviewStatus, reviewNotes) => {
    console.log('Submitting review:', { applicationId, reviewStatus, reviewNotes });
    // setIsLoading(true); // Consider adding loading state for the modal/submit button

    try {
      const response = await fetch(`/api/admin/users/applications/${applicationId}/review`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: reviewStatus, notes: reviewNotes }),
      });

      if (!response.ok) {
        const errData = await response.json();
        throw new Error(errData.error || `Failed to submit review. Status: ${response.status}`);
      }

      const result = await response.json();
      toast.success(result.message || 'Review submitted successfully!');
      handleCloseReviewModal();
      loadApplications();
      console.log('Review submitted, TODO: refresh application list');
    } catch (err) {
      toast.error(`Error submitting review: ${err.message}`);
      console.error('Error submitting review:', err);
      // Potentially leave modal open on error, or close it:
      // handleCloseReviewModal();
    } finally {
      // setIsLoading(false);
    }
  };


  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= Math.ceil(totalApplications / filters.limit)) {
      setFilters(prev => ({ ...prev, page: newPage }));
    }
  };

  return (
    <AdminLayout>
      <Head>
        <title>Manage Applications | Admin Panel</title>
      </Head>
      <div style={{ padding: '20px' }}> {/* Basic styling */}
        <h1>Artist & Braider Applications</h1>

        <div style={{ marginBottom: '20px', display: 'flex', gap: '10px', flexWrap: 'wrap', alignItems: 'center' }}>
          <h4>Application Stats:</h4>
          <p>Total: {applicationStats.total}</p>
          <p>Pending: {applicationStats.pending}</p>
          <p>Under Review: {applicationStats.under_review}</p>
          <p>Approved: {applicationStats.approved}</p>
          <p>Rejected: {applicationStats.rejected}</p>
        </div>

        <ApplicationsList
          applications={applications}
          loading={isLoading}
          filters={filters}
          onFilterChange={handleFilterChange}
          onReviewApplication={handleReviewApplication}
          // onDeleteApplication and onCancelApplication can be added later if needed
        />

        {isReviewModalOpen && selectedApplication && (
          <ApplicationReviewModal
            application={selectedApplication}
            onClose={handleCloseReviewModal}
            onSubmit={handleSubmitReview}
          />
        )}

        {error && <p style={{ color: 'red' }}>Error loading applications: {error}</p>}

        <div style={{ marginTop: '20px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <button
            onClick={() => handlePageChange(filters.page - 1)}
            disabled={filters.page <= 1 || isLoading}
          >
            Previous
          </button>
          <span>Page {filters.page} of {Math.ceil(totalApplications / filters.limit) || 1}</span>
          <button
            onClick={() => handlePageChange(filters.page + 1)}
            disabled={filters.page >= Math.ceil(totalApplications / filters.limit) || isLoading}
          >
            Next
          </button>
        </div>
      </div>
    </AdminLayout>
  )
}
