/**
 * Unified Supabase Client
 *
 * This module provides a single source of truth for Supabase authentication
 * across the application, replacing the fragmented approach previously used.
 *
 * It exports:
 * - supabase: The main Supabase client for client-side use
 * - supabaseAdmin: The admin client for server-side use (bypasses RLS)
 * - getAdminClient: A function to get the admin client for server-side use
 */

import { createClient } from '@supabase/supabase-js'

/**
 * Known admin user IDs
 * Centralized list to avoid duplication
 */
export const KNOWN_ADMIN_IDS = [
  '8c59a3bc-a96b-4555-bdc4-6abe905ae761', // <EMAIL>
  'c6080246-db51-485e-8e29-69be7cc86cdb'  // <EMAIL>
];

/**
 * Check if a user ID is a known admin
 *
 * @param {string} userId - User ID to check
 * @returns {boolean} - True if user is a known admin
 */
export function isKnownAdmin(userId) {
  return KNOWN_ADMIN_IDS.includes(userId);
}

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

// Default headers for all requests
const DEFAULT_HEADERS = {
  'X-Client-Info': 'ocean-soul-sparkles@1.0.0',
  'X-Protocol-Version': '1.0'
}

/**
 * Determine appropriate cache-control header based on the request URL and method
 * @param {string} url - The request URL
 * @param {string} method - The HTTP method
 * @returns {string} Cache-control header value
 */
const getCacheControlHeader = (url, method = 'GET') => {
  // Only apply cache headers to GET requests
  if (method.toUpperCase() !== 'GET') {
    return 'no-cache';
  }

  // Parse URL to determine table/endpoint
  const urlString = typeof url === 'string' ? url : url.toString();

  // Real-time data - no cache or very short cache
  if (urlString.includes('/bookings') ||
      urlString.includes('/payments') ||
      urlString.includes('status=eq.pending') ||
      urlString.includes('payment_status=eq.completed')) {
    return 'no-cache, no-store, must-revalidate';
  }

  // Semi-static data - moderate cache
  if (urlString.includes('/customers') ||
      urlString.includes('/products') ||
      urlString.includes('/services')) {
    return 'max-age=300, stale-while-revalidate=60'; // 5 minutes cache, 1 minute stale
  }

  // Admin-specific endpoints - shorter cache
  if (urlString.includes('/admin/')) {
    return 'max-age=60, no-cache'; // 1 minute cache for admin data
  }

  // Default cache for other requests
  return 'max-age=180, stale-while-revalidate=30'; // 3 minutes cache, 30 seconds stale
};

/**
 * Custom fetch implementation with timeout
 * Prevents requests from hanging indefinitely
 * Note: Cache headers are handled at the API response level, not request level
 */
const fetchWithTimeout = async (resource, options = {}) => {
  const controller = new AbortController()
  const timeout = options.timeout || 15000 // Increased to 15 second timeout for better reliability

  // Generate a request ID for logging
  const requestId = Math.random().toString(36).substring(2, 8)

  // Log the request if in development mode
  if (process.env.NODE_ENV === 'development' || process.env.NEXT_PUBLIC_DEBUG_AUTH === 'true') {
    console.log(`[${requestId}] Fetch request to: ${typeof resource === 'string' ? resource : 'Request object'}`)
  }

  const id = setTimeout(() => {
    controller.abort()
    console.error(`[${requestId}] Fetch request timed out after ${timeout}ms`)
  }, timeout)

  try {
    const response = await fetch(resource, {
      ...options,
      signal: controller.signal
    })

    // Log the response status if in development mode
    if (process.env.NODE_ENV === 'development' || process.env.NEXT_PUBLIC_DEBUG_AUTH === 'true') {
      console.log(`[${requestId}] Fetch response status: ${response.status}`)
    }

    return response
  } catch (error) {
    // Log fetch errors
    console.error(`[${requestId}] Fetch error:`, error.name, error.message)
    throw error
  } finally {
    clearTimeout(id)
  }
}

/**
 * Enhanced singleton Supabase client instance
 * Prevents multiple GoTrueClient instances with better production support
 */
let supabaseInstance = null;
let isCreatingInstance = false;
let creationPromise = null;

/**
 * Create Supabase client with enhanced singleton protection
 */
function createSupabaseClient() {
  // Return existing instance if available
  if (supabaseInstance) {
    return supabaseInstance;
  }

  // If already creating, wait for the creation promise
  if (isCreatingInstance && creationPromise) {
    return creationPromise;
  }

  // Start creation process
  isCreatingInstance = true;
  console.log('[Supabase] Creating singleton client instance');

  try {
    // Check for existing global instance (production mode protection)
    if (typeof window !== 'undefined') {
      if (window.__SUPABASE_CLIENT_INSTANCE) {
        console.log('[Supabase] Using existing global client instance');
        supabaseInstance = window.__SUPABASE_CLIENT_INSTANCE;
        isCreatingInstance = false;
        creationPromise = null;
        return supabaseInstance;
      }
    }

    supabaseInstance = createClient(supabaseUrl, supabaseKey, {
      auth: {
        persistSession: true,
        autoRefreshToken: true,
        detectSessionInUrl: false, // Disable to prevent redirect loops
        storageKey: 'oss_auth_token_cache',
        storage: typeof window !== 'undefined' ? window.localStorage : undefined,
        cookieOptions: {
          path: '/',
          sameSite: 'Lax',
          secure: process.env.NODE_ENV === 'production'
        }
      },
      global: {
        headers: DEFAULT_HEADERS,
        fetch: fetchWithTimeout
      },
      realtime: {
        params: {
          eventsPerSecond: 2
        }
      }
    });

    // Store globally to prevent multiple instances in production
    if (typeof window !== 'undefined') {
      window.__SUPABASE_CLIENT_INSTANCE = supabaseInstance;
      window.__SUPABASE_CLIENT_COUNT = (window.__SUPABASE_CLIENT_COUNT || 0) + 1;
      console.log(`[Supabase] Client instance count: ${window.__SUPABASE_CLIENT_COUNT}`);

      // Add debugging info
      if (window.__SUPABASE_CLIENT_COUNT > 1) {
        console.warn(`[Supabase] WARNING: Multiple client instances detected (${window.__SUPABASE_CLIENT_COUNT})`);
        console.trace('[Supabase] Client creation stack trace');
      }
    }

    isCreatingInstance = false;
    creationPromise = null;
    return supabaseInstance;
  } catch (error) {
    console.error('[Supabase] Error creating client instance:', error);
    isCreatingInstance = false;
    creationPromise = null;
    throw error;
  }
}

/**
 * Get the main Supabase client for client-side use
 * Enhanced singleton pattern with production mode support
 */
export const supabase = createSupabaseClient();

/**
 * Supabase admin client with service role key
 * This bypasses RLS policies and should only be used server-side
 *
 * Note: This will throw an error if used client-side or if the service role key is not available
 */
export const supabaseAdmin = typeof window === 'undefined' && supabaseServiceKey
  ? createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      },
      global: {
        headers: {
          ...DEFAULT_HEADERS,
          'X-Client-Info': 'ocean-soul-sparkles-admin@1.0.0'
        },
        fetch: fetchWithTimeout
      }
    })
  : null;

// Singleton admin client to prevent repeated creation and reduce log noise
let adminClientInstance = null;
let adminClientCreatedAt = null;
const ADMIN_CLIENT_TTL = 5 * 60 * 1000; // 5 minutes

/**
 * Get the admin client with service role credentials
 * This should only be used server-side and never exposed to the client
 * Uses singleton pattern to prevent repeated client creation
 *
 * @returns {Object} Supabase admin client
 * @throws {Error} If called client-side or if service role key is not available
 */
export const getAdminClient = () => {
  // Generate a request ID for logging
  const requestId = Math.random().toString(36).substring(2, 8);

  // Check environment
  if (typeof window !== 'undefined') {
    console.error(`[${requestId}] Attempted to use admin client on client-side`);
    throw new Error('Admin client can only be used server-side');
  }

  // Check for service key
  if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
    console.error(`[${requestId}] SUPABASE_SERVICE_ROLE_KEY is missing`);
    throw new Error('SUPABASE_SERVICE_ROLE_KEY is required for admin operations');
  }

  if (!supabaseServiceKey) {
    console.error(`[${requestId}] supabaseServiceKey is not defined`);
    throw new Error('SUPABASE_SERVICE_ROLE_KEY is not defined');
  }

  // Check for URL
  if (!supabaseUrl) {
    console.error(`[${requestId}] NEXT_PUBLIC_SUPABASE_URL is missing`);
    throw new Error('NEXT_PUBLIC_SUPABASE_URL is required for admin operations');
  }

  // Check if we have a valid cached client
  const now = Date.now();
  if (adminClientInstance && adminClientCreatedAt && (now - adminClientCreatedAt < ADMIN_CLIENT_TTL)) {
    // Only log occasionally to reduce noise
    if (Math.random() < 0.1) { // 10% chance to log
      console.log(`[${requestId}] Using cached admin client (age: ${Math.round((now - adminClientCreatedAt) / 1000)}s)`);
    }
    return adminClientInstance;
  }

  try {
    // Create and cache the admin client
    console.log(`[${requestId}] Creating new admin client with service role key`);
    adminClientInstance = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      },
      global: {
        headers: {
          ...DEFAULT_HEADERS,
          'X-Client-Info': 'ocean-soul-sparkles-admin@1.0.0'
        },
        fetch: fetchWithTimeout
      }
    });

    adminClientCreatedAt = now;
    console.log(`[${requestId}] Admin client created successfully and cached`);
    return adminClientInstance;
  } catch (error) {
    console.error(`[${requestId}] Error creating admin client:`, error);
    // Clear cache on error
    adminClientInstance = null;
    adminClientCreatedAt = null;
    throw new Error(`Failed to create admin client: ${error.message}`);
  }
}

/**
 * Get a Supabase client (either admin or regular depending on environment)
 * This is a convenience function that automatically chooses the appropriate client
 *
 * @returns {Object} Supabase client
 */
export const getClient = () => {
  // Generate a request ID for logging
  const requestId = Math.random().toString(36).substring(2, 8);

  // On server-side, prefer admin client if available
  if (typeof window === 'undefined') {
    console.log(`[${requestId}] Getting client for server-side use`);
    try {
      return getAdminClient();
    } catch (error) {
      console.warn(`[${requestId}] Admin client not available, falling back to regular client:`, error.message);
      return supabase;
    }
  }

  // On client-side, use regular client
  console.log(`[${requestId}] Getting client for client-side use`);
  return supabase;
};

/**
 * Get the current session
 * Convenience method with timeout protection
 *
 * @returns {Promise<Object>} Session data or null
 */
export const getSession = async () => {
  try {
    // Add timeout protection to prevent hanging
    const sessionPromise = supabase.auth.getSession()
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('getSession timeout')), 8000)
    })

    const { data, error } = await Promise.race([sessionPromise, timeoutPromise])

    if (error) {
      console.error('Error getting session:', error)
      return null
    }

    return data.session
  } catch (error) {
    console.error('Exception in getSession:', error)
    return null
  }
}

/**
 * Get the current user
 * Convenience method with role information
 *
 * @returns {Promise<Object>} User data with role or null
 */
export const getCurrentUser = async () => {
  try {
    console.log('[getCurrentUser] Starting user session check...')

    // Add timeout protection to prevent hanging
    const sessionPromise = getSession()
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('getCurrentUser timeout')), 10000)
    })

    const session = await Promise.race([sessionPromise, timeoutPromise])

    if (!session || !session.user) {
      console.log('[getCurrentUser] No active session found')
      return { user: null, role: null }
    }

    console.log(`[getCurrentUser] Session found for user: ${session.user.email} (${session.user.id})`)

    // Get user role from user_roles table with enhanced error handling
    console.log('[getCurrentUser] Fetching user role from database...')
    const { data: roleData, error: roleError } = await supabase
      .from('user_roles')
      .select('role')
      .eq('id', session.user.id)
      .single()

    if (roleError) {
      console.error('[getCurrentUser] Error fetching user role:', roleError.message, 'Code:', roleError.code)

      // Check if it's a "not found" error (PGRST116)
      if (roleError.code === 'PGRST116') {
        console.log('[getCurrentUser] PGRST116: No role record found for user. Checking metadata and fallbacks...')
        if (session.user.user_metadata) {
          console.log('[getCurrentUser] User metadata:', session.user.user_metadata)
        }

        // If account_activated is true, but role still not found, return null role
        if (session.user.user_metadata && session.user.user_metadata.account_activated === true) {
          console.warn(`[getCurrentUser] User ${session.user.email} has account_activated:true but no role found. Returning role: null.`)
          return {
            user: session.user,
            role: null // Explicitly null for activated users without a role
          }
        }

        // Fallback for known admin users
        if (isKnownAdmin(session.user.id)) {
          console.log(`[getCurrentUser] User ${session.user.email} is a known admin, assigning 'admin' role.`)
          return {
            user: session.user,
            role: 'admin'
          }
        }

        // Default for non-activated users or if metadata check fails
        console.log(`[getCurrentUser] No specific condition met for ${session.user.email} after PGRST116, defaulting to 'user' role.`)
        return {
          user: session.user,
          role: 'user' // Default for other cases of PGRST116
        }
      } else {
        // Other database errors - log and return null user/role
        console.error('[getCurrentUser] Unhandled database error while fetching role:', roleError)
        // Not throwing here to allow signIn to handle it, but logging severity.
        return { user: session.user, role: null } // Or consider re-throwing based on broader error handling strategy
      }
    }

    if (!roleData || !roleData.role) {
      console.warn('[getCurrentUser] Role data is empty or invalid after successful fetch:', roleData)
      // This case should ideally not happen if roleError handles missing roles.
      // However, as a safeguard, treat as 'user' or null depending on policy.
      // For now, align with the PGRST116 activated user case for consistency.
      return {
        user: session.user,
        role: null // Or 'user' if that's preferred for this unexpected scenario
      }
    }

    console.log(`[getCurrentUser] ✅ User ${session.user.email} (ID: ${session.user.id}) has role: ${roleData.role}. Returning user and role.`)
    return {
      user: session.user,
      role: roleData.role
    }
  } catch (error) {
    console.error('[getCurrentUser] General error:', error.message, error.stack)
    return { user: null, role: null }
  }
}

/**
 * Get the current user with a specific token
 * Used for admin authentication with a provided token
 *
 * @param {string} token - JWT token
 * @returns {Promise<Object>} User data with role or error
 */
export const getCurrentUserWithToken = async (token) => {
  try {
    if (!token) {
      return { user: null, role: null, error: new Error('No token provided') }
    }

    // Get admin client to verify the token
    const adminClient = getAdminClient()

    // Verify token
    const { data, error } = await adminClient.auth.getUser(token)

    if (error) {
      return { user: null, role: null, error }
    }

    if (!data.user) {
      return { user: null, role: null, error: new Error('Invalid token') }
    }

    // Get user role from user_roles table
    const { data: roleData, error: roleError } = await adminClient
      .from('user_roles')
      .select('role')
      .eq('id', data.user.id)
      .single()

    if (roleError) {
      console.error('Error fetching user role with token:', roleError)

      // Fallback for known admin users
      if (isKnownAdmin(data.user.id)) {
        return {
          user: data.user,
          role: 'admin',
          error: null
        }
      }

      return {
        user: data.user,
        role: null,
        error: roleError
      }
    }

    return {
      user: data.user,
      role: roleData.role,
      error: null
    }
  } catch (error) {
    console.error('Error in getCurrentUserWithToken:', error)
    return { user: null, role: null, error }
  }
}

/**
 * Get the current user from a request object
 * Used for server-side authentication in API routes
 *
 * @param {Object} req - HTTP request object
 * @returns {Promise<Object>} User data with role or throws error
 */
export const getCurrentUserFromRequest = async (req) => {
  try {
    // Generate a request ID for logging
    const requestId = Math.random().toString(36).substring(2, 8);
    console.log(`[${requestId}] Getting current user from request`);

    // Extract token from Authorization header
    let token = null;
    const authHeader = req.headers.authorization;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7).trim();
      console.log(`[${requestId}] Token extracted from Authorization header`);
    }

    // Fallback to cookies if no header token
    if (!token && req.cookies) {
      token = req.cookies['sb-access-token'] ||
              req.cookies['supabase-auth-token'] ||
              req.cookies['oss_auth_token'];
      if (token) {
        console.log(`[${requestId}] Token extracted from cookies`);
      }
    }

    // Fallback to X-Auth-Token header
    if (!token && req.headers['x-auth-token']) {
      token = req.headers['x-auth-token'];
      console.log(`[${requestId}] Token extracted from X-Auth-Token header`);
    }

    if (!token) {
      console.warn(`[${requestId}] No authentication token found`);
      throw new Error('No authentication token provided');
    }

    // Validate token and get user/role
    console.log(`[${requestId}] Validating token with getCurrentUserWithToken`);
    const result = await getCurrentUserWithToken(token);

    if (result.error) {
      console.error(`[${requestId}] Token validation failed:`, result.error.message);
      throw result.error;
    }

    if (!result.user) {
      console.warn(`[${requestId}] No user found for token`);
      throw new Error('Invalid authentication token');
    }

    console.log(`[${requestId}] Authentication successful for user: ${result.user.email}, role: ${result.role}`);
    return { user: result.user, role: result.role };
  } catch (error) {
    console.error('Error in getCurrentUserFromRequest:', error);
    throw error;
  }
}

/**
 * Authentication methods for common operations
 */
export const auth = {
  /**
   * Sign in with email and password
   *
   * @param {string} email - User email
   * @param {string} password - User password
   * @returns {Promise<Object>} Auth response
   */
  async signIn(email, password) {
    return await supabase.auth.signInWithPassword({ email, password })
  },

  /**
   * Sign out current user
   *
   * @returns {Promise<Object>} Auth response
   */
  async signOut() {
    return await supabase.auth.signOut()
  },

  /**
   * Get current session
   *
   * @returns {Promise<Object>} Session object
   */
  async getSession() {
    return await supabase.auth.getSession()
  },

  /**
   * Get current user
   *
   * @returns {Promise<Object>} User object
   */
  async getUser() {
    return await supabase.auth.getUser()
  }
}

// Default export for backward compatibility
/**
 * Integration Client Functions
 * Specialized functions for integration management
 */

/**
 * Get integration credentials for a user
 */
export async function getIntegrationCredentials(userId, provider = null) {
  const adminClient = getAdminClient()

  let query = adminClient
    .from('integration_credentials')
    .select('provider, expires_at, updated_at')
    .eq('user_id', userId)

  if (provider) {
    query = query.eq('provider', provider)
  }

  const { data, error } = await query

  if (error) {
    throw new Error(`Failed to get integration credentials: ${error.message}`)
  }

  return data || []
}

/**
 * Get integration settings for a user
 */
export async function getIntegrationSettings(userId, provider = null) {
  const adminClient = getAdminClient()

  let query = adminClient
    .from('integration_settings')
    .select('*')
    .eq('user_id', userId)

  if (provider) {
    query = query.eq('provider', provider)
  }

  const { data, error } = await query

  if (error) {
    throw new Error(`Failed to get integration settings: ${error.message}`)
  }

  return data || []
}

/**
 * Update integration settings for a user
 */
export async function updateIntegrationSettings(userId, provider, settings) {
  const adminClient = getAdminClient()

  const { data, error } = await adminClient
    .from('integration_settings')
    .upsert({
      user_id: userId,
      provider,
      settings,
      updated_at: new Date().toISOString()
    }, {
      onConflict: 'user_id,provider'
    })

  if (error) {
    throw new Error(`Failed to update integration settings: ${error.message}`)
  }

  return data
}

/**
 * Get integration logs for a user
 */
export async function getIntegrationLogs(userId, provider = null, limit = 50) {
  const adminClient = getAdminClient()

  let query = adminClient
    .from('integration_logs')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
    .limit(limit)

  if (provider) {
    query = query.eq('provider', provider)
  }

  const { data, error } = await query

  if (error) {
    throw new Error(`Failed to get integration logs: ${error.message}`)
  }

  return data || []
}

export default supabase

