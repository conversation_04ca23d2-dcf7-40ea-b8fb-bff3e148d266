/**
 * Quick Actions Component - Phase 8: Advanced Customer Experience
 * Ocean Soul Sparkles - Customer Dashboard Quick Actions
 */

import { useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { useMobileOptimization } from '@/lib/hooks/useMobileOptimization'
import { toast } from 'react-toastify'
import styles from '@/styles/customer/QuickActions.module.css'

export default function QuickActions({ customer, onActionComplete }) {
  const router = useRouter()
  const { isMobile, viewport } = useMobileOptimization()
  
  const [loading, setLoading] = useState({})

  const handleQuickAction = async (actionType, actionData = {}) => {
    setLoading(prev => ({ ...prev, [actionType]: true }))

    try {
      switch (actionType) {
        case 'book_appointment':
          router.push('/book-online')
          break

        case 'reschedule_next':
          await handleRescheduleNext()
          break

        case 'view_loyalty':
          router.push('/customer/loyalty')
          break

        case 'contact_support':
          router.push('/customer/support')
          break

        case 'refer_friend':
          await handleReferFriend()
          break

        case 'book_multi_service':
          router.push('/customer/services/multi-booking')
          break

        case 'manage_subscriptions':
          router.push('/customer/subscriptions')
          break

        case 'emergency_reschedule':
          await handleEmergencyReschedule()
          break

        default:
          console.warn('Unknown quick action:', actionType)
      }

      if (onActionComplete) {
        onActionComplete(actionType, actionData)
      }

    } catch (error) {
      console.error('Error handling quick action:', error)
      toast.error('Action failed. Please try again.')
    } finally {
      setLoading(prev => ({ ...prev, [actionType]: false }))
    }
  }

  const handleRescheduleNext = async () => {
    try {
      // Get next upcoming booking
      const response = await fetch('/api/customer/bookings?status=upcoming&limit=1')
      const data = await response.json()

      if (data.success && data.data.length > 0) {
        const nextBooking = data.data[0]
        router.push(`/customer/bookings/${nextBooking.id}/reschedule`)
      } else {
        toast.info('No upcoming bookings to reschedule')
      }
    } catch (error) {
      console.error('Error finding next booking:', error)
      toast.error('Failed to find upcoming booking')
    }
  }

  const handleReferFriend = async () => {
    try {
      // Generate referral link
      const response = await fetch('/api/customer/referral/generate', {
        method: 'POST'
      })
      const data = await response.json()

      if (data.success) {
        // Copy to clipboard
        await navigator.clipboard.writeText(data.referral_link)
        toast.success('Referral link copied to clipboard!')
      } else {
        throw new Error(data.error || 'Failed to generate referral link')
      }
    } catch (error) {
      console.error('Error generating referral:', error)
      toast.error('Failed to generate referral link')
    }
  }

  const handleEmergencyReschedule = async () => {
    try {
      // Get today's bookings
      const today = new Date().toISOString().split('T')[0]
      const response = await fetch(`/api/customer/bookings?date=${today}&status=confirmed`)
      const data = await response.json()

      if (data.success && data.data.length > 0) {
        router.push('/customer/emergency-reschedule')
      } else {
        toast.info('No bookings today that need rescheduling')
      }
    } catch (error) {
      console.error('Error checking today\'s bookings:', error)
      toast.error('Failed to check today\'s bookings')
    }
  }

  const quickActions = [
    {
      id: 'book_appointment',
      icon: '📅',
      label: 'Book Appointment',
      description: 'Schedule a new service',
      color: '#4ECDC4',
      priority: 1
    },
    {
      id: 'book_multi_service',
      icon: '✨',
      label: 'Multi-Service',
      description: 'Book multiple services',
      color: '#667eea',
      priority: 2
    },
    {
      id: 'reschedule_next',
      icon: '🔄',
      label: 'Reschedule',
      description: 'Change next appointment',
      color: '#f39c12',
      priority: 3
    },
    {
      id: 'view_loyalty',
      icon: '⭐',
      label: 'Rewards',
      description: 'Check loyalty points',
      color: '#e74c3c',
      priority: 4
    },
    {
      id: 'refer_friend',
      icon: '👥',
      label: 'Refer Friend',
      description: 'Share & earn rewards',
      color: '#9b59b6',
      priority: 5
    },
    {
      id: 'contact_support',
      icon: '💬',
      label: 'Support',
      description: 'Get help',
      color: '#34495e',
      priority: 6
    }
  ]

  // Filter actions based on customer state
  const availableActions = quickActions.filter(action => {
    switch (action.id) {
      case 'view_loyalty':
        return customer?.loyalty_info // Only show if customer has loyalty program
      case 'reschedule_next':
        return true // Always available, will check for bookings when clicked
      default:
        return true
    }
  })

  // Sort by priority and limit for mobile
  const displayActions = availableActions
    .sort((a, b) => a.priority - b.priority)
    .slice(0, isMobile ? 4 : 6)

  return (
    <div className={styles.quickActions}>
      <div className={styles.actionsGrid}>
        {displayActions.map(action => (
          <button
            key={action.id}
            className={styles.actionButton}
            onClick={() => handleQuickAction(action.id)}
            disabled={loading[action.id]}
            style={{ '--action-color': action.color }}
          >
            <div className={styles.actionIcon}>
              {loading[action.id] ? (
                <div className={styles.spinner}></div>
              ) : (
                action.icon
              )}
            </div>
            
            <div className={styles.actionContent}>
              <div className={styles.actionLabel}>
                {action.label}
              </div>
              <div className={styles.actionDescription}>
                {action.description}
              </div>
            </div>
          </button>
        ))}
      </div>

      {/* Emergency Actions */}
      <div className={styles.emergencyActions}>
        <button
          className={styles.emergencyButton}
          onClick={() => handleQuickAction('emergency_reschedule')}
          disabled={loading.emergency_reschedule}
        >
          <span className={styles.emergencyIcon}>🚨</span>
          <span className={styles.emergencyLabel}>
            {loading.emergency_reschedule ? 'Checking...' : 'Emergency Reschedule'}
          </span>
        </button>
      </div>

      {/* Additional Quick Links */}
      {!isMobile && (
        <div className={styles.quickLinks}>
          <Link href="/customer/subscriptions" className={styles.quickLink}>
            <span className={styles.quickLinkIcon}>🔄</span>
            <span className={styles.quickLinkLabel}>Subscriptions</span>
          </Link>
          
          <Link href="/customer/history" className={styles.quickLink}>
            <span className={styles.quickLinkIcon}>📋</span>
            <span className={styles.quickLinkLabel}>History</span>
          </Link>
          
          <Link href="/customer/profile" className={styles.quickLink}>
            <span className={styles.quickLinkIcon}>👤</span>
            <span className={styles.quickLinkLabel}>Profile</span>
          </Link>
        </div>
      )}
    </div>
  )
}
