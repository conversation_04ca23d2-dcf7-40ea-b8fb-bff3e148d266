/**
 * Social Media Portfolio Sync API Endpoint for Ocean Soul Sparkles
 * Handles synchronization of portfolio items to social media platforms
 * 
 * Phase 7.3: Social Media Integration Layer
 */

import { authenticateAdminRequest } from '@/lib/admin-auth'
import { syncRateLimit } from '@/lib/integrations/rate-limiter'
import { Request<PERSON><PERSON><PERSON><PERSON>, AuditLogger } from '@/lib/integrations/security-utils'
import SocialMediaManager from '@/lib/integrations/social/social-manager'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

/**
 * Portfolio Sync Handler
 * POST /api/integrations/social/sync-portfolio - Sync portfolio items to social media
 */
export default async function handler(req, res) {
  // Apply rate limiting
  await new Promise((resolve, reject) => {
    syncRateLimit(req, res, (error) => {
      if (error) reject(error)
      else resolve()
    })
  })

  const startTime = Date.now()

  try {
    // Validate request method
    RequestValidator.validateEndpointAccess(req, ['POST'])

    // Authenticate user
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.success) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      })
    }

    const { user } = authResult
    const userId = user.id

    // Get request parameters
    const { 
      limit = 5, 
      providers = null,
      includeRecent = true,
      daysBack = 30 
    } = req.body

    // Initialize social media manager
    const socialManager = new SocialMediaManager(userId)

    // Get portfolio items to sync
    const portfolioItems = await getPortfolioItems(userId, {
      limit: parseInt(limit),
      includeRecent,
      daysBack: parseInt(daysBack)
    })

    if (portfolioItems.length === 0) {
      return res.status(200).json({
        success: true,
        message: 'No portfolio items found to sync',
        results: []
      })
    }

    // Get sync settings
    const syncSettings = await getSyncSettings(userId, providers)

    // Sync portfolio to social media
    const syncResults = await socialManager.syncPortfolioToSocial(portfolioItems, syncSettings)

    // Update portfolio items with sync status
    await updatePortfolioSyncStatus(portfolioItems, syncResults)

    const successCount = syncResults.filter(r => r.success).length
    const totalAttempts = syncResults.length

    await AuditLogger.logIntegrationActivity(
      userId,
      'social_media',
      'portfolio_sync_completed',
      successCount === totalAttempts ? 'success' : 'warning',
      {
        portfolioItemCount: portfolioItems.length,
        syncAttempts: totalAttempts,
        successCount,
        failedCount: totalAttempts - successCount
      }
    )

    await AuditLogger.logApiAccess(
      userId,
      req.url,
      req.method,
      200,
      Date.now() - startTime,
      { 
        action: 'sync_portfolio',
        portfolioItems: portfolioItems.length,
        syncResults: totalAttempts,
        successCount
      }
    )

    return res.status(200).json({
      success: true,
      message: `Portfolio sync completed. ${successCount} items posted successfully.`,
      results: syncResults,
      summary: {
        portfolioItems: portfolioItems.length,
        totalAttempts,
        successful: successCount,
        failed: totalAttempts - successCount
      }
    })

  } catch (error) {
    console.error('Portfolio sync error:', error)

    await AuditLogger.logSecurityEvent(
      req.user?.id || null,
      'portfolio_sync_error',
      {
        error: error.message,
        stack: error.stack,
        endpoint: req.url,
        method: req.method,
        userAgent: req.headers['user-agent'],
        ipAddress: req.headers['x-forwarded-for'] || req.connection?.remoteAddress
      },
      'error'
    )

    await AuditLogger.logApiAccess(
      req.user?.id || null,
      req.url,
      req.method,
      500,
      Date.now() - startTime,
      { error: error.message }
    )

    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to sync portfolio to social media'
    })
  }
}

/**
 * Get portfolio items to sync
 */
async function getPortfolioItems(userId, options) {
  try {
    let query = supabase
      .from('portfolio_items')
      .select('*')
      .eq('user_id', userId)
      .eq('is_public', true)
      .order('created_at', { ascending: false })

    if (options.includeRecent && options.daysBack) {
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - options.daysBack)
      query = query.gte('created_at', cutoffDate.toISOString())
    }

    if (options.limit) {
      query = query.limit(options.limit)
    }

    const { data: portfolioItems, error } = await query

    if (error) {
      throw new Error(`Failed to fetch portfolio items: ${error.message}`)
    }

    return portfolioItems || []

  } catch (error) {
    console.error('Error getting portfolio items:', error)
    return []
  }
}

/**
 * Get sync settings for user
 */
async function getSyncSettings(userId, requestedProviders = null) {
  try {
    const { data: settings, error } = await supabase
      .from('integration_settings')
      .select('*')
      .eq('user_id', userId)
      .in('provider', ['instagram_business', 'facebook_business'])

    if (error) {
      console.error('Failed to get sync settings:', error)
      return {}
    }

    const syncSettings = {}
    
    for (const setting of settings || []) {
      // Only include requested providers if specified
      if (requestedProviders && !requestedProviders.includes(setting.provider)) {
        continue
      }

      syncSettings[setting.provider] = {
        autoSync: setting.settings?.autoSync || false,
        hashtags: setting.settings?.hashtags || ['#OceanSoulSparkles', '#BeautyArt', '#Braiding'],
        locationId: setting.settings?.locationId || null,
        ...setting.settings
      }
    }

    return syncSettings

  } catch (error) {
    console.error('Error getting sync settings:', error)
    return {}
  }
}

/**
 * Update portfolio items with sync status
 */
async function updatePortfolioSyncStatus(portfolioItems, syncResults) {
  try {
    for (const item of portfolioItems) {
      const itemSyncResults = syncResults.filter(r => r.portfolioItemId === item.id)
      
      if (itemSyncResults.length > 0) {
        const syncData = {
          lastSyncAt: new Date().toISOString(),
          syncResults: itemSyncResults,
          syncedPlatforms: itemSyncResults.filter(r => r.success).map(r => r.provider)
        }

        await supabase
          .from('portfolio_items')
          .update({
            social_sync_data: syncData,
            updated_at: new Date().toISOString()
          })
          .eq('id', item.id)
      }
    }

  } catch (error) {
    console.error('Error updating portfolio sync status:', error)
  }
}

/**
 * API Route Configuration
 */
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
}
