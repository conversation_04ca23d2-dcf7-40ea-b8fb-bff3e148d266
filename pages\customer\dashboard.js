/**
 * Customer Dashboard - Phase 8: Advanced Customer Experience
 * Ocean Soul Sparkles - Enhanced Customer Portal
 */

import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'
import { useMobileOptimization } from '@/lib/hooks/useMobileOptimization'
import { useCustomer } from '@/contexts/CustomerContext'
import { toast } from 'react-toastify'
import styles from '@/styles/customer/Dashboard.module.css'

// Import customer portal components
import CustomerLayout from '@/components/customer/CustomerLayout'
import DashboardOverview from '@/components/customer/DashboardOverview'
import UpcomingBookings from '@/components/customer/UpcomingBookings'
import ServiceHistory from '@/components/customer/ServiceHistory'
import LoyaltyDashboard from '@/components/customer/LoyaltyDashboard'
import PersonalizedRecommendations from '@/components/customer/PersonalizedRecommendations'
import QuickActions from '@/components/customer/QuickActions'
import CommunicationCenter from '@/components/customer/CommunicationCenter'

export default function CustomerDashboard() {
  const router = useRouter()
  const { user, loading: authLoading } = useAuth()
  const { customer, loading: customerLoading } = useCustomer()
  const { isMobile, isTablet, viewport } = useMobileOptimization()
  
  const [activeTab, setActiveTab] = useState('overview')
  const [dashboardData, setDashboardData] = useState({
    upcomingBookings: [],
    recentHistory: [],
    loyaltyInfo: null,
    recommendations: [],
    unreadMessages: 0
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login?redirect=/customer/dashboard')
    }
  }, [user, authLoading, router])

  // Load dashboard data
  useEffect(() => {
    if (user && customer) {
      loadDashboardData()
    }
  }, [user, customer])

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Fetch dashboard data from multiple endpoints
      const [bookingsRes, historyRes, loyaltyRes, recommendationsRes, messagesRes] = await Promise.all([
        fetch('/api/customer/bookings?status=upcoming&limit=5'),
        fetch('/api/customer/service-history?limit=5'),
        fetch('/api/customer/loyalty'),
        fetch('/api/customer/recommendations?limit=6'),
        fetch('/api/customer/messages/unread-count')
      ])

      const [bookings, history, loyalty, recommendations, messages] = await Promise.all([
        bookingsRes.json(),
        historyRes.json(),
        loyaltyRes.json(),
        recommendationsRes.json(),
        messagesRes.json()
      ])

      setDashboardData({
        upcomingBookings: bookings.data || [],
        recentHistory: history.data || [],
        loyaltyInfo: loyalty.data || null,
        recommendations: recommendations.data || [],
        unreadMessages: messages.count || 0
      })

    } catch (error) {
      console.error('Error loading dashboard data:', error)
      setError('Failed to load dashboard data')
      toast.error('Failed to load dashboard data')
    } finally {
      setLoading(false)
    }
  }

  const handleTabChange = (tab) => {
    setActiveTab(tab)
    
    // Update URL without page reload
    const url = new URL(window.location)
    url.searchParams.set('tab', tab)
    window.history.pushState({}, '', url)
  }

  // Handle URL tab parameter
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const tabParam = urlParams.get('tab')
    if (tabParam && ['overview', 'bookings', 'history', 'loyalty', 'messages'].includes(tabParam)) {
      setActiveTab(tabParam)
    }
  }, [])

  if (authLoading || customerLoading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.spinner}></div>
        <p>Loading your dashboard...</p>
      </div>
    )
  }

  if (!user || !customer) {
    return null // Will redirect
  }

  return (
    <>
      <Head>
        <title>My Dashboard - Ocean Soul Sparkles</title>
        <meta name="description" content="Your personal Ocean Soul Sparkles dashboard" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <CustomerLayout>
        <div className={styles.dashboard}>
          {/* Welcome Header */}
          <div className={styles.welcomeHeader}>
            <div className={styles.welcomeContent}>
              <h1 className={styles.welcomeTitle}>
                Welcome back, {customer.name}!
              </h1>
              <p className={styles.welcomeSubtitle}>
                {dashboardData.upcomingBookings.length > 0 
                  ? `You have ${dashboardData.upcomingBookings.length} upcoming appointment${dashboardData.upcomingBookings.length > 1 ? 's' : ''}`
                  : 'Ready to book your next appointment?'
                }
              </p>
            </div>
            
            {/* Quick Actions */}
            <div className={styles.quickActionsContainer}>
              <QuickActions 
                customer={customer}
                onActionComplete={loadDashboardData}
              />
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className={styles.tabNavigation}>
            <div className={styles.tabList}>
              <button
                className={`${styles.tab} ${activeTab === 'overview' ? styles.active : ''}`}
                onClick={() => handleTabChange('overview')}
              >
                <span className={styles.tabIcon}>📊</span>
                <span className={styles.tabLabel}>Overview</span>
              </button>
              
              <button
                className={`${styles.tab} ${activeTab === 'bookings' ? styles.active : ''}`}
                onClick={() => handleTabChange('bookings')}
              >
                <span className={styles.tabIcon}>📅</span>
                <span className={styles.tabLabel}>Bookings</span>
                {dashboardData.upcomingBookings.length > 0 && (
                  <span className={styles.badge}>{dashboardData.upcomingBookings.length}</span>
                )}
              </button>
              
              <button
                className={`${styles.tab} ${activeTab === 'history' ? styles.active : ''}`}
                onClick={() => handleTabChange('history')}
              >
                <span className={styles.tabIcon}>📋</span>
                <span className={styles.tabLabel}>History</span>
              </button>
              
              <button
                className={`${styles.tab} ${activeTab === 'loyalty' ? styles.active : ''}`}
                onClick={() => handleTabChange('loyalty')}
              >
                <span className={styles.tabIcon}>⭐</span>
                <span className={styles.tabLabel}>Rewards</span>
                {dashboardData.loyaltyInfo?.points_balance > 0 && (
                  <span className={styles.badge}>{dashboardData.loyaltyInfo.points_balance}</span>
                )}
              </button>
              
              <button
                className={`${styles.tab} ${activeTab === 'messages' ? styles.active : ''}`}
                onClick={() => handleTabChange('messages')}
              >
                <span className={styles.tabIcon}>💬</span>
                <span className={styles.tabLabel}>Messages</span>
                {dashboardData.unreadMessages > 0 && (
                  <span className={styles.badge}>{dashboardData.unreadMessages}</span>
                )}
              </button>
            </div>
          </div>

          {/* Tab Content */}
          <div className={styles.tabContent}>
            {error && (
              <div className={styles.error}>
                <p>{error}</p>
                <button onClick={loadDashboardData} className={styles.retryButton}>
                  Try Again
                </button>
              </div>
            )}

            {loading ? (
              <div className={styles.loadingContent}>
                <div className={styles.spinner}></div>
                <p>Loading...</p>
              </div>
            ) : (
              <>
                {activeTab === 'overview' && (
                  <DashboardOverview
                    customer={customer}
                    dashboardData={dashboardData}
                    onRefresh={loadDashboardData}
                  />
                )}

                {activeTab === 'bookings' && (
                  <UpcomingBookings
                    bookings={dashboardData.upcomingBookings}
                    customer={customer}
                    onBookingUpdate={loadDashboardData}
                  />
                )}

                {activeTab === 'history' && (
                  <ServiceHistory
                    history={dashboardData.recentHistory}
                    customer={customer}
                    onHistoryUpdate={loadDashboardData}
                  />
                )}

                {activeTab === 'loyalty' && (
                  <LoyaltyDashboard
                    loyaltyInfo={dashboardData.loyaltyInfo}
                    customer={customer}
                    onLoyaltyUpdate={loadDashboardData}
                  />
                )}

                {activeTab === 'messages' && (
                  <CommunicationCenter
                    customer={customer}
                    unreadCount={dashboardData.unreadMessages}
                    onMessageUpdate={loadDashboardData}
                  />
                )}
              </>
            )}
          </div>

          {/* Personalized Recommendations */}
          {activeTab === 'overview' && dashboardData.recommendations.length > 0 && (
            <div className={styles.recommendationsSection}>
              <PersonalizedRecommendations
                recommendations={dashboardData.recommendations}
                customer={customer}
                onRecommendationAction={loadDashboardData}
              />
            </div>
          )}
        </div>
      </CustomerLayout>
    </>
  )
}
