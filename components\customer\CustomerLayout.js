/**
 * Customer Layout Component - Phase 8: Advanced Customer Experience
 * Ocean Soul Sparkles - Customer Portal Layout
 */

import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Link from 'next/link'
import Image from 'next/image'
import { useAuth } from '@/contexts/AuthContext'
import { useCustomer } from '@/contexts/CustomerContext'
import { useMobileOptimization } from '@/lib/hooks/useMobileOptimization'
import { toast } from 'react-toastify'
import styles from '@/styles/customer/CustomerLayout.module.css'

export default function CustomerLayout({ children, title = 'Ocean Soul Sparkles' }) {
  const router = useRouter()
  const { user, signOut } = useAuth()
  const { customer } = useCustomer()
  const { isMobile, isTablet, viewport } = useMobileOptimization()
  
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [notificationCount, setNotificationCount] = useState(0)

  // Load notification count
  useEffect(() => {
    if (user && customer) {
      loadNotificationCount()
    }
  }, [user, customer])

  const loadNotificationCount = async () => {
    try {
      const response = await fetch('/api/customer/notifications/count')
      const data = await response.json()
      setNotificationCount(data.count || 0)
    } catch (error) {
      console.error('Error loading notification count:', error)
    }
  }

  const handleSignOut = async () => {
    try {
      await signOut()
      router.push('/')
      toast.success('Signed out successfully')
    } catch (error) {
      console.error('Error signing out:', error)
      toast.error('Error signing out')
    }
  }

  const navigationItems = [
    {
      href: '/customer/dashboard',
      icon: '🏠',
      label: 'Dashboard',
      active: router.pathname === '/customer/dashboard'
    },
    {
      href: '/customer/bookings',
      icon: '📅',
      label: 'My Bookings',
      active: router.pathname.startsWith('/customer/bookings')
    },
    {
      href: '/customer/services',
      icon: '✨',
      label: 'Book Services',
      active: router.pathname.startsWith('/customer/services')
    },
    {
      href: '/customer/history',
      icon: '📋',
      label: 'Service History',
      active: router.pathname.startsWith('/customer/history')
    },
    {
      href: '/customer/loyalty',
      icon: '⭐',
      label: 'Rewards',
      active: router.pathname.startsWith('/customer/loyalty')
    },
    {
      href: '/customer/subscriptions',
      icon: '🔄',
      label: 'Subscriptions',
      active: router.pathname.startsWith('/customer/subscriptions')
    },
    {
      href: '/customer/messages',
      icon: '💬',
      label: 'Messages',
      active: router.pathname.startsWith('/customer/messages'),
      badge: notificationCount > 0 ? notificationCount : null
    },
    {
      href: '/customer/profile',
      icon: '👤',
      label: 'Profile',
      active: router.pathname.startsWith('/customer/profile')
    }
  ]

  return (
    <div className={styles.layout}>
      {/* Mobile Header */}
      {isMobile && (
        <header className={styles.mobileHeader}>
          <button
            className={styles.menuButton}
            onClick={() => setSidebarOpen(!sidebarOpen)}
            aria-label="Toggle menu"
          >
            <span className={styles.hamburger}></span>
          </button>
          
          <Link href="/customer/dashboard" className={styles.logoLink}>
            <Image
              src="/images/logo.png"
              alt="Ocean Soul Sparkles"
              width={120}
              height={40}
              className={styles.logo}
            />
          </Link>
          
          <div className={styles.headerActions}>
            <Link href="/customer/messages" className={styles.notificationButton}>
              <span className={styles.messageIcon}>💬</span>
              {notificationCount > 0 && (
                <span className={styles.notificationBadge}>{notificationCount}</span>
              )}
            </Link>
          </div>
        </header>
      )}

      {/* Sidebar */}
      <aside className={`${styles.sidebar} ${sidebarOpen ? styles.sidebarOpen : ''}`}>
        {/* Desktop Logo */}
        {!isMobile && (
          <div className={styles.sidebarHeader}>
            <Link href="/customer/dashboard" className={styles.logoLink}>
              <Image
                src="/images/logo.png"
                alt="Ocean Soul Sparkles"
                width={150}
                height={50}
                className={styles.logo}
              />
            </Link>
          </div>
        )}

        {/* Customer Info */}
        <div className={styles.customerInfo}>
          <div className={styles.customerAvatar}>
            {customer?.profile_image_url ? (
              <Image
                src={customer.profile_image_url}
                alt={customer.name}
                width={48}
                height={48}
                className={styles.avatarImage}
              />
            ) : (
              <div className={styles.avatarPlaceholder}>
                {customer?.name?.charAt(0)?.toUpperCase() || 'U'}
              </div>
            )}
          </div>
          <div className={styles.customerDetails}>
            <h3 className={styles.customerName}>{customer?.name || 'Customer'}</h3>
            <p className={styles.customerEmail}>{customer?.email}</p>
          </div>
        </div>

        {/* Navigation */}
        <nav className={styles.navigation}>
          <ul className={styles.navList}>
            {navigationItems.map((item) => (
              <li key={item.href} className={styles.navItem}>
                <Link
                  href={item.href}
                  className={`${styles.navLink} ${item.active ? styles.active : ''}`}
                  onClick={() => isMobile && setSidebarOpen(false)}
                >
                  <span className={styles.navIcon}>{item.icon}</span>
                  <span className={styles.navLabel}>{item.label}</span>
                  {item.badge && (
                    <span className={styles.navBadge}>{item.badge}</span>
                  )}
                </Link>
              </li>
            ))}
          </ul>
        </nav>

        {/* Quick Actions */}
        <div className={styles.quickActions}>
          <Link href="/book-online" className={styles.quickActionButton}>
            <span className={styles.quickActionIcon}>➕</span>
            <span className={styles.quickActionLabel}>Book Now</span>
          </Link>
          
          <Link href="/customer/support" className={styles.quickActionButton}>
            <span className={styles.quickActionIcon}>❓</span>
            <span className={styles.quickActionLabel}>Support</span>
          </Link>
        </div>

        {/* Sign Out */}
        <div className={styles.sidebarFooter}>
          <button
            onClick={handleSignOut}
            className={styles.signOutButton}
          >
            <span className={styles.signOutIcon}>🚪</span>
            <span className={styles.signOutLabel}>Sign Out</span>
          </button>
        </div>
      </aside>

      {/* Mobile Sidebar Overlay */}
      {isMobile && sidebarOpen && (
        <div
          className={styles.sidebarOverlay}
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Main Content */}
      <main className={styles.main}>
        <div className={styles.content}>
          {children}
        </div>
      </main>

      {/* Mobile Bottom Navigation */}
      {isMobile && (
        <nav className={styles.bottomNavigation}>
          <Link
            href="/customer/dashboard"
            className={`${styles.bottomNavItem} ${router.pathname === '/customer/dashboard' ? styles.active : ''}`}
          >
            <span className={styles.bottomNavIcon}>🏠</span>
            <span className={styles.bottomNavLabel}>Home</span>
          </Link>
          
          <Link
            href="/customer/bookings"
            className={`${styles.bottomNavItem} ${router.pathname.startsWith('/customer/bookings') ? styles.active : ''}`}
          >
            <span className={styles.bottomNavIcon}>📅</span>
            <span className={styles.bottomNavLabel}>Bookings</span>
          </Link>
          
          <Link
            href="/book-online"
            className={styles.bottomNavItem}
          >
            <span className={styles.bottomNavIcon}>➕</span>
            <span className={styles.bottomNavLabel}>Book</span>
          </Link>
          
          <Link
            href="/customer/messages"
            className={`${styles.bottomNavItem} ${router.pathname.startsWith('/customer/messages') ? styles.active : ''}`}
          >
            <span className={styles.bottomNavIcon}>💬</span>
            <span className={styles.bottomNavLabel}>Messages</span>
            {notificationCount > 0 && (
              <span className={styles.bottomNavBadge}>{notificationCount}</span>
            )}
          </Link>
          
          <Link
            href="/customer/profile"
            className={`${styles.bottomNavItem} ${router.pathname.startsWith('/customer/profile') ? styles.active : ''}`}
          >
            <span className={styles.bottomNavIcon}>👤</span>
            <span className={styles.bottomNavLabel}>Profile</span>
          </Link>
        </nav>
      )}
    </div>
  )
}
