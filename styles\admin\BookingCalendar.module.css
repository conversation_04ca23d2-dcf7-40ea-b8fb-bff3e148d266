.calendarContainer {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 30px;
}

.loading {
  padding: 40px;
  text-align: center;
  color: #6c757d;
  font-size: 1rem;
}

.error {
  padding: 20px;
  background-color: #f8d7da;
  color: #721c24;
  border-radius: 4px;
  margin-bottom: 20px;
  position: relative;
}

.closeButton {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 1.5rem;
  line-height: 1;
  cursor: pointer;
  color: #721c24;
}

.closeButton:hover {
  color: #491217;
}

.calendarActions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 1rem;
}

.calendarToggles {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.toggleLabel {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #495057;
  cursor: pointer;
}

.toggleLabel input[type="checkbox"] {
  margin: 0;
  accent-color: #6a0dad;
}

.conflictIndicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #fff3cd;
  color: #856404;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  border: 1px solid #ffeaa7;
  font-size: 0.85rem;
  font-weight: 500;
}

.conflictIcon {
  font-size: 1rem;
}

.refreshButton {
  display: flex;
  align-items: center;
  gap: 5px;
  background-color: #6a0dad;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.refreshButton:hover {
  background-color: #5a0b9d;
}

.refreshButton svg {
  width: 16px;
  height: 16px;
}

/* Override react-big-calendar styles */
.calendar :global(.rbc-toolbar) {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e9ecef;
}

.calendar :global(.rbc-toolbar button) {
  color: #495057;
  background-color: white;
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 6px 12px;
  font-weight: 500;
}

.calendar :global(.rbc-toolbar button:hover) {
  background-color: #f8f9fa;
}

.calendar :global(.rbc-toolbar button.rbc-active) {
  background-color: #6a0dad;
  color: white;
  border-color: #6a0dad;
}

.calendar :global(.rbc-toolbar button.rbc-active:hover) {
  background-color: #5a0b9d;
}

.calendar :global(.rbc-month-view),
.calendar :global(.rbc-time-view),
.calendar :global(.rbc-agenda-view) {
  border: 1px solid #e9ecef;
  border-radius: 4px;
}

.calendar :global(.rbc-header) {
  padding: 10px;
  font-weight: 600;
  background-color: #f8f9fa;
}

.calendar :global(.rbc-date-cell) {
  padding: 5px 8px;
  text-align: right;
}

.calendar :global(.rbc-off-range-bg) {
  background-color: #f8f9fa;
}

.calendar :global(.rbc-today) {
  background-color: #e6f7ff;
}

.calendar :global(.rbc-event) {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85rem;
}

.calendar :global(.rbc-event-label) {
  font-size: 0.75rem;
  margin-bottom: 2px;
}

.calendar :global(.rbc-show-more) {
  color: #6a0dad;
  font-weight: 500;
}

.calendar :global(.rbc-agenda-empty) {
  padding: 30px;
  text-align: center;
  color: #6c757d;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .calendarContainer {
    padding: 10px;
  }

  .calendar :global(.rbc-toolbar) {
    flex-direction: column;
    align-items: flex-start;
  }

  .calendar :global(.rbc-toolbar-label) {
    margin: 10px 0;
  }

  .calendar :global(.rbc-btn-group) {
    margin-bottom: 10px;
  }
}
