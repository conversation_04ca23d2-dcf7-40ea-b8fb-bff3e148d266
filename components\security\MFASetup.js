/**
 * Multi-Factor Authentication Setup Component for Ocean Soul Sparkles
 * Provides UI for setting up and managing MFA
 * 
 * Phase 9.1: Enhanced Authentication System
 */

import { useState, useEffect } from 'react'
import { toast } from 'react-hot-toast'
import { QrCodeIcon, KeyIcon, ShieldCheckIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline'

export default function MFASetup({ user, onMFAStatusChange }) {
  const [mfaStatus, setMFAStatus] = useState({
    enabled: false,
    enforced: false,
    backupCodesRemaining: 0
  })
  const [setupStep, setSetupStep] = useState('initial') // initial, qr, verify, backup, complete
  const [qrCode, setQrCode] = useState('')
  const [manualKey, setManualKey] = useState('')
  const [verificationCode, setVerificationCode] = useState('')
  const [backupCodes, setBackupCodes] = useState([])
  const [loading, setLoading] = useState(false)
  const [showBackupCodes, setShowBackupCodes] = useState(false)

  useEffect(() => {
    fetchMFAStatus()
  }, [user])

  const fetchMFAStatus = async () => {
    try {
      const response = await fetch('/api/security/mfa/status', {
        headers: {
          'Authorization': `Bearer ${user.access_token}`
        }
      })
      
      if (response.ok) {
        const status = await response.json()
        setMFAStatus(status)
        onMFAStatusChange?.(status)
      }
    } catch (error) {
      console.error('Error fetching MFA status:', error)
    }
  }

  const startMFASetup = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/security/mfa/setup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${user.access_token}`
        },
        body: JSON.stringify({
          userEmail: user.email,
          userName: user.user_metadata?.name || user.email
        })
      })

      if (response.ok) {
        const data = await response.json()
        setQrCode(data.qrCode)
        setManualKey(data.manualEntryKey)
        setSetupStep('qr')
      } else {
        const error = await response.json()
        toast.error(error.message || 'Failed to start MFA setup')
      }
    } catch (error) {
      console.error('Error starting MFA setup:', error)
      toast.error('Failed to start MFA setup')
    } finally {
      setLoading(false)
    }
  }

  const verifyAndEnableMFA = async () => {
    if (!verificationCode || verificationCode.length !== 6) {
      toast.error('Please enter a valid 6-digit code')
      return
    }

    setLoading(true)
    try {
      const response = await fetch('/api/security/mfa/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${user.access_token}`
        },
        body: JSON.stringify({
          token: verificationCode
        })
      })

      if (response.ok) {
        const data = await response.json()
        setBackupCodes(data.backupCodes)
        setSetupStep('backup')
        setMFAStatus(prev => ({ ...prev, enabled: true }))
        toast.success('MFA enabled successfully!')
      } else {
        const error = await response.json()
        toast.error(error.message || 'Invalid verification code')
      }
    } catch (error) {
      console.error('Error verifying MFA:', error)
      toast.error('Failed to verify MFA')
    } finally {
      setLoading(false)
    }
  }

  const regenerateBackupCodes = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/security/mfa/backup-codes', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${user.access_token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setBackupCodes(data.backupCodes)
        setShowBackupCodes(true)
        toast.success('New backup codes generated')
        fetchMFAStatus()
      } else {
        const error = await response.json()
        toast.error(error.message || 'Failed to generate backup codes')
      }
    } catch (error) {
      console.error('Error regenerating backup codes:', error)
      toast.error('Failed to generate backup codes')
    } finally {
      setLoading(false)
    }
  }

  const disableMFA = async () => {
    if (!confirm('Are you sure you want to disable multi-factor authentication? This will make your account less secure.')) {
      return
    }

    setLoading(true)
    try {
      const response = await fetch('/api/security/mfa/disable', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${user.access_token}`
        }
      })

      if (response.ok) {
        setMFAStatus({ enabled: false, enforced: false, backupCodesRemaining: 0 })
        setSetupStep('initial')
        toast.success('MFA disabled successfully')
        onMFAStatusChange?.({ enabled: false, enforced: false, backupCodesRemaining: 0 })
      } else {
        const error = await response.json()
        toast.error(error.message || 'Failed to disable MFA')
      }
    } catch (error) {
      console.error('Error disabling MFA:', error)
      toast.error('Failed to disable MFA')
    } finally {
      setLoading(false)
    }
  }

  const downloadBackupCodes = () => {
    const codesText = backupCodes.join('\n')
    const blob = new Blob([codesText], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'ocean-soul-sparkles-backup-codes.txt'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const copyBackupCodes = () => {
    navigator.clipboard.writeText(backupCodes.join('\n'))
    toast.success('Backup codes copied to clipboard')
  }

  if (mfaStatus.enabled && setupStep === 'initial') {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <ShieldCheckIcon className="h-6 w-6 text-green-500 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">Multi-Factor Authentication</h3>
          </div>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            Enabled
          </span>
        </div>
        
        <p className="text-sm text-gray-600 mb-4">
          Your account is protected with multi-factor authentication.
        </p>
        
        <div className="space-y-3">
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <span className="text-sm text-gray-700">Backup codes remaining</span>
            <span className="text-sm font-medium text-gray-900">{mfaStatus.backupCodesRemaining}</span>
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={regenerateBackupCodes}
              disabled={loading}
              className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Generating...' : 'Generate New Backup Codes'}
            </button>
            
            <button
              onClick={disableMFA}
              disabled={loading || mfaStatus.enforced}
              className="flex-1 bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 disabled:opacity-50"
            >
              {loading ? 'Disabling...' : 'Disable MFA'}
            </button>
          </div>
          
          {mfaStatus.enforced && (
            <div className="flex items-center p-3 bg-yellow-50 rounded-lg">
              <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400 mr-2" />
              <span className="text-sm text-yellow-800">
                MFA is enforced by your administrator and cannot be disabled.
              </span>
            </div>
          )}
        </div>
        
        {showBackupCodes && backupCodes.length > 0 && (
          <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h4 className="text-sm font-medium text-yellow-800 mb-2">New Backup Codes</h4>
            <div className="grid grid-cols-2 gap-2 mb-3">
              {backupCodes.map((code, index) => (
                <code key={index} className="text-sm bg-white p-2 rounded border">
                  {code}
                </code>
              ))}
            </div>
            <div className="flex space-x-2">
              <button
                onClick={copyBackupCodes}
                className="text-sm bg-yellow-600 text-white px-3 py-1 rounded hover:bg-yellow-700"
              >
                Copy Codes
              </button>
              <button
                onClick={downloadBackupCodes}
                className="text-sm bg-yellow-600 text-white px-3 py-1 rounded hover:bg-yellow-700"
              >
                Download Codes
              </button>
              <button
                onClick={() => setShowBackupCodes(false)}
                className="text-sm text-yellow-800 hover:text-yellow-900"
              >
                Hide Codes
              </button>
            </div>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center mb-4">
        <ShieldCheckIcon className="h-6 w-6 text-gray-400 mr-2" />
        <h3 className="text-lg font-medium text-gray-900">Multi-Factor Authentication</h3>
      </div>

      {setupStep === 'initial' && (
        <div>
          <p className="text-sm text-gray-600 mb-4">
            Add an extra layer of security to your account by enabling multi-factor authentication.
          </p>
          <button
            onClick={startMFASetup}
            disabled={loading}
            className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Setting up...' : 'Enable MFA'}
          </button>
        </div>
      )}

      {setupStep === 'qr' && (
        <div>
          <h4 className="text-md font-medium text-gray-900 mb-3">Step 1: Scan QR Code</h4>
          <p className="text-sm text-gray-600 mb-4">
            Scan this QR code with your authenticator app (Google Authenticator, Authy, etc.)
          </p>
          
          <div className="flex flex-col items-center mb-4">
            <div className="bg-white p-4 rounded-lg border-2 border-gray-200 mb-4">
              <img src={qrCode} alt="MFA QR Code" className="w-48 h-48" />
            </div>
            
            <div className="text-center">
              <p className="text-xs text-gray-500 mb-2">Can't scan? Enter this code manually:</p>
              <code className="text-sm bg-gray-100 px-2 py-1 rounded">{manualKey}</code>
            </div>
          </div>
          
          <button
            onClick={() => setSetupStep('verify')}
            className="w-full bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700"
          >
            I've Added the Account
          </button>
        </div>
      )}

      {setupStep === 'verify' && (
        <div>
          <h4 className="text-md font-medium text-gray-900 mb-3">Step 2: Verify Setup</h4>
          <p className="text-sm text-gray-600 mb-4">
            Enter the 6-digit code from your authenticator app to verify the setup.
          </p>
          
          <div className="mb-4">
            <input
              type="text"
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
              placeholder="000000"
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-center text-lg font-mono"
              maxLength={6}
            />
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={() => setSetupStep('qr')}
              className="flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-400"
            >
              Back
            </button>
            <button
              onClick={verifyAndEnableMFA}
              disabled={loading || verificationCode.length !== 6}
              className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Verifying...' : 'Verify & Enable'}
            </button>
          </div>
        </div>
      )}

      {setupStep === 'backup' && (
        <div>
          <h4 className="text-md font-medium text-gray-900 mb-3">Step 3: Save Backup Codes</h4>
          <p className="text-sm text-gray-600 mb-4">
            Save these backup codes in a safe place. You can use them to access your account if you lose your authenticator device.
          </p>
          
          <div className="bg-gray-50 p-4 rounded-lg mb-4">
            <div className="grid grid-cols-2 gap-2 mb-3">
              {backupCodes.map((code, index) => (
                <code key={index} className="text-sm bg-white p-2 rounded border">
                  {code}
                </code>
              ))}
            </div>
            
            <div className="flex space-x-2">
              <button
                onClick={copyBackupCodes}
                className="text-sm bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700"
              >
                Copy Codes
              </button>
              <button
                onClick={downloadBackupCodes}
                className="text-sm bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700"
              >
                Download Codes
              </button>
            </div>
          </div>
          
          <button
            onClick={() => {
              setSetupStep('initial')
              fetchMFAStatus()
            }}
            className="w-full bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700"
          >
            I've Saved My Backup Codes
          </button>
        </div>
      )}
    </div>
  )
}
