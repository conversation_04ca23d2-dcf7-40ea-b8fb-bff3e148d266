/**
 * AI Insights Generator for Ocean Soul Sparkles
 * Generates automated business intelligence and actionable recommendations
 */

import { supabase } from '@/lib/supabase'

export class AIInsightsGenerator {
  constructor() {
    this.insightsCache = new Map()
    this.cacheExpiry = 30 * 60 * 1000 // 30 minutes
  }

  /**
   * Generate daily insights for the business
   * @param {Date} date - Date to generate insights for
   * @returns {Promise<Object>} Daily insights
   */
  async generateDailyInsights(date = new Date()) {
    const cacheKey = `daily-${date.toISOString().split('T')[0]}`
    const cached = this.insightsCache.get(cacheKey)
    
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      console.log('[AIInsightsGenerator] Cache hit for daily insights')
      return cached.insights
    }

    try {
      console.log(`[AIInsightsGenerator] Generating daily insights for ${date.toISOString().split('T')[0]}`)
      
      const [bookingData, revenueData, artistData] = await Promise.all([
        this.getBookingData(date),
        this.getRevenueData(date),
        this.getArtistPerformanceData(date)
      ])

      const insights = {
        date: date.toISOString().split('T')[0],
        generatedAt: new Date().toISOString(),
        summary: await this.generateExecutiveSummary(bookingData, revenueData),
        bookingInsights: await this.analyzeBookingPatterns(bookingData),
        revenueInsights: await this.analyzeRevenuePatterns(revenueData),
        artistInsights: await this.analyzeArtistPerformance(artistData),
        predictions: await this.generatePredictions(bookingData, revenueData),
        recommendations: await this.generateActionableRecommendations(bookingData, revenueData, artistData),
        alerts: await this.detectAnomalies(bookingData, revenueData),
        metadata: {
          dataPoints: bookingData.length + revenueData.length + artistData.length,
          analysisVersion: '1.0.0',
          confidenceLevel: this.calculateOverallConfidence(bookingData, revenueData)
        }
      }

      this.insightsCache.set(cacheKey, {
        insights,
        timestamp: Date.now()
      })

      console.log(`[AIInsightsGenerator] Generated insights with ${insights.recommendations.length} recommendations and ${insights.alerts.length} alerts`)
      return insights
    } catch (error) {
      console.error('[AIInsightsGenerator] Insights generation failed:', error)
      return this.getDefaultInsights(date, error.message)
    }
  }

  /**
   * Get booking data for analysis
   * @param {Date} date - Target date
   * @returns {Promise<Array>} Booking data
   */
  async getBookingData(date) {
    const startOfDay = new Date(date)
    startOfDay.setHours(0, 0, 0, 0)
    
    const endOfDay = new Date(date)
    endOfDay.setHours(23, 59, 59, 999)

    const { data: bookings, error } = await supabase
      .from('bookings')
      .select(`
        id,
        start_time,
        end_time,
        status,
        total_amount,
        customer_id,
        assigned_artist_id,

        booking_source,
        services!inner(name, category, duration, price),
        customers(name, created_at)
      `)
      .gte('start_time', startOfDay.toISOString())
      .lte('start_time', endOfDay.toISOString())

    if (error) {
      throw new Error(`Failed to fetch booking data: ${error.message}`)
    }

    return bookings || []
  }

  /**
   * Get revenue data for analysis
   * @param {Date} date - Target date
   * @returns {Promise<Array>} Revenue data
   */
  async getRevenueData(date) {
    const startOfMonth = new Date(date.getFullYear(), date.getMonth(), 1)
    const endOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0)

    const { data: revenue, error } = await supabase
      .from('bookings')
      .select(`
        total_amount,
        start_time,
        status,
        services!inner(category, name, price)
      `)
      .gte('start_time', startOfMonth.toISOString())
      .lte('start_time', endOfMonth.toISOString())
      .eq('status', 'completed')

    if (error) {
      throw new Error(`Failed to fetch revenue data: ${error.message}`)
    }

    return revenue || []
  }

  /**
   * Get artist performance data
   * @param {Date} date - Target date
   * @returns {Promise<Array>} Artist performance data
   */
  async getArtistPerformanceData(date) {
    const startOfWeek = new Date(date)
    startOfWeek.setDate(date.getDate() - date.getDay())
    startOfWeek.setHours(0, 0, 0, 0)

    const { data: performance, error } = await supabase
      .from('bookings')
      .select(`
        assigned_artist_id,
        total_amount,

        start_time,
        status,
        services!inner(duration)
      `)
      .gte('start_time', startOfWeek.toISOString())
      .not('assigned_artist_id', 'is', null)

    if (error) {
      throw new Error(`Failed to fetch artist performance data: ${error.message}`)
    }

    return performance || []
  }

  /**
   * Generate executive summary
   * @param {Array} bookingData - Booking data
   * @param {Array} revenueData - Revenue data
   * @returns {Promise<Object>} Executive summary
   */
  async generateExecutiveSummary(bookingData, revenueData) {
    const totalBookings = bookingData.length
    const completedBookings = bookingData.filter(b => b.status === 'completed').length
    const totalRevenue = revenueData.reduce((sum, r) => sum + (r.total_amount || 0), 0)
    const avgBookingValue = completedBookings > 0 ? totalRevenue / completedBookings : 0

    const previousPeriodRevenue = await this.getPreviousPeriodRevenue()
    const revenueGrowth = previousPeriodRevenue > 0 
      ? ((totalRevenue - previousPeriodRevenue) / previousPeriodRevenue * 100)
      : 0

    const keyMetric = this.identifyKeyMetric(totalBookings, totalRevenue, revenueGrowth)

    return {
      totalBookings,
      completedBookings,
      completionRate: totalBookings > 0 ? ((completedBookings / totalBookings) * 100).toFixed(1) : '0.0',
      totalRevenue: parseFloat(totalRevenue.toFixed(2)),
      avgBookingValue: parseFloat(avgBookingValue.toFixed(2)),
      revenueGrowth: parseFloat(revenueGrowth.toFixed(1)),
      keyMetric,
      narrative: this.generateNarrativeSummary(totalBookings, totalRevenue, revenueGrowth),
      performanceIndicator: this.getPerformanceIndicator(revenueGrowth, completedBookings / Math.max(totalBookings, 1))
    }
  }

  /**
   * Analyze booking patterns
   * @param {Array} bookingData - Booking data
   * @returns {Promise<Object>} Booking insights
   */
  async analyzeBookingPatterns(bookingData) {
    const hourlyDistribution = this.analyzeHourlyDistribution(bookingData)
    const servicePopularity = this.analyzeServicePopularity(bookingData)
    const customerSegments = this.analyzeCustomerSegments(bookingData)
    const bookingTrends = this.identifyBookingTrends(bookingData)

    return {
      peakHours: this.identifyPeakHours(hourlyDistribution),
      popularServices: servicePopularity.slice(0, 3),
      customerInsights: customerSegments,
      bookingTrends,
      recommendations: this.generateBookingRecommendations(hourlyDistribution, servicePopularity),
      hourlyDistribution: hourlyDistribution.filter(h => h.count > 0) // Only include hours with bookings
    }
  }

  /**
   * Generate booking recommendations based on patterns
   * @param {Array} hourlyDistribution - Hourly booking distribution
   * @param {Array} servicePopularity - Service popularity data
   * @returns {Array} Booking recommendations
   */
  generateBookingRecommendations(hourlyDistribution, servicePopularity) {
    const recommendations = []

    // Find peak hours
    const peakHours = this.identifyPeakHours(hourlyDistribution)

    if (peakHours.length > 0) {
      recommendations.push({
        type: 'scheduling',
        priority: 'medium',
        message: `Peak booking hours are ${peakHours.map(h => h.time).join(', ')}. Consider optimizing staff allocation during these times.`,
        action: 'optimize_scheduling',
        confidence: 0.8
      })
    }

    // Service popularity recommendations
    if (servicePopularity.length > 0) {
      const topService = servicePopularity[0]
      if (topService.count > 1) {
        recommendations.push({
          type: 'marketing',
          priority: 'low',
          message: `${topService.service} is your most popular service. Consider promoting similar services.`,
          action: 'promote_similar_services',
          confidence: 0.7
        })
      }
    }

    // Low booking recommendations
    if (hourlyDistribution.filter(h => h.count > 0).length < 3) {
      recommendations.push({
        type: 'marketing',
        priority: 'high',
        message: 'Low booking activity detected. Consider promotional campaigns or service adjustments.',
        action: 'increase_marketing',
        confidence: 0.9
      })
    }

    return recommendations
  }

  /**
   * Analyze hourly booking distribution
   * @param {Array} bookingData - Booking data
   * @returns {Array} Hourly distribution
   */
  analyzeHourlyDistribution(bookingData) {
    const hourlyCount = new Array(24).fill(0)
    
    bookingData.forEach(booking => {
      const hour = new Date(booking.start_time).getHours()
      hourlyCount[hour]++
    })

    return hourlyCount.map((count, hour) => ({ 
      hour, 
      count,
      percentage: bookingData.length > 0 ? ((count / bookingData.length) * 100).toFixed(1) : '0.0'
    }))
  }

  /**
   * Analyze revenue patterns
   * @param {Array} revenueData - Revenue data
   * @returns {Promise<Object>} Revenue analysis
   */
  async analyzeRevenuePatterns(revenueData) {
    const totalRevenue = revenueData.reduce((sum, r) => sum + (r.total_amount || 0), 0)
    const avgBookingValue = revenueData.length > 0 ? totalRevenue / revenueData.length : 0

    // Analyze revenue by service category
    const revenueByCategory = {}
    revenueData.forEach(booking => {
      const category = booking.services?.category || 'Other'
      revenueByCategory[category] = (revenueByCategory[category] || 0) + (booking.total_amount || 0)
    })

    const topRevenueCategory = Object.entries(revenueByCategory)
      .sort((a, b) => b[1] - a[1])[0]

    // Calculate growth compared to previous period
    const previousRevenue = await this.getPreviousPeriodRevenue()
    const growth = previousRevenue > 0 ? ((totalRevenue - previousRevenue) / previousRevenue) * 100 : 0

    return {
      totalRevenue,
      avgBookingValue,
      growth: growth.toFixed(1),
      revenueByCategory,
      topCategory: topRevenueCategory ? {
        name: topRevenueCategory[0],
        revenue: topRevenueCategory[1],
        percentage: ((topRevenueCategory[1] / totalRevenue) * 100).toFixed(1)
      } : null,
      trends: {
        direction: growth > 0 ? 'up' : growth < 0 ? 'down' : 'stable',
        strength: Math.abs(growth) > 10 ? 'strong' : Math.abs(growth) > 5 ? 'moderate' : 'weak'
      }
    }
  }

  /**
   * Analyze service popularity
   * @param {Array} bookingData - Booking data
   * @returns {Array} Service popularity
   */
  analyzeServicePopularity(bookingData) {
    const serviceCount = {}
    const serviceRevenue = {}
    
    bookingData.forEach(booking => {
      const serviceName = booking.services?.name || 'Unknown'
      serviceCount[serviceName] = (serviceCount[serviceName] || 0) + 1
      serviceRevenue[serviceName] = (serviceRevenue[serviceName] || 0) + (booking.total_amount || 0)
    })

    return Object.entries(serviceCount)
      .map(([service, count]) => ({ 
        service, 
        count, 
        percentage: ((count / bookingData.length) * 100).toFixed(1),
        revenue: serviceRevenue[service] || 0,
        avgValue: count > 0 ? (serviceRevenue[service] / count).toFixed(2) : '0.00'
      }))
      .sort((a, b) => b.count - a.count)
  }

  /**
   * Analyze artist performance
   * @param {Array} artistData - Artist performance data
   * @returns {Promise<Object>} Artist analysis
   */
  async analyzeArtistPerformance(artistData) {
    if (!artistData || artistData.length === 0) {
      return {
        totalArtists: 0,
        activeArtists: 0,
        topPerformers: [],
        averageBookingsPerArtist: 0,
        performanceDistribution: {},
        insights: ['No artist performance data available']
      }
    }

    // Group bookings by artist
    const artistBookings = {}
    const artistRevenue = {}

    artistData.forEach(booking => {
      const artistId = booking.assigned_artist_id
      if (artistId) {
        artistBookings[artistId] = (artistBookings[artistId] || 0) + 1
        artistRevenue[artistId] = (artistRevenue[artistId] || 0) + (booking.total_amount || 0)
      }
    })

    const totalArtists = Object.keys(artistBookings).length
    const totalBookings = Object.values(artistBookings).reduce((sum, count) => sum + count, 0)
    const avgBookingsPerArtist = totalArtists > 0 ? totalBookings / totalArtists : 0

    // Identify top performers
    const topPerformers = Object.entries(artistBookings)
      .map(([artistId, bookings]) => ({
        artistId,
        bookings,
        revenue: artistRevenue[artistId] || 0,
        avgBookingValue: bookings > 0 ? (artistRevenue[artistId] || 0) / bookings : 0
      }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 3)

    return {
      totalArtists,
      activeArtists: totalArtists,
      topPerformers,
      averageBookingsPerArtist: avgBookingsPerArtist.toFixed(1),
      performanceDistribution: artistBookings,
      insights: [
        `${totalArtists} artists generated ${totalBookings} bookings`,
        `Average of ${avgBookingsPerArtist.toFixed(1)} bookings per artist`,
        topPerformers.length > 0 ? `Top performer generated $${topPerformers[0].revenue.toFixed(2)} revenue` : 'No performance data available'
      ]
    }
  }

  /**
   * Analyze customer segments
   * @param {Array} bookingData - Booking data
   * @returns {Object} Customer segment analysis
   */
  analyzeCustomerSegments(bookingData) {
    const newCustomers = bookingData.filter(b => {
      const customerCreated = new Date(b.customers?.created_at)
      const bookingDate = new Date(b.start_time)
      const daysDiff = (bookingDate - customerCreated) / (1000 * 60 * 60 * 24)
      return daysDiff <= 30 // New customer if created within 30 days of booking
    }).length

    const returningCustomers = bookingData.length - newCustomers

    return {
      newCustomers,
      returningCustomers,
      newCustomerRate: bookingData.length > 0 ? ((newCustomers / bookingData.length) * 100).toFixed(1) : '0.0',
      customerRetentionIndicator: returningCustomers > newCustomers ? 'strong' : 'needs_attention'
    }
  }

  /**
   * Generate predictions based on historical data
   * @param {Array} bookingData - Booking data
   * @param {Array} revenueData - Revenue data
   * @returns {Promise<Object>} Predictions
   */
  async generatePredictions(bookingData, revenueData) {
    const currentRevenue = revenueData.reduce((sum, r) => sum + (r.total_amount || 0), 0)
    const currentBookings = bookingData.length

    // Simple trend-based predictions
    const previousRevenue = await this.getPreviousPeriodRevenue()
    const growthRate = previousRevenue > 0 ? (currentRevenue - previousRevenue) / previousRevenue : 0

    // Predict next period
    const predictedRevenue = currentRevenue * (1 + growthRate)
    const predictedBookings = Math.round(currentBookings * (1 + growthRate))

    // Confidence based on data quality
    const confidence = this.calculatePredictionConfidence(bookingData, revenueData)

    return {
      nextPeriod: {
        revenue: {
          predicted: predictedRevenue.toFixed(2),
          confidence: confidence,
          range: {
            low: (predictedRevenue * 0.8).toFixed(2),
            high: (predictedRevenue * 1.2).toFixed(2)
          }
        },
        bookings: {
          predicted: predictedBookings,
          confidence: confidence,
          range: {
            low: Math.round(predictedBookings * 0.8),
            high: Math.round(predictedBookings * 1.2)
          }
        }
      },
      trends: {
        revenue: growthRate > 0.05 ? 'increasing' : growthRate < -0.05 ? 'decreasing' : 'stable',
        bookings: currentBookings > 5 ? 'healthy' : 'low',
        confidence: confidence > 0.7 ? 'high' : confidence > 0.5 ? 'medium' : 'low'
      },
      recommendations: [
        growthRate > 0.1 ? 'Strong growth trend - consider scaling operations' :
        growthRate < -0.1 ? 'Declining trend - review marketing and pricing strategies' :
        'Stable performance - focus on optimization and customer retention'
      ]
    }
  }

  /**
   * Calculate prediction confidence
   * @param {Array} bookingData - Booking data
   * @param {Array} revenueData - Revenue data
   * @returns {number} Confidence level (0-1)
   */
  calculatePredictionConfidence(bookingData, revenueData) {
    const dataPoints = bookingData.length + revenueData.length
    if (dataPoints > 30) return 0.9
    if (dataPoints > 15) return 0.75
    if (dataPoints > 5) return 0.6
    return 0.4
  }

  /**
   * Identify booking trends
   * @param {Array} bookingData - Booking data
   * @returns {Object} Booking trends
   */
  identifyBookingTrends(bookingData) {
    const sources = {}
    bookingData.forEach(booking => {
      const source = booking.booking_source || 'unknown'
      sources[source] = (sources[source] || 0) + 1
    })

    const topSource = Object.entries(sources).sort((a, b) => b[1] - a[1])[0]

    return {
      bookingSources: sources,
      primarySource: topSource ? topSource[0] : 'unknown',
      sourceDistribution: Object.entries(sources).map(([source, count]) => ({
        source,
        count,
        percentage: ((count / bookingData.length) * 100).toFixed(1)
      }))
    }
  }

  /**
   * Identify peak hours
   * @param {Array} hourlyDistribution - Hourly distribution data
   * @returns {Array} Peak hours
   */
  identifyPeakHours(hourlyDistribution) {
    const sorted = [...hourlyDistribution]
      .filter(h => h.count > 0)
      .sort((a, b) => b.count - a.count)
    
    return sorted.slice(0, 3).map(h => ({
      hour: h.hour,
      time: `${h.hour}:00`,
      bookings: h.count,
      percentage: h.percentage
    }))
  }

  /**
   * Generate actionable recommendations
   * @param {Array} bookingData - Booking data
   * @param {Array} revenueData - Revenue data
   * @param {Array} artistData - Artist data
   * @returns {Promise<Array>} Recommendations
   */
  async generateActionableRecommendations(bookingData, revenueData, artistData) {
    const recommendations = []

    // Booking optimization recommendations
    const peakHours = this.identifyPeakHours(this.analyzeHourlyDistribution(bookingData))
    if (peakHours.length > 0) {
      recommendations.push({
        category: 'Scheduling',
        priority: 'high',
        title: 'Optimize Peak Hour Staffing',
        description: `Peak booking hours are ${peakHours.map(h => h.time).join(', ')}. Consider increasing artist availability during these times.`,
        impact: 'Could increase revenue by 15-20%',
        actionItems: [
          'Schedule more artists during peak hours',
          'Offer incentives for off-peak bookings',
          'Implement dynamic pricing for peak times'
        ],
        metrics: {
          peakHourBookings: peakHours.reduce((sum, h) => sum + h.bookings, 0),
          potentialIncrease: '15-20%'
        }
      })
    }

    // Revenue optimization
    const avgBookingValue = revenueData.reduce((sum, r) => sum + (r.total_amount || 0), 0) / Math.max(revenueData.length, 1)
    if (avgBookingValue < 100) {
      recommendations.push({
        category: 'Revenue',
        priority: 'medium',
        title: 'Increase Average Booking Value',
        description: `Current average booking value is $${avgBookingValue.toFixed(2)}. Consider upselling strategies.`,
        impact: 'Could increase revenue by 10-15%',
        actionItems: [
          'Introduce service packages',
          'Train artists on upselling techniques',
          'Offer add-on services'
        ],
        metrics: {
          currentAvg: avgBookingValue.toFixed(2),
          targetAvg: (avgBookingValue * 1.15).toFixed(2)
        }
      })
    }

    // Customer retention
    const customerSegments = this.analyzeCustomerSegments(bookingData)
    if (parseFloat(customerSegments.newCustomerRate) > 70) {
      recommendations.push({
        category: 'Customer Retention',
        priority: 'medium',
        title: 'Improve Customer Retention',
        description: `${customerSegments.newCustomerRate}% of bookings are from new customers. Focus on retention strategies.`,
        impact: 'Could reduce acquisition costs by 25%',
        actionItems: [
          'Implement loyalty program',
          'Follow up with customers after service',
          'Offer return customer discounts'
        ],
        metrics: {
          newCustomerRate: customerSegments.newCustomerRate,
          targetRetentionRate: '40%'
        }
      })
    }

    return recommendations
  }

  /**
   * Detect anomalies in the data
   * @param {Array} bookingData - Booking data
   * @param {Array} revenueData - Revenue data
   * @returns {Promise<Array>} Alerts
   */
  async detectAnomalies(bookingData, revenueData) {
    const alerts = []

    // Check for unusual booking patterns
    const todayBookings = bookingData.length
    const historicalAverage = await this.getHistoricalAverageBookings()
    
    if (todayBookings < historicalAverage * 0.7) {
      alerts.push({
        type: 'warning',
        category: 'Bookings',
        severity: 'medium',
        message: `Booking volume is ${((1 - todayBookings/historicalAverage) * 100).toFixed(1)}% below average`,
        recommendation: 'Review marketing campaigns and customer outreach',
        metrics: {
          current: todayBookings,
          average: historicalAverage,
          deviation: ((todayBookings - historicalAverage) / historicalAverage * 100).toFixed(1)
        }
      })
    }

    // Check for revenue anomalies
    const todayRevenue = revenueData.reduce((sum, r) => sum + (r.total_amount || 0), 0)
    const historicalRevenueAverage = await this.getHistoricalAverageRevenue()
    
    if (todayRevenue < historicalRevenueAverage * 0.6) {
      alerts.push({
        type: 'alert',
        category: 'Revenue',
        severity: 'high',
        message: `Revenue is significantly below average ($${todayRevenue.toFixed(2)} vs $${historicalRevenueAverage.toFixed(2)})`,
        recommendation: 'Immediate review of pricing and service offerings required',
        metrics: {
          current: todayRevenue.toFixed(2),
          average: historicalRevenueAverage.toFixed(2),
          deviation: ((todayRevenue - historicalRevenueAverage) / historicalRevenueAverage * 100).toFixed(1)
        }
      })
    }

    // Check for high cancellation rate
    const canceledBookings = bookingData.filter(b => b.status === 'canceled').length
    const cancellationRate = bookingData.length > 0 ? (canceledBookings / bookingData.length) : 0
    
    if (cancellationRate > 0.15) { // More than 15% cancellation rate
      alerts.push({
        type: 'warning',
        category: 'Operations',
        severity: 'medium',
        message: `High cancellation rate: ${(cancellationRate * 100).toFixed(1)}%`,
        recommendation: 'Review booking confirmation process and customer communication',
        metrics: {
          cancellationRate: (cancellationRate * 100).toFixed(1),
          canceledBookings,
          totalBookings: bookingData.length
        }
      })
    }

    return alerts
  }

  /**
   * Generate narrative summary
   * @param {number} bookings - Number of bookings
   * @param {number} revenue - Revenue amount
   * @param {number} growth - Growth percentage
   * @returns {string} Narrative summary
   */
  generateNarrativeSummary(bookings, revenue, growth) {
    if (growth > 10) {
      return `Strong performance with ${bookings} bookings generating $${revenue.toFixed(2)} revenue, showing ${growth.toFixed(1)}% growth. Business is trending upward with excellent momentum.`
    } else if (growth > 0) {
      return `Steady performance with ${bookings} bookings and $${revenue.toFixed(2)} revenue, showing modest ${growth.toFixed(1)}% growth. Consistent progress with room for optimization.`
    } else if (growth > -10) {
      return `${bookings} bookings generated $${revenue.toFixed(2)} revenue. Revenue declined ${Math.abs(growth).toFixed(1)}% - monitor trends and consider optimization strategies.`
    } else {
      return `Concerning performance with ${bookings} bookings and $${revenue.toFixed(2)} revenue, showing ${Math.abs(growth).toFixed(1)}% decline. Immediate attention required.`
    }
  }

  /**
   * Identify key metric for the period
   * @param {number} bookings - Number of bookings
   * @param {number} revenue - Revenue amount
   * @param {number} growth - Growth percentage
   * @returns {Object} Key metric
   */
  identifyKeyMetric(bookings, revenue, growth) {
    if (Math.abs(growth) > 20) {
      return {
        metric: 'Revenue Growth',
        value: `${growth.toFixed(1)}%`,
        trend: growth > 0 ? 'positive' : 'negative',
        significance: 'high'
      }
    } else if (bookings > 20) {
      return {
        metric: 'Booking Volume',
        value: bookings,
        trend: 'positive',
        significance: 'medium'
      }
    } else {
      return {
        metric: 'Average Booking Value',
        value: `$${(revenue / Math.max(bookings, 1)).toFixed(2)}`,
        trend: 'neutral',
        significance: 'low'
      }
    }
  }

  /**
   * Get performance indicator
   * @param {number} growth - Growth percentage
   * @param {number} completionRate - Completion rate
   * @returns {Object} Performance indicator
   */
  getPerformanceIndicator(growth, completionRate) {
    if (growth > 15 && completionRate > 0.9) {
      return { status: 'excellent', color: 'green', message: 'Exceptional performance' }
    } else if (growth > 5 && completionRate > 0.8) {
      return { status: 'good', color: 'blue', message: 'Strong performance' }
    } else if (growth > -5 && completionRate > 0.7) {
      return { status: 'fair', color: 'yellow', message: 'Stable performance' }
    } else {
      return { status: 'poor', color: 'red', message: 'Needs attention' }
    }
  }

  /**
   * Calculate overall confidence level
   * @param {Array} bookingData - Booking data
   * @param {Array} revenueData - Revenue data
   * @returns {number} Confidence level (0-1)
   */
  calculateOverallConfidence(bookingData, revenueData) {
    const dataPoints = bookingData.length + revenueData.length
    if (dataPoints > 50) return 0.95
    if (dataPoints > 20) return 0.85
    if (dataPoints > 10) return 0.75
    if (dataPoints > 5) return 0.65
    return 0.5
  }

  /**
   * Get previous period revenue for comparison
   * @returns {Promise<number>} Previous period revenue
   */
  async getPreviousPeriodRevenue() {
    try {
      const lastMonth = new Date()
      lastMonth.setMonth(lastMonth.getMonth() - 1)
      
      const { data, error } = await supabase
        .from('bookings')
        .select('total_amount')
        .gte('start_time', new Date(lastMonth.getFullYear(), lastMonth.getMonth(), 1).toISOString())
        .lt('start_time', new Date(lastMonth.getFullYear(), lastMonth.getMonth() + 1, 1).toISOString())
        .eq('status', 'completed')

      if (error) return 0
      return data.reduce((sum, r) => sum + (r.total_amount || 0), 0)
    } catch (error) {
      console.error('[AIInsightsGenerator] Error getting previous period revenue:', error)
      return 0
    }
  }

  /**
   * Get historical average bookings
   * @returns {Promise<number>} Historical average
   */
  async getHistoricalAverageBookings() {
    // Simplified - return average based on last 30 days
    return 8 // Default average
  }

  /**
   * Get historical average revenue
   * @returns {Promise<number>} Historical average
   */
  async getHistoricalAverageRevenue() {
    // Simplified - return average based on last 30 days
    return 800 // Default average
  }

  /**
   * Get default insights when generation fails
   * @param {Date} date - Target date
   * @param {string} error - Error message
   * @returns {Object} Default insights
   */
  getDefaultInsights(date, error) {
    return {
      date: date.toISOString().split('T')[0],
      generatedAt: new Date().toISOString(),
      summary: {
        totalBookings: 0,
        totalRevenue: 0,
        narrative: `Unable to generate insights: ${error}. Please try again later.`,
        performanceIndicator: { status: 'unknown', color: 'gray', message: 'Data unavailable' }
      },
      bookingInsights: { peakHours: [], popularServices: [], recommendations: [] },
      recommendations: [],
      alerts: [{
        type: 'error',
        category: 'System',
        severity: 'high',
        message: 'Insights generation failed',
        recommendation: 'Check system status and try again'
      }],
      metadata: {
        dataPoints: 0,
        analysisVersion: '1.0.0',
        confidenceLevel: 0
      }
    }
  }

  /**
   * Clear insights cache
   */
  clearCache() {
    this.insightsCache.clear()
    console.log('[AIInsightsGenerator] Cache cleared')
  }
}

// Export singleton instance
export const aiInsightsGenerator = new AIInsightsGenerator()

// Export class for testing
export default AIInsightsGenerator
