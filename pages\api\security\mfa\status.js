/**
 * MFA Status API Endpoint for Ocean Soul Sparkles
 * Returns MFA status for the authenticated user
 * 
 * Phase 9.1: Enhanced Authentication System
 */

import { withAdminAuth } from '@/lib/admin-auth'
import { mfaManager } from '@/lib/security/mfa-manager'

async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const userId = req.user.id

    // Get MFA status for user
    const status = await mfaManager.getMFAStatus(userId)

    res.status(200).json(status)

  } catch (error) {
    console.error('MFA status error:', error)
    res.status(500).json({
      error: 'Failed to get MFA status',
      message: error.message
    })
  }
}

export default withAdminAuth(handler)
