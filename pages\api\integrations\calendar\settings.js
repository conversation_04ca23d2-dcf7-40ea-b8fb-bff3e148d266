/**
 * Calendar Settings API Endpoint for Ocean Soul Sparkles
 * Handles calendar integration settings management
 * 
 * Phase 7: Advanced Integrations & Ecosystem
 */

import { authenticateAdminRequest } from '@/lib/admin-auth'
import { integrationRateLimit } from '@/lib/integrations/rate-limiter'
import { RequestValidator, AuditLogger } from '@/lib/integrations/security-utils'
import { updateIntegrationSettings, getIntegrationSettings } from '@/lib/supabase'

/**
 * Calendar Settings Handler
 * GET /api/integrations/calendar/settings - Get calendar settings
 * PUT /api/integrations/calendar/settings - Update calendar settings
 */
export default async function handler(req, res) {
  // Apply rate limiting
  await new Promise((resolve, reject) => {
    integrationRateLimit(req, res, (error) => {
      if (error) reject(error)
      else resolve()
    })
  })

  const startTime = Date.now()

  try {
    // Validate request method
    RequestValidator.validateEndpointAccess(req, ['GET', 'PUT'])

    // Authenticate user
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.success) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      })
    }

    const { user } = authResult
    const userId = user.id

    if (req.method === 'GET') {
      // Get calendar settings
      const { provider } = req.query

      let settings
      if (provider) {
        // Get settings for specific provider
        const providerSettings = await getIntegrationSettings(userId, provider)
        settings = providerSettings.length > 0 ? providerSettings[0] : null
      } else {
        // Get all calendar settings
        const allSettings = await getIntegrationSettings(userId)
        const calendarProviders = ['google_calendar', 'outlook', 'apple_calendar']
        settings = allSettings.filter(setting => 
          calendarProviders.includes(setting.provider)
        )
      }

      await AuditLogger.logApiAccess(
        userId,
        req.url,
        req.method,
        200,
        Date.now() - startTime,
        { action: 'get_calendar_settings', provider }
      )

      return res.status(200).json({
        success: true,
        settings
      })

    } else if (req.method === 'PUT') {
      // Update calendar settings
      const { provider, settings } = req.body

      if (!provider || !settings) {
        return res.status(400).json({
          error: 'Missing required fields',
          message: 'Provider and settings are required'
        })
      }

      // Validate provider
      const validProviders = ['google_calendar', 'outlook', 'apple_calendar']
      if (!validProviders.includes(provider)) {
        return res.status(400).json({
          error: 'Invalid provider',
          message: `Provider must be one of: ${validProviders.join(', ')}`
        })
      }

      // Validate settings
      const validationResult = validateSettings(settings)
      if (!validationResult.valid) {
        return res.status(400).json({
          error: 'Invalid settings',
          message: validationResult.message
        })
      }

      // Update settings
      await updateIntegrationSettings(userId, provider, settings)

      await AuditLogger.logIntegrationActivity(
        userId,
        provider,
        'settings_updated',
        'success',
        { settings }
      )

      await AuditLogger.logApiAccess(
        userId,
        req.url,
        req.method,
        200,
        Date.now() - startTime,
        { action: 'update_calendar_settings', provider }
      )

      return res.status(200).json({
        success: true,
        message: 'Calendar settings updated successfully'
      })
    }

  } catch (error) {
    console.error('Calendar settings error:', error)

    await AuditLogger.logApiAccess(
      req.user?.id || null,
      req.url,
      req.method,
      500,
      Date.now() - startTime,
      { error: error.message }
    )

    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to manage calendar settings'
    })
  }
}

/**
 * Validate calendar settings
 */
function validateSettings(settings) {
  const allowedSettings = [
    'autoSync',
    'syncFrequency',
    'conflictResolution',
    'defaultCalendar',
    'calendarsToSync',
    'syncDirection',
    'importKeywords',
    'excludeKeywords',
    'reminderSettings',
    'privacyLevel'
  ]

  // Check for unknown settings
  const unknownSettings = Object.keys(settings).filter(key => 
    !allowedSettings.includes(key)
  )

  if (unknownSettings.length > 0) {
    return {
      valid: false,
      message: `Unknown settings: ${unknownSettings.join(', ')}`
    }
  }

  // Validate specific settings
  if (settings.syncFrequency) {
    const validFrequencies = ['realtime', 'hourly', 'daily', 'manual']
    if (!validFrequencies.includes(settings.syncFrequency)) {
      return {
        valid: false,
        message: `Invalid syncFrequency. Must be one of: ${validFrequencies.join(', ')}`
      }
    }
  }

  if (settings.conflictResolution) {
    const validResolutions = ['manual', 'ocean_soul_sparkles_wins', 'external_wins', 'merge']
    if (!validResolutions.includes(settings.conflictResolution)) {
      return {
        valid: false,
        message: `Invalid conflictResolution. Must be one of: ${validResolutions.join(', ')}`
      }
    }
  }

  if (settings.syncDirection) {
    const validDirections = ['bidirectional', 'to_external', 'from_external']
    if (!validDirections.includes(settings.syncDirection)) {
      return {
        valid: false,
        message: `Invalid syncDirection. Must be one of: ${validDirections.join(', ')}`
      }
    }
  }

  if (settings.privacyLevel) {
    const validLevels = ['public', 'private', 'confidential']
    if (!validLevels.includes(settings.privacyLevel)) {
      return {
        valid: false,
        message: `Invalid privacyLevel. Must be one of: ${validLevels.join(', ')}`
      }
    }
  }

  if (settings.calendarsToSync && !Array.isArray(settings.calendarsToSync)) {
    return {
      valid: false,
      message: 'calendarsToSync must be an array'
    }
  }

  if (settings.importKeywords && !Array.isArray(settings.importKeywords)) {
    return {
      valid: false,
      message: 'importKeywords must be an array'
    }
  }

  if (settings.excludeKeywords && !Array.isArray(settings.excludeKeywords)) {
    return {
      valid: false,
      message: 'excludeKeywords must be an array'
    }
  }

  if (settings.reminderSettings && typeof settings.reminderSettings !== 'object') {
    return {
      valid: false,
      message: 'reminderSettings must be an object'
    }
  }

  return { valid: true }
}

/**
 * API Route Configuration
 */
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
}
