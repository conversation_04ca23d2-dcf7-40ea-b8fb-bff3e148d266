/**
 * Business Customer Sync API Endpoint for Ocean Soul Sparkles
 * Handles synchronization of customers to marketing platforms
 * 
 * Phase 7.4: Business Management Integrations
 */

import { authenticateAdminRequest } from '@/lib/admin-auth'
import { syncRateLimit } from '@/lib/integrations/rate-limiter'
import { RequestValidator, AuditLogger } from '@/lib/integrations/security-utils'
import BusinessManager from '@/lib/integrations/business-manager'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

/**
 * Customer Sync Handler
 * POST /api/integrations/business/sync-customers - Sync customers to marketing platforms
 */
export default async function handler(req, res) {
  // Apply rate limiting
  await new Promise((resolve, reject) => {
    syncRateLimit(req, res, (error) => {
      if (error) reject(error)
      else resolve()
    })
  })

  const startTime = Date.now()

  try {
    // Validate request method
    RequestValidator.validateEndpointAccess(req, ['POST'])

    // Authenticate user
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.success) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      })
    }

    const { user } = authResult
    const userId = user.id

    // Get request parameters
    const { 
      limit = 500,
      segmentTags = ['Ocean Soul Sparkles Customer'],
      includeInactive = false,
      forceSync = false
    } = req.body

    // Initialize business manager
    const businessManager = new BusinessManager(userId)

    // Get customers to sync
    const customers = await getCustomersToSync(userId, {
      limit: parseInt(limit),
      includeInactive,
      forceSync
    })

    if (customers.length === 0) {
      return res.status(200).json({
        success: true,
        message: 'No customers found to sync',
        results: []
      })
    }

    // Sync customers to marketing platforms
    const syncResults = await businessManager.syncCustomersToMarketing(customers, {
      segmentTags
    })

    // Update customers with sync status
    await updateCustomerSyncStatus(customers, syncResults)

    const totalSynced = syncResults.reduce((sum, result) => 
      sum + (result.successCount || 0), 0
    )

    await AuditLogger.logIntegrationActivity(
      userId,
      'business_management',
      'customers_sync_completed',
      'success',
      {
        customerCount: customers.length,
        syncResults: syncResults.length,
        totalSynced,
        segmentTags
      }
    )

    await AuditLogger.logApiAccess(
      userId,
      req.url,
      req.method,
      200,
      Date.now() - startTime,
      { 
        action: 'sync_customers_to_marketing',
        customers: customers.length,
        totalSynced
      }
    )

    return res.status(200).json({
      success: true,
      message: `Customer sync completed. ${totalSynced} customers synced successfully.`,
      results: syncResults,
      summary: {
        totalCustomers: customers.length,
        totalSynced,
        providers: syncResults.length
      }
    })

  } catch (error) {
    console.error('Customer sync error:', error)

    await AuditLogger.logSecurityEvent(
      req.user?.id || null,
      'customer_sync_error',
      {
        error: error.message,
        stack: error.stack,
        endpoint: req.url,
        method: req.method,
        userAgent: req.headers['user-agent'],
        ipAddress: req.headers['x-forwarded-for'] || req.connection?.remoteAddress
      },
      'error'
    )

    await AuditLogger.logApiAccess(
      req.user?.id || null,
      req.url,
      req.method,
      500,
      Date.now() - startTime,
      { error: error.message }
    )

    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to sync customers to marketing platforms'
    })
  }
}

/**
 * Get customers to sync to marketing platforms
 */
async function getCustomersToSync(userId, options) {
  try {
    // Get customers with booking statistics
    const { data: customers, error } = await supabase
      .from('customers')
      .select(`
        *,
        bookings (
          id,
          total_amount,
          status,
          created_at
        )
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(options.limit)

    if (error) {
      throw new Error(`Failed to fetch customers: ${error.message}`)
    }

    // Process customers and calculate statistics
    const processedCustomers = customers.map(customer => {
      const bookings = customer.bookings || []
      const completedBookings = bookings.filter(b => b.status === 'completed')
      
      const totalSpent = completedBookings.reduce((sum, booking) => 
        sum + parseFloat(booking.total_amount || 0), 0
      )

      const lastBooking = bookings.length > 0 
        ? bookings.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))[0]
        : null

      const daysSinceLastBooking = lastBooking 
        ? Math.floor((new Date() - new Date(lastBooking.created_at)) / (1000 * 60 * 60 * 24))
        : null

      return {
        id: customer.id,
        email: customer.email,
        firstName: customer.first_name || customer.name?.split(' ')[0] || '',
        lastName: customer.last_name || customer.name?.split(' ').slice(1).join(' ') || '',
        phone: customer.phone,
        totalBookings: completedBookings.length,
        totalSpent,
        lastBookingDate: lastBooking?.created_at || null,
        daysSinceLastBooking,
        isActive: daysSinceLastBooking === null || daysSinceLastBooking < 90,
        createdAt: customer.created_at,
        marketingSyncData: customer.marketing_sync_data
      }
    })

    // Filter customers based on options
    let filteredCustomers = processedCustomers

    // Filter out customers without email
    filteredCustomers = filteredCustomers.filter(customer => 
      customer.email && customer.email.includes('@')
    )

    // Filter inactive customers if not included
    if (!options.includeInactive) {
      filteredCustomers = filteredCustomers.filter(customer => customer.isActive)
    }

    // Filter out already synced customers unless force sync
    if (!options.forceSync) {
      filteredCustomers = filteredCustomers.filter(customer => 
        !customer.marketingSyncData || 
        !customer.marketingSyncData.lastSyncAt ||
        new Date(customer.marketingSyncData.lastSyncAt) < new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 7 days ago
      )
    }

    return filteredCustomers

  } catch (error) {
    console.error('Error getting customers to sync:', error)
    return []
  }
}

/**
 * Update customers with marketing sync status
 */
async function updateCustomerSyncStatus(customers, syncResults) {
  try {
    for (const customer of customers) {
      // Find sync results for this customer
      const customerSyncResults = []
      
      for (const providerResult of syncResults) {
        if (providerResult.results) {
          const customerResult = providerResult.results.find(r => r.customerId === customer.id)
          if (customerResult) {
            customerSyncResults.push({
              provider: providerResult.provider,
              success: customerResult.success,
              mailchimpId: customerResult.mailchimpId,
              error: customerResult.error
            })
          }
        }
      }

      if (customerSyncResults.length > 0) {
        const syncData = {
          lastSyncAt: new Date().toISOString(),
          syncResults: customerSyncResults,
          syncedProviders: customerSyncResults.filter(r => r.success).map(r => r.provider)
        }

        await supabase
          .from('customers')
          .update({
            marketing_sync_data: syncData,
            updated_at: new Date().toISOString()
          })
          .eq('id', customer.id)
      }
    }

  } catch (error) {
    console.error('Error updating customer sync status:', error)
  }
}

/**
 * API Route Configuration
 */
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
}
