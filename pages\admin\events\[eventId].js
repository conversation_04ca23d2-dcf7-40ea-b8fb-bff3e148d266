import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import AdminLayout from '@/components/admin/AdminLayout';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import Modal from '@/components/admin/Modal';
import EventExpenseTracker from '@/components/admin/EventExpenseTracker';
import { toast } from 'react-toastify';
import authTokenManager from '@/lib/auth-token-manager';
import styles from '@/styles/admin/EventDetail.module.css';

/**
 * Individual Event Management Page
 * Manages specific event details, QR codes, and analytics
 */
export default function EventDetail() {
  const router = useRouter();
  const { eventId } = router.query;

  const [event, setEvent] = useState(null);
  const [qrCodes, setQrCodes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showQRModal, setShowQRModal] = useState(false);
  const [selectedQR, setSelectedQR] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [showEditModal, setShowEditModal] = useState(false);

  useEffect(() => {
    if (eventId) {
      fetchEventData();
      fetchQRCodes();
    }
  }, [eventId, fetchEventData, fetchQRCodes]);

  const fetchEventData = async () => {
    try {
      const token = authTokenManager.getTokenFromStorage() || await authTokenManager.getAuthToken();

      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await fetch(`/api/admin/events/${eventId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch event data`);
      }

      const data = await response.json();
      setEvent(data.event);
    } catch (error) {
      console.error('Error fetching event:', error);
      toast.error(`Failed to load event data: ${error.message}`);
    }
  };

  const fetchQRCodes = async () => {
    try {
      setLoading(true);
      const token = authTokenManager.getTokenFromStorage() || await authTokenManager.getAuthToken();

      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await fetch(`/api/admin/events/${eventId}/qr-codes?include_analytics=true`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch QR codes`);
      }

      const data = await response.json();
      setQrCodes(data.qr_codes || []);
    } catch (error) {
      console.error('Error fetching QR codes:', error);
      toast.error(`Failed to load QR codes: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateQR = async (qrData) => {
    try {
      const token = authTokenManager.getTokenFromStorage() || await authTokenManager.getAuthToken();

      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await fetch(`/api/admin/events/${eventId}/qr-codes`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(qrData)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: Failed to generate QR code`);
      }

      const data = await response.json();
      setQrCodes(prev => [data.qr_code, ...prev]);
      setShowQRModal(false);
      toast.success('QR code generated successfully');
    } catch (error) {
      console.error('Error generating QR code:', error);
      toast.error(`Failed to generate QR code: ${error.message}`);
    }
  };

  const handleViewAnalytics = (qrCode) => {
    setSelectedQR(qrCode);
    setActiveTab('analytics');
  };

  const downloadQRCode = (qrCode) => {
    const link = document.createElement('a');
    link.href = qrCode.image;
    link.download = `${qrCode.eventName}-QR-${qrCode.code}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleEditEvent = async (eventData) => {
    try {
      const token = authTokenManager.getTokenFromStorage() || await authTokenManager.getAuthToken();

      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await fetch(`/api/admin/events/${eventId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(eventData)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: Failed to update event`);
      }

      const data = await response.json();
      setEvent(data.event);
      setShowEditModal(false);
      toast.success('Event updated successfully');
    } catch (error) {
      console.error('Error updating event:', error);
      toast.error(`Failed to update event: ${error.message}`);
    }
  };

  const getEventStatus = () => {
    if (!event) return 'unknown';

    const now = new Date();
    const startDate = new Date(event.start_date);
    const endDate = new Date(event.end_date);

    if (now < startDate) return 'upcoming';
    if (now > endDate) return 'completed';
    return 'active';
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-AU', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!event && !loading) {
    return (
      <ProtectedRoute>
        <AdminLayout>
          <div className={styles.errorContainer}>
            <h1>Event Not Found</h1>
            <p>The requested event could not be found.</p>
            <button onClick={() => router.push('/admin/events')}>
              Back to Events
            </button>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <AdminLayout>
        <div className={styles.container}>
          {loading && !event ? (
            <div className={styles.loading}>
              <div className={styles.spinner}></div>
              <p>Loading event data...</p>
            </div>
          ) : (
            <>
              <div className={styles.header}>
                <button
                  className={styles.backButton}
                  onClick={() => router.push('/admin/events')}
                >
                  ← Back to Events
                </button>

                <div className={styles.eventHeader}>
                  <div className={styles.eventTitle}>
                    <h1>{event?.name}</h1>
                    <span className={`${styles.statusBadge} ${styles[getEventStatus()]}`}>
                      {getEventStatus()}
                    </span>
                  </div>
                  <div className={styles.headerActions}>
                    <button
                      className={styles.editButton}
                      onClick={() => setShowEditModal(true)}
                    >
                      <span className={styles.buttonIcon}>✏️</span>
                      Edit Event
                    </button>
                    <button
                      className={styles.generateButton}
                      onClick={() => setShowQRModal(true)}
                    >
                      <span className={styles.buttonIcon}>📱</span>
                      Generate QR Code
                    </button>
                  </div>
                </div>
              </div>

              <div className={styles.tabs}>
                <button
                  className={`${styles.tab} ${activeTab === 'overview' ? styles.active : ''}`}
                  onClick={() => setActiveTab('overview')}
                >
                  📊 Overview
                </button>
                <button
                  className={`${styles.tab} ${activeTab === 'expenses' ? styles.active : ''}`}
                  onClick={() => setActiveTab('expenses')}
                >
                  💰 Expenses
                </button>
                <button
                  className={`${styles.tab} ${activeTab === 'qr-codes' ? styles.active : ''}`}
                  onClick={() => setActiveTab('qr-codes')}
                >
                  📱 QR Codes ({qrCodes.length})
                </button>
                <button
                  className={`${styles.tab} ${activeTab === 'analytics' ? styles.active : ''}`}
                  onClick={() => setActiveTab('analytics')}
                >
                  📈 Analytics
                </button>
              </div>

              <div className={styles.tabContent}>
                {activeTab === 'overview' && (
                  <EventOverview event={event} qrCodes={qrCodes} />
                )}

                {activeTab === 'expenses' && (
                  <EventExpenseTracker
                    eventId={eventId}
                    event={event}
                    onExpenseUpdate={() => {
                      // Refresh event data to update financial totals
                      fetchEventData();
                    }}
                  />
                )}

                {activeTab === 'qr-codes' && (
                  <QRCodesTab
                    qrCodes={qrCodes}
                    loading={loading}
                    onViewAnalytics={handleViewAnalytics}
                    onDownload={downloadQRCode}
                  />
                )}

                {activeTab === 'analytics' && (
                  <AnalyticsTab
                    event={event}
                    qrCodes={qrCodes}
                    selectedQR={selectedQR}
                  />
                )}
              </div>

              {showQRModal && (
                <GenerateQRModal
                  event={event}
                  onClose={() => setShowQRModal(false)}
                  onSubmit={handleGenerateQR}
                />
              )}

              {showEditModal && (
                <EditEventModal
                  event={event}
                  onClose={() => setShowEditModal(false)}
                  onSubmit={handleEditEvent}
                />
              )}
            </>
          )}
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}

/**
 * Event Overview Tab Component
 */
function EventOverview({ event, qrCodes }) {
  const totalScans = qrCodes.reduce((sum, qr) => sum + (qr.usage_count || 0), 0);
  const totalRevenue = qrCodes.reduce((sum, qr) => {
    const revenue = qr.revenue_tracking?.total_revenue || 0;
    return sum + parseFloat(revenue);
  }, 0);

  return (
    <div className={styles.overview}>
      <div className={styles.eventDetails}>
        <h3>Event Details</h3>
        <div className={styles.detailsGrid}>
          <div className={styles.detail}>
            <span className={styles.detailLabel}>Location:</span>
            <span className={styles.detailValue}>{event.location}</span>
          </div>
          <div className={styles.detail}>
            <span className={styles.detailLabel}>Start:</span>
            <span className={styles.detailValue}>
              {new Date(event.start_date).toLocaleDateString('en-AU', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              })}
            </span>
          </div>
          <div className={styles.detail}>
            <span className={styles.detailLabel}>End:</span>
            <span className={styles.detailValue}>
              {new Date(event.end_date).toLocaleDateString('en-AU', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              })}
            </span>
          </div>
          {event.max_capacity && (
            <div className={styles.detail}>
              <span className={styles.detailLabel}>Capacity:</span>
              <span className={styles.detailValue}>
                {event.current_bookings || 0} / {event.max_capacity}
              </span>
            </div>
          )}
        </div>

        {event.description && (
          <div className={styles.description}>
            <h4>Description</h4>
            <p>{event.description}</p>
          </div>
        )}
      </div>

      <div className={styles.quickStats}>
        <h3>Quick Stats</h3>
        <div className={styles.statsGrid}>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{qrCodes.length}</div>
            <div className={styles.statLabel}>QR Codes</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{totalScans}</div>
            <div className={styles.statLabel}>Total Scans</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>${totalRevenue.toFixed(2)}</div>
            <div className={styles.statLabel}>Revenue</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>
              {qrCodes.filter(qr => qr.is_active).length}
            </div>
            <div className={styles.statLabel}>Active QR Codes</div>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * QR Codes Tab Component
 */
function QRCodesTab({ qrCodes, loading, onViewAnalytics, onDownload }) {
  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>Loading QR codes...</p>
      </div>
    );
  }

  if (qrCodes.length === 0) {
    return (
      <div className={styles.emptyState}>
        <div className={styles.emptyIcon}>📱</div>
        <h3>No QR Codes Generated</h3>
        <p>Generate your first QR code to start accepting mobile bookings for this event.</p>
      </div>
    );
  }

  return (
    <div className={styles.qrCodesGrid}>
      {qrCodes.map(qrCode => (
        <QRCodeCard
          key={qrCode.id}
          qrCode={qrCode}
          onViewAnalytics={() => onViewAnalytics(qrCode)}
          onDownload={() => onDownload(qrCode)}
        />
      ))}
    </div>
  );
}

/**
 * QR Code Card Component
 */
function QRCodeCard({ qrCode, onViewAnalytics, onDownload }) {
  const revenue = qrCode.revenue_tracking?.total_revenue || 0;
  const bookings = qrCode.revenue_tracking?.booking_count || 0;

  return (
    <div className={styles.qrCard}>
      <div className={styles.qrHeader}>
        <h4>QR Code</h4>
        <span className={`${styles.qrStatus} ${qrCode.is_active ? styles.active : styles.inactive}`}>
          {qrCode.is_active ? 'Active' : 'Inactive'}
        </span>
      </div>

      <div className={styles.qrCode}>
        <span className={styles.qrCodeText}>{qrCode.code}</span>
      </div>

      <div className={styles.qrStats}>
        <div className={styles.qrStat}>
          <span className={styles.qrStatValue}>{qrCode.usage_count || 0}</span>
          <span className={styles.qrStatLabel}>Scans</span>
        </div>
        <div className={styles.qrStat}>
          <span className={styles.qrStatValue}>{bookings}</span>
          <span className={styles.qrStatLabel}>Bookings</span>
        </div>
        <div className={styles.qrStat}>
          <span className={styles.qrStatValue}>${parseFloat(revenue).toFixed(2)}</span>
          <span className={styles.qrStatLabel}>Revenue</span>
        </div>
      </div>

      <div className={styles.qrActions}>
        <button
          className={styles.qrActionButton}
          onClick={onViewAnalytics}
        >
          View Analytics
        </button>
        <button
          className={styles.qrActionButton}
          onClick={onDownload}
        >
          Download
        </button>
      </div>
    </div>
  );
}

/**
 * Analytics Tab Component
 */
function AnalyticsTab({ event, qrCodes, selectedQR }) {
  // Calculate overall analytics
  const totalScans = qrCodes.reduce((sum, qr) => sum + (qr.usage_count || 0), 0);
  const totalRevenue = qrCodes.reduce((sum, qr) => {
    const revenue = qr.revenue_tracking?.total_revenue || 0;
    return sum + parseFloat(revenue);
  }, 0);
  const totalBookings = qrCodes.reduce((sum, qr) => {
    const bookings = qr.revenue_tracking?.booking_count || 0;
    return sum + parseInt(bookings);
  }, 0);

  const conversionRate = totalScans > 0 ? (totalBookings / totalScans * 100) : 0;
  const averageBookingValue = totalBookings > 0 ? (totalRevenue / totalBookings) : 0;

  return (
    <div className={styles.analytics}>
      <div className={styles.analyticsHeader}>
        <h3>Event Analytics</h3>
        <p>Performance metrics for {event?.name}</p>
      </div>

      <div className={styles.metricsGrid}>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{totalScans}</div>
          <div className={styles.metricLabel}>Total QR Scans</div>
        </div>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{totalBookings}</div>
          <div className={styles.metricLabel}>Total Bookings</div>
        </div>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>${totalRevenue.toFixed(2)}</div>
          <div className={styles.metricLabel}>Total Revenue</div>
        </div>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{conversionRate.toFixed(1)}%</div>
          <div className={styles.metricLabel}>Conversion Rate</div>
        </div>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>${averageBookingValue.toFixed(2)}</div>
          <div className={styles.metricLabel}>Avg Booking Value</div>
        </div>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{qrCodes.filter(qr => qr.is_active).length}</div>
          <div className={styles.metricLabel}>Active QR Codes</div>
        </div>
      </div>

      {selectedQR && (
        <div className={styles.selectedQRAnalytics}>
          <h4>QR Code: {selectedQR.code}</h4>
          <div className={styles.qrMetrics}>
            <div className={styles.qrMetric}>
              <span className={styles.qrMetricLabel}>Scans:</span>
              <span className={styles.qrMetricValue}>{selectedQR.usage_count || 0}</span>
            </div>
            <div className={styles.qrMetric}>
              <span className={styles.qrMetricLabel}>Revenue:</span>
              <span className={styles.qrMetricValue}>
                ${parseFloat(selectedQR.revenue_tracking?.total_revenue || 0).toFixed(2)}
              </span>
            </div>
            <div className={styles.qrMetric}>
              <span className={styles.qrMetricLabel}>Bookings:</span>
              <span className={styles.qrMetricValue}>
                {selectedQR.revenue_tracking?.booking_count || 0}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

/**
 * Generate QR Modal Component
 */
function GenerateQRModal({ event, onClose, onSubmit }) {
  const [formData, setFormData] = useState({
    assigned_artists: [],
    available_services: [],
    max_usage: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  return (
    <Modal onClose={onClose} title="Generate QR Code">
      <form onSubmit={handleSubmit} className={styles.form}>
        <div className={styles.formGroup}>
          <label className={styles.label}>Maximum Usage (Optional)</label>
          <input
            type="number"
            name="max_usage"
            value={formData.max_usage}
            onChange={handleChange}
            className={styles.input}
            min="1"
            placeholder="Leave empty for unlimited usage"
          />
          <small className={styles.helpText}>
            Set a limit on how many times this QR code can be scanned
          </small>
        </div>

        <div className={styles.formActions}>
          <button
            type="button"
            onClick={onClose}
            className={styles.cancelButton}
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className={styles.submitButton}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Generating...' : 'Generate QR Code'}
          </button>
        </div>
      </form>
    </Modal>
  );
}

/**
 * Edit Event Modal Component
 */
function EditEventModal({ event, onClose, onSubmit }) {
  const [formData, setFormData] = useState({
    name: event?.name || '',
    location: event?.location || '',
    description: event?.description || '',
    start_date: event?.start_date ? new Date(event.start_date).toISOString().slice(0, 16) : '',
    end_date: event?.end_date ? new Date(event.end_date).toISOString().slice(0, 16) : '',
    max_capacity: event?.max_capacity || '',
    // Financial tracking fields
    artist_ticket_cost: event?.artist_ticket_cost || '',
    artists_pay_tickets: event?.artists_pay_tickets || false,
    expense_budget: event?.expense_budget || '',
    revenue_target: event?.revenue_target || ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  return (
    <Modal onClose={onClose} title="Edit Event">
      <form onSubmit={handleSubmit} className={styles.form}>
        <div className={styles.formGroup}>
          <label className={styles.label}>Event Name *</label>
          <input
            type="text"
            name="name"
            value={formData.name}
            onChange={handleChange}
            className={styles.input}
            required
            placeholder="e.g., Summer Festival 2024"
          />
        </div>

        <div className={styles.formGroup}>
          <label className={styles.label}>Location *</label>
          <input
            type="text"
            name="location"
            value={formData.location}
            onChange={handleChange}
            className={styles.input}
            required
            placeholder="e.g., Bondi Beach, Sydney"
          />
        </div>

        <div className={styles.formGroup}>
          <label className={styles.label}>Description</label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            className={styles.textarea}
            rows="3"
            placeholder="Event description..."
          />
        </div>

        <div className={styles.formRow}>
          <div className={styles.formGroup}>
            <label className={styles.label}>Start Date & Time *</label>
            <input
              type="datetime-local"
              name="start_date"
              value={formData.start_date}
              onChange={handleChange}
              className={styles.input}
              required
            />
          </div>

          <div className={styles.formGroup}>
            <label className={styles.label}>End Date & Time *</label>
            <input
              type="datetime-local"
              name="end_date"
              value={formData.end_date}
              onChange={handleChange}
              className={styles.input}
              required
            />
          </div>
        </div>

        <div className={styles.formGroup}>
          <label className={styles.label}>Maximum Capacity</label>
          <input
            type="number"
            name="max_capacity"
            value={formData.max_capacity}
            onChange={handleChange}
            className={styles.input}
            min="1"
            placeholder="Leave empty for unlimited"
          />
        </div>

        {/* Financial Tracking Section */}
        <div className={styles.sectionHeader}>
          <h3>💰 Financial Tracking</h3>
          <p>Configure expense budgets and artist ticket settings</p>
        </div>

        <div className={styles.formRow}>
          <div className={styles.formGroup}>
            <label className={styles.label}>Expense Budget</label>
            <input
              type="number"
              name="expense_budget"
              value={formData.expense_budget}
              onChange={handleChange}
              className={styles.input}
              min="0"
              step="0.01"
              placeholder="e.g., 5000.00"
            />
            <small className={styles.helpText}>
              Set a budget limit for event expenses
            </small>
          </div>

          <div className={styles.formGroup}>
            <label className={styles.label}>Revenue Target</label>
            <input
              type="number"
              name="revenue_target"
              value={formData.revenue_target}
              onChange={handleChange}
              className={styles.input}
              min="0"
              step="0.01"
              placeholder="e.g., 15000.00"
            />
            <small className={styles.helpText}>
              Expected revenue goal for this event
            </small>
          </div>
        </div>

        <div className={styles.formRow}>
          <div className={styles.formGroup}>
            <label className={styles.label}>Artist Festival Ticket Cost</label>
            <input
              type="number"
              name="artist_ticket_cost"
              value={formData.artist_ticket_cost}
              onChange={handleChange}
              className={styles.input}
              min="0"
              step="0.01"
              placeholder="e.g., 50.00"
            />
            <small className={styles.helpText}>
              Cost per artist for festival entry (if applicable)
            </small>
          </div>

          <div className={styles.formGroup}>
            <div className={styles.checkboxGroup}>
              <label className={styles.checkboxLabel}>
                <input
                  type="checkbox"
                  name="artists_pay_tickets"
                  checked={formData.artists_pay_tickets}
                  onChange={handleChange}
                  className={styles.checkbox}
                />
                <span className={styles.checkboxText}>
                  Artists pay their own festival tickets
                </span>
              </label>
              <small className={styles.helpText}>
                When enabled, ticket costs will be deducted from artist earnings
              </small>
            </div>
          </div>
        </div>

        <div className={styles.formActions}>
          <button
            type="button"
            onClick={onClose}
            className={styles.cancelButton}
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className={styles.submitButton}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Updating...' : 'Update Event'}
          </button>
        </div>
      </form>
    </Modal>
  );
}

// Enable static generation for dynamic routes
export async function getStaticPaths() {
  return {
    paths: [],
    fallback: 'blocking'
  }
}

export async function getStaticProps() {
  return {
    props: {}
  }
}
