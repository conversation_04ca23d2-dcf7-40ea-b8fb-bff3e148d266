/* styles/artists/ArtistProfileForm.module.css */
.profileForm {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  max-width: 700px;
  margin: auto;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.label {
  font-weight: bold;
  margin-bottom: 8px;
  color: #333;
}

.input,
.textarea {
  padding: 10px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s ease-in-out;
}

.input:focus,
.textarea:focus {
  outline: none;
  border-color: #6e8efb; /* Example focus color */
  box-shadow: 0 0 0 2px rgba(110, 142, 251, 0.2);
}

.inputError,
.textarea.inputError {
  border-color: #e74c3c; /* Example error color */
}

.errorMessage {
  color: #e74c3c;
  font-size: 0.875rem;
  margin-top: 5px;
}

.readOnlyField {
  padding: 10px 0;
  font-size: 1rem;
  color: #555;
}

.formActions {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

.submitButton {
  padding: 12px 25px;
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
}

.submitButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.submitButton:hover:not(:disabled) {
  background: linear-gradient(135deg, #5a78e4, #8f62cb);
}
