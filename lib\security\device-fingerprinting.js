/**
 * Device Fingerprinting Manager for Ocean Soul Sparkles
 * Provides device identification and suspicious login detection
 * 
 * Phase 9.1: Enhanced Authentication System
 */

import crypto from 'crypto'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

/**
 * Device Fingerprinting Manager Class
 * Handles device identification and tracking
 */
export class DeviceFingerprintManager {
  constructor() {
    this.fingerprintComponents = [
      'userAgent',
      'language',
      'platform',
      'screenResolution',
      'timezone',
      'cookieEnabled',
      'doNotTrack',
      'plugins',
      'canvas',
      'webgl'
    ]
  }

  /**
   * Generate device fingerprint from request
   * @param {Object} request - Request object with headers
   * @returns {Promise<Object>} - Fingerprint data
   */
  async generateFingerprint(request) {
    try {
      const components = {
        userAgent: request.headers['user-agent'] || 'unknown',
        acceptLanguage: request.headers['accept-language'] || 'unknown',
        acceptEncoding: request.headers['accept-encoding'] || 'unknown',
        accept: request.headers['accept'] || 'unknown',
        connection: request.headers['connection'] || 'unknown',
        upgradeInsecureRequests: request.headers['upgrade-insecure-requests'] || 'unknown',
        secFetchSite: request.headers['sec-fetch-site'] || 'unknown',
        secFetchMode: request.headers['sec-fetch-mode'] || 'unknown',
        secFetchDest: request.headers['sec-fetch-dest'] || 'unknown'
      }
      
      // Create fingerprint hash
      const fingerprintString = Object.values(components).join('|')
      const hash = crypto.createHash('sha256').update(fingerprintString).digest('hex')
      
      return {
        hash,
        components,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      console.error('Error generating fingerprint:', error)
      return {
        hash: 'unknown',
        components: {},
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Generate client-side fingerprint (for browser)
   * @returns {Promise<Object>} - Client fingerprint data
   */
  async generateClientFingerprint() {
    if (typeof window === 'undefined') {
      return { hash: 'server-side', components: {} }
    }

    try {
      const components = {
        userAgent: navigator.userAgent,
        language: navigator.language,
        languages: navigator.languages?.join(',') || '',
        platform: navigator.platform,
        cookieEnabled: navigator.cookieEnabled,
        doNotTrack: navigator.doNotTrack || 'unknown',
        hardwareConcurrency: navigator.hardwareConcurrency || 'unknown',
        maxTouchPoints: navigator.maxTouchPoints || 0,
        screenResolution: `${screen.width}x${screen.height}`,
        screenColorDepth: screen.colorDepth,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        timezoneOffset: new Date().getTimezoneOffset(),
        sessionStorage: typeof sessionStorage !== 'undefined',
        localStorage: typeof localStorage !== 'undefined',
        indexedDB: typeof indexedDB !== 'undefined',
        webgl: await this.getWebGLFingerprint(),
        canvas: await this.getCanvasFingerprint(),
        plugins: this.getPluginsFingerprint(),
        fonts: await this.getFontsFingerprint()
      }
      
      // Create fingerprint hash
      const fingerprintString = Object.values(components).join('|')
      const hash = crypto.createHash('sha256').update(fingerprintString).digest('hex')
      
      return {
        hash,
        components,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      console.error('Error generating client fingerprint:', error)
      return {
        hash: 'error',
        components: {},
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Get WebGL fingerprint
   * @returns {Promise<string>} - WebGL fingerprint
   */
  async getWebGLFingerprint() {
    if (typeof window === 'undefined') return 'server-side'
    
    try {
      const canvas = document.createElement('canvas')
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
      
      if (!gl) return 'no-webgl'
      
      const debugInfo = gl.getExtension('WEBGL_debug_renderer_info')
      const vendor = debugInfo ? gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL) : 'unknown'
      const renderer = debugInfo ? gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) : 'unknown'
      
      return `${vendor}|${renderer}`
    } catch (error) {
      return 'webgl-error'
    }
  }

  /**
   * Get Canvas fingerprint
   * @returns {Promise<string>} - Canvas fingerprint
   */
  async getCanvasFingerprint() {
    if (typeof window === 'undefined') return 'server-side'
    
    try {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      
      if (!ctx) return 'no-canvas'
      
      // Draw some text and shapes
      ctx.textBaseline = 'top'
      ctx.font = '14px Arial'
      ctx.fillText('Ocean Soul Sparkles 🌊✨', 2, 2)
      ctx.fillStyle = 'rgba(102, 204, 0, 0.7)'
      ctx.fillRect(100, 5, 80, 20)
      
      return canvas.toDataURL().slice(-50) // Last 50 chars for brevity
    } catch (error) {
      return 'canvas-error'
    }
  }

  /**
   * Get plugins fingerprint
   * @returns {string} - Plugins fingerprint
   */
  getPluginsFingerprint() {
    if (typeof window === 'undefined' || !navigator.plugins) return 'server-side'
    
    try {
      const plugins = Array.from(navigator.plugins)
        .map(plugin => plugin.name)
        .sort()
        .join(',')
      
      return plugins || 'no-plugins'
    } catch (error) {
      return 'plugins-error'
    }
  }

  /**
   * Get fonts fingerprint (basic detection)
   * @returns {Promise<string>} - Fonts fingerprint
   */
  async getFontsFingerprint() {
    if (typeof window === 'undefined') return 'server-side'
    
    try {
      const testFonts = [
        'Arial', 'Helvetica', 'Times New Roman', 'Courier New', 'Verdana',
        'Georgia', 'Palatino', 'Garamond', 'Bookman', 'Comic Sans MS',
        'Trebuchet MS', 'Arial Black', 'Impact'
      ]
      
      const availableFonts = []
      const testString = 'mmmmmmmmmmlli'
      const testSize = '72px'
      const h = document.getElementsByTagName('body')[0]
      
      // Create test elements
      const s = document.createElement('span')
      s.style.fontSize = testSize
      s.innerHTML = testString
      const defaultWidth = {}
      const defaultHeight = {}
      
      // Test with default fonts
      for (const font of ['monospace', 'sans-serif', 'serif']) {
        s.style.fontFamily = font
        h.appendChild(s)
        defaultWidth[font] = s.offsetWidth
        defaultHeight[font] = s.offsetHeight
        h.removeChild(s)
      }
      
      // Test each font
      for (const font of testFonts) {
        let detected = false
        for (const baseFont of ['monospace', 'sans-serif', 'serif']) {
          s.style.fontFamily = `${font}, ${baseFont}`
          h.appendChild(s)
          const matched = (s.offsetWidth !== defaultWidth[baseFont] || 
                          s.offsetHeight !== defaultHeight[baseFont])
          h.removeChild(s)
          detected = detected || matched
        }
        if (detected) {
          availableFonts.push(font)
        }
      }
      
      return availableFonts.join(',')
    } catch (error) {
      return 'fonts-error'
    }
  }

  /**
   * Update device information in database
   * @param {string} userId - User ID
   * @param {Object} fingerprint - Fingerprint data
   * @returns {Promise<Object>} - Updated device info
   */
  async updateDeviceInfo(userId, fingerprint) {
    try {
      // Check if device already exists
      const { data: existingDevice, error: fetchError } = await supabase
        .from('device_fingerprints')
        .select('*')
        .eq('user_id', userId)
        .eq('fingerprint_hash', fingerprint.hash)
        .single()
      
      if (fetchError && fetchError.code !== 'PGRST116') { // Not found is OK
        console.error('Error fetching existing device:', fetchError)
      }
      
      if (existingDevice) {
        // Update existing device
        const { data: updatedDevice, error: updateError } = await supabase
          .from('device_fingerprints')
          .update({
            last_seen: new Date().toISOString(),
            login_count: existingDevice.login_count + 1,
            device_info: fingerprint.components
          })
          .eq('id', existingDevice.id)
          .select()
          .single()
        
        if (updateError) {
          console.error('Error updating device:', updateError)
        }
        
        return updatedDevice || existingDevice
      } else {
        // Create new device record
        const { data: newDevice, error: insertError } = await supabase
          .from('device_fingerprints')
          .insert({
            user_id: userId,
            fingerprint_hash: fingerprint.hash,
            device_info: fingerprint.components,
            is_trusted: false, // New devices start as untrusted
            login_count: 1
          })
          .select()
          .single()
        
        if (insertError) {
          console.error('Error creating device record:', insertError)
          throw insertError
        }
        
        // Log new device
        await this.logSecurityEvent(userId, 'new_device_detected', 
          'New device detected for user', 'medium', {
            deviceId: newDevice.id,
            fingerprint: fingerprint.hash
          })
        
        return newDevice
      }
    } catch (error) {
      console.error('Error updating device info:', error)
      throw error
    }
  }

  /**
   * Calculate similarity between two fingerprints
   * @param {string} fingerprint1 - First fingerprint hash
   * @param {string} fingerprint2 - Second fingerprint hash
   * @returns {Promise<number>} - Similarity score (0-1)
   */
  async calculateSimilarity(fingerprint1, fingerprint2) {
    if (fingerprint1 === fingerprint2) {
      return 1.0
    }
    
    try {
      // Get device info for both fingerprints
      const { data: device1 } = await supabase
        .from('device_fingerprints')
        .select('device_info')
        .eq('fingerprint_hash', fingerprint1)
        .single()
      
      const { data: device2 } = await supabase
        .from('device_fingerprints')
        .select('device_info')
        .eq('fingerprint_hash', fingerprint2)
        .single()
      
      if (!device1 || !device2) {
        return 0.0
      }
      
      // Compare device info components
      const info1 = device1.device_info || {}
      const info2 = device2.device_info || {}
      
      const keys = new Set([...Object.keys(info1), ...Object.keys(info2)])
      let matches = 0
      let total = keys.size
      
      for (const key of keys) {
        if (info1[key] === info2[key]) {
          matches++
        }
      }
      
      return total > 0 ? matches / total : 0.0
    } catch (error) {
      console.error('Error calculating similarity:', error)
      return 0.0
    }
  }

  /**
   * Trust a device for a user
   * @param {string} userId - User ID
   * @param {string} deviceId - Device ID
   * @returns {Promise<boolean>} - Success status
   */
  async trustDevice(userId, deviceId) {
    try {
      const { error } = await supabase
        .from('device_fingerprints')
        .update({ is_trusted: true })
        .eq('user_id', userId)
        .eq('id', deviceId)
      
      if (error) {
        throw error
      }
      
      // Log device trust
      await this.logSecurityEvent(userId, 'device_trusted', 
        'User marked device as trusted', 'low', { deviceId })
      
      return true
    } catch (error) {
      console.error('Error trusting device:', error)
      return false
    }
  }

  /**
   * Untrust a device for a user
   * @param {string} userId - User ID
   * @param {string} deviceId - Device ID
   * @returns {Promise<boolean>} - Success status
   */
  async untrustDevice(userId, deviceId) {
    try {
      const { error } = await supabase
        .from('device_fingerprints')
        .update({ is_trusted: false })
        .eq('user_id', userId)
        .eq('id', deviceId)
      
      if (error) {
        throw error
      }
      
      // Log device untrust
      await this.logSecurityEvent(userId, 'device_untrusted', 
        'User marked device as untrusted', 'low', { deviceId })
      
      return true
    } catch (error) {
      console.error('Error untrusting device:', error)
      return false
    }
  }

  /**
   * Get user's devices
   * @param {string} userId - User ID
   * @returns {Promise<Array>} - User's devices
   */
  async getUserDevices(userId) {
    try {
      const { data: devices, error } = await supabase
        .from('device_fingerprints')
        .select('id, device_info, is_trusted, first_seen, last_seen, login_count')
        .eq('user_id', userId)
        .order('last_seen', { ascending: false })
      
      if (error) {
        throw error
      }
      
      return devices || []
    } catch (error) {
      console.error('Error getting user devices:', error)
      return []
    }
  }

  /**
   * Log security event
   * @param {string} userId - User ID
   * @param {string} eventType - Event type
   * @param {string} description - Event description
   * @param {string} severity - Event severity
   * @param {Object} additionalData - Additional event data
   */
  async logSecurityEvent(userId, eventType, description, severity, additionalData = {}) {
    try {
      await supabase
        .from('security_events')
        .insert({
          user_id: userId,
          event_type: eventType,
          event_description: description,
          severity,
          additional_data: additionalData
        })
    } catch (error) {
      console.error('Error logging security event:', error)
    }
  }
}

// Export singleton instance
export const deviceFingerprintManager = new DeviceFingerprintManager()
export default deviceFingerprintManager
