import nodemailer from 'nodemailer';

// DEVELOPER NOTE: Replace jsonTransport with your actual email provider configuration.
// Example for SMTP:
// const transporter = nodemailer.createTransport({
//   host: 'smtp.example.com',
//   port: 587,
//   secure: false, // true for 465, false for other ports
//   auth: {
//     user: process.env.SMTP_USER,
//     pass: process.env.SMTP_PASSWORD,
//   },
// });
// Example for SendGrid (using API key):
// const transporter = nodemailer.createTransport({
//   service: 'SendGrid', // or use host/port if self-managed
//   auth: {
//     user: 'apikey', // This is the literal string 'apikey'
//     pass: process.env.SENDGRID_API_KEY,
//   },
// });

const transporter = nodemailer.createTransport({
  jsonTransport: true, // Using JSON transport for testing/development
});

const generateOrderConfirmationHTML = (orderDetails, paymentDetails) => {
  const itemsHTML = orderDetails.items.map(item => `
    <tr>
      <td style="padding: 10px; border-bottom: 1px solid #eee;">${item.name}</td>
      <td style="padding: 10px; border-bottom: 1px solid #eee; text-align: center;">${item.quantity}</td>
      <td style="padding: 10px; border-bottom: 1px solid #eee; text-align: right;">$${parseFloat(item.price).toFixed(2)}</td>
      <td style="padding: 10px; border-bottom: 1px solid #eee; text-align: right;">$${(parseFloat(item.price) * item.quantity).toFixed(2)}</td>
    </tr>
  `).join('');

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-G">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Order Confirmation</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4; color: #333; }
        .container { max-width: 600px; margin: 20px auto; background-color: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        .header { text-align: center; padding-bottom: 20px; border-bottom: 1px solid #eee; }
        .header img { max-width: 150px; } /* Shop logo */
        .content h1 { font-size: 22px; color: #333; }
        .content p { line-height: 1.6; }
        .orderDetailsTable { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        .orderDetailsTable th { background-color: #f9f9f9; padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
        .orderDetailsTable td { padding: 10px; border-bottom: 1px solid #eee; }
        .totalsTable { width: 100%; margin-top: 20px; }
        .totalsTable td { padding: 5px 0; }
        .totalsTable .label { text-align: right; padding-right: 10px; font-weight: bold; }
        .footer { text-align: center; margin-top: 20px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #777; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <img src="https://wwww.oceansoulsparkles.com.au/images/logo.png" alt="Ocean Soul Sparkles Logo">
          <h1>Order Confirmed!</h1>
        </div>
        <div class="content">
          <p>Hi ${orderDetails.customerName || 'Valued Customer'},</p>
          <p>Thank you for your order with Ocean Soul Sparkles! We're excited to get your items ready for you.</p>
          <p><strong>Order ID:</strong> ${orderDetails.orderId}</p>
          <p><strong>Date of Order:</strong> ${new Date(orderDetails.orderDate || Date.now()).toLocaleDateString()}</p>

          <h2>Your Order Summary</h2>
          <table class="orderDetailsTable">
            <thead>
              <tr>
                <th>Item</th>
                <th style="text-align: center;">Quantity</th>
                <th style="text-align: right;">Price</th>
                <th style="text-align: right;">Total</th>
              </tr>
            </thead>
            <tbody>
              ${itemsHTML}
            </tbody>
          </table>

          <table class="totalsTable">
            <tr>
              <td class="label">Subtotal:</td>
              <td style="text-align: right;">$${parseFloat(orderDetails.subtotal).toFixed(2)}</td>
            </tr>
            <tr>
              <td class="label">Shipping:</td>
              <td style="text-align: right;">$${parseFloat(orderDetails.shipping).toFixed(2)}</td>
            </tr>
            ${orderDetails.taxes ? `<tr><td class="label">Taxes:</td><td style="text-align: right;">$${parseFloat(orderDetails.taxes).toFixed(2)}</td></tr>` : ''}
            <tr>
              <td class="label" style="font-size: 1.2em;">Total Amount Paid:</td>
              <td style="text-align: right; font-size: 1.2em; font-weight: bold;">$${parseFloat(orderDetails.total).toFixed(2)}</td>
            </tr>
          </table>

          <h2>Payment Details</h2>
          <p>Paid with: ${paymentDetails.method || 'Card'}${paymentDetails.last4 ? ` ending in ${paymentDetails.last4}` : ''}</p>
          ${paymentDetails.transactionId ? `<p>Transaction ID: ${paymentDetails.transactionId}</p>` : ''}
          <p>Payment Date: ${new Date(paymentDetails.paymentDate || Date.now()).toLocaleDateString()}</p>

          ${orderDetails.shippingAddress ? `
            <h2>Shipping Address</h2>
            <p>
              ${orderDetails.shippingAddress.name || orderDetails.customerName}<br>
              ${orderDetails.shippingAddress.address1 || ''}<br>
              ${orderDetails.shippingAddress.address2 ? orderDetails.shippingAddress.address2 + '<br>' : ''}
              ${orderDetails.shippingAddress.city || ''}, ${orderDetails.shippingAddress.state || ''} ${orderDetails.shippingAddress.postalCode || ''}<br>
              ${orderDetails.shippingAddress.country || ''}
            </p>
          ` : ''}

          <h2>Estimated Delivery</h2>
          <p>Your order will be processed shortly. Estimated delivery is typically 3-5 business days once shipped. We'll notify you when your order is on its way!</p>
        </div>
        <div class="footer">
          <p>&copy; ${new Date().getFullYear()} Ocean Soul Sparkles. All rights reserved.</p>
          <p><a href="https://wwww.oceansoulsparkles.com.au/policies">Shop Policies</a> | <a href="https://wwww.oceansoulsparkles.com.au/contact">Contact Us</a></p>
        </div>
      </div>
    </body>
    </html>
  `;
};


export const sendOrderConfirmationEmail = async (to, orderDetails, paymentDetails) => {
  if (!to) {
    console.error('No recipient email address provided for order confirmation.');
    return { success: false, error: 'No recipient email address.' };
  }
  if (!orderDetails || !paymentDetails) {
    console.error('Order details or payment details missing for email confirmation.');
    return { success: false, error: 'Order/Payment details missing.' };
  }

  const subject = `Your Ocean Soul Sparkles Order Confirmation #${orderDetails.orderId || 'N/A'}`;
  const htmlContent = generateOrderConfirmationHTML(orderDetails, paymentDetails);

  const mailOptions = {
    from: process.env.EMAIL_FROM || '"Ocean Soul Sparkles" <<EMAIL>>', // Sender address
    to: to, // List of receivers
    subject: subject, // Subject line
    html: htmlContent, // HTML body
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    console.log('Order confirmation email sent (JSON Transport): ' + JSON.stringify(info.message));
    // For real transport, info.messageId would be useful.
    // With jsonTransport, info.message contains the email content as a JSON string.
    return { success: true, messageId: info.messageId || null, message: info.message };
  } catch (error) {
    console.error('Error sending order confirmation email:', error);
    return { success: false, error: error.message };
  }
};

// --- Admin Order Notification ---

const generateAdminOrderNotificationHTML = (orderDetails, paymentDetails, squarePaymentResponse) => {
  const itemsHTML = orderDetails.items.map(item => `
    <tr>
      <td style="padding: 8px; border-bottom: 1px solid #eee;">${item.name} (ID: ${item.id || 'N/A'})</td>
      <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: center;">${item.quantity}</td>
      <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: right;">$${parseFloat(item.price).toFixed(2)}</td>
      <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: right;">$${(parseFloat(item.price) * item.quantity).toFixed(2)}</td>
    </tr>
  `).join('');

  const adminBaseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://wwww.oceansoulsparkles.com.au';
  const orderLink = `${adminBaseUrl}/admin/orders/${orderDetails.orderId || squarePaymentResponse?.orderId || 'view'}`;
  // const customerLink = `${adminBaseUrl}/admin/customers/${orderDetails.customerId || 'view'}`; // Assuming customerId is available

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>New Order Notification</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; color: #212529; }
        .container { max-width: 680px; margin: 20px auto; background-color: #ffffff; padding: 25px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.08); }
        .header { text-align: center; padding-bottom: 15px; border-bottom: 1px solid #dee2e6; margin-bottom: 20px; }
        .header h1 { font-size: 24px; color: #007bff; margin: 0; }
        .section { margin-bottom: 20px; }
        .section h2 { font-size: 18px; color: #495057; border-bottom: 1px solid #ced4da; padding-bottom: 8px; margin-top: 0;}
        .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom:15px;}
        .info-grid p, .section p { margin: 5px 0; line-height: 1.6; }
        .info-grid strong, .section strong { color: #343a40; }
        .orderDetailsTable { width: 100%; border-collapse: collapse; margin-bottom: 20px; font-size: 14px; }
        .orderDetailsTable th { background-color: #e9ecef; padding: 10px; text-align: left; border-bottom: 1px solid #adb5bd; }
        .orderDetailsTable td { padding: 8px; border-bottom: 1px solid #dee2e6; }
        .totalsTable { width: 100%; margin-top: 15px; font-size: 15px; }
        .totalsTable td { padding: 6px 0; }
        .totalsTable .label { text-align: right; padding-right: 15px; font-weight: bold; color: #495057; }
        .actions { text-align: center; margin-top: 25px; }
        .actions a { display: inline-block; background-color: #007bff; color: #fff; padding: 12px 20px; text-decoration: none; border-radius: 5px; font-weight: bold; margin: 5px; }
        .actions a:hover { background-color: #0056b3; }
        .footer { text-align: center; margin-top: 25px; padding-top: 15px; border-top: 1px solid #dee2e6; font-size: 12px; color: #6c757d; }
        .raw-details { background-color: #f1f3f5; padding: 10px; border-radius: 4px; font-size: 12px; white-space: pre-wrap; word-break: break-all; margin-top:10px; max-height: 200px; overflow-y: auto; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header"><h1>New Order Received!</h1></div>

        <div class="section">
          <h2>Order Overview</h2>
          <p><strong>Order ID:</strong> ${orderDetails.orderId || squarePaymentResponse?.orderId || 'N/A'}</p>
          <p><strong>Order Date:</strong> ${new Date(orderDetails.orderDate || Date.now()).toLocaleString()}</p>
        </div>

        <div class="section">
          <h2>Customer Information</h2>
          <div class="info-grid">
            <div>
              <p><strong>Name:</strong> ${orderDetails.customerName || 'N/A'}</p>
              <p><strong>Email:</strong> <a href="mailto:${orderDetails.customerEmail || ''}">${orderDetails.customerEmail || 'N/A'}</a></p>
              <p><strong>Phone:</strong> ${orderDetails.customerPhone || 'N/A'}</p>
            </div>
            ${orderDetails.shippingAddress ? `
            <div>
              <strong>Shipping Address:</strong><br>
              ${orderDetails.shippingAddress.name || orderDetails.customerName || ''}<br>
              ${orderDetails.shippingAddress.address1 || ''}<br>
              ${orderDetails.shippingAddress.address2 ? orderDetails.shippingAddress.address2 + '<br>' : ''}
              ${orderDetails.shippingAddress.city || ''}, ${orderDetails.shippingAddress.state || ''} ${orderDetails.shippingAddress.postalCode || ''}<br>
              ${orderDetails.shippingAddress.country || ''}
            </div>
            ` : '<div><p><strong>Shipping Address:</strong> Not Provided</p></div>'}
          </div>
        </div>

        <div class="section">
          <h2>Order Items</h2>
          <table class="orderDetailsTable">
            <thead><tr><th>Item (ID)</th><th style="text-align: center;">Qty</th><th style="text-align: right;">Price</th><th style="text-align: right;">Total</th></tr></thead>
            <tbody>${itemsHTML}</tbody>
          </table>
          <table class="totalsTable">
            <tr><td class="label">Subtotal:</td><td style="text-align: right;">$${parseFloat(orderDetails.subtotal).toFixed(2)}</td></tr>
            <tr><td class="label">Shipping:</td><td style="text-align: right;">$${parseFloat(orderDetails.shippingCost !== undefined ? orderDetails.shippingCost : orderDetails.shipping || 0).toFixed(2)}</td></tr>
            ${orderDetails.taxes ? `<tr><td class="label">Taxes:</td><td style="text-align: right;">$${parseFloat(orderDetails.taxes).toFixed(2)}</td></tr>` : ''}
            <tr><td class="label" style="font-size: 1.2em;">Grand Total:</td><td style="text-align: right; font-size: 1.2em; font-weight: bold;">$${parseFloat(orderDetails.total).toFixed(2)}</td></tr>
          </table>
        </div>

        <div class="section">
          <h2>Payment Details</h2>
          <div class="info-grid">
            <div>
              <p><strong>Method:</strong> ${paymentDetails.method || 'Card'}${paymentDetails.last4 ? ` ending in ${paymentDetails.last4}` : ''}</p>
              <p><strong>Transaction ID:</strong> ${paymentDetails.transactionId || 'N/A'}</p>
            </div>
            <div>
              <p><strong>Payment Status:</strong> ${squarePaymentResponse?.status || paymentDetails.status || 'N/A'}</p>
              <p><strong>Amount Paid:</strong> $${parseFloat(orderDetails.total).toFixed(2)}</p>
            </div>
          </div>
          ${squarePaymentResponse ? `
            <div>
              <strong>Square Payment Details:</strong>
              <div class="raw-details">${JSON.stringify(squarePaymentResponse, (key, value) => typeof value === 'bigint' ? value.toString() : value, 2)}</div>
            </div>` : ''}
        </div>

        <div class="actions">
          <a href="${orderLink}" target="_blank">View Order in Admin</a>
          ${orderDetails.customerId ? `<a href="${adminBaseUrl}/admin/customers/${orderDetails.customerId}" target="_blank">View Customer Profile</a>` : ''}
        </div>

        <div class="footer">
          <p>This is an automated notification. Please do not reply directly to this email.</p>
        </div>
      </div>
    </body>
    </html>
  `;
};

export const sendAdminOrderNotificationEmail = async (orderDetails, paymentDetails, squarePaymentResponse) => {
  const adminEmail = '<EMAIL>'; // Hardcoded admin email

  if (!orderDetails || !paymentDetails || !squarePaymentResponse) {
    console.error('Missing details for admin order notification.');
    return { success: false, error: 'Missing order/payment/Square details for admin email.' };
  }

  const subject = `🎉 New Order Received #${orderDetails.orderId || squarePaymentResponse?.orderId || 'N/A'} - Ocean Soul Sparkles`;
  const htmlContent = generateAdminOrderNotificationHTML(orderDetails, paymentDetails, squarePaymentResponse);

  const mailOptions = {
    from: process.env.EMAIL_FROM || '"Ocean Soul Sparkles Admin" <<EMAIL>>',
    to: adminEmail,
    subject: subject,
    html: htmlContent,
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    console.log('Admin order notification email sent (JSON Transport): ' + JSON.stringify(info.message));
    return { success: true, messageId: info.messageId || null, message: info.message };
  } catch (error) {
    console.error('Error sending admin order notification email:', error);
    return { success: false, error: error.message };
  }
};
