/* Real-time Test Page Styles */

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
}

.header {
  margin-bottom: 32px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.header h1 {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 2rem;
  font-weight: 700;
}

.header p {
  margin: 0;
  color: #6b7280;
  font-size: 1.125rem;
}

.section {
  margin-bottom: 32px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.section h2 {
  margin: 0 0 20px 0;
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 600;
}

.statusGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.statusCard {
  padding: 20px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.statusCard h3 {
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 1.125rem;
  font-weight: 600;
}

.statusDetails {
  margin-top: 16px;
}

.statusDetails p {
  margin: 8px 0;
  font-size: 0.875rem;
  color: #4b5563;
}

.controls {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.button {
  padding: 10px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.button:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
}

.button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.dataCard {
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.dataDisplay {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.75rem;
  line-height: 1.4;
  color: #374151;
  white-space: pre-wrap;
  word-break: break-word;
}

.logsContainer {
  max-height: 400px;
  overflow-y: auto;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 16px;
}

.logEntry {
  margin-bottom: 16px;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.logTimestamp {
  display: inline-block;
  padding: 2px 6px;
  background: #e5e7eb;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  color: #374151;
  margin-right: 8px;
}

.logEvent {
  display: inline-block;
  padding: 2px 6px;
  background: #dbeafe;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  color: #1e40af;
  margin-right: 8px;
}

.logData {
  margin: 8px 0 0 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.75rem;
  line-height: 1.4;
  color: #4b5563;
  background: #f3f4f6;
  padding: 8px;
  border-radius: 4px;
  white-space: pre-wrap;
  word-break: break-word;
}

.messagesContainer {
  max-height: 300px;
  overflow-y: auto;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 16px;
}

.messageEntry {
  margin-bottom: 16px;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.messageType {
  display: inline-block;
  padding: 2px 6px;
  background: #dcfce7;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  color: #166534;
  margin-bottom: 8px;
}

.messageData {
  margin: 8px 0 0 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.75rem;
  line-height: 1.4;
  color: #4b5563;
  background: #f3f4f6;
  padding: 8px;
  border-radius: 4px;
  white-space: pre-wrap;
  word-break: break-word;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .statusGrid {
    grid-template-columns: 1fr;
  }

  .controls {
    flex-direction: column;
  }

  .button {
    width: 100%;
    justify-content: center;
  }

  .dataCard,
  .logsContainer,
  .messagesContainer {
    max-height: 250px;
  }
}

/* Scrollbar styling */
.logsContainer::-webkit-scrollbar,
.messagesContainer::-webkit-scrollbar,
.dataCard::-webkit-scrollbar {
  width: 6px;
}

.logsContainer::-webkit-scrollbar-track,
.messagesContainer::-webkit-scrollbar-track,
.dataCard::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.logsContainer::-webkit-scrollbar-thumb,
.messagesContainer::-webkit-scrollbar-thumb,
.dataCard::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.logsContainer::-webkit-scrollbar-thumb:hover,
.messagesContainer::-webkit-scrollbar-thumb:hover,
.dataCard::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
