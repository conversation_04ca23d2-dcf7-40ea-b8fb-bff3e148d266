/**
 * Authentication Helpers
 *
 * This module provides simplified authentication functions using the unified Supabase client.
 * It provides a consistent interface for authentication across the application.
 */

import supabase, { getCurrentUser as getUser, auth } from './supabase'
import { handleAuthError, recoverFromAuthError } from './auth-error-handling'

/**
 * Sign in with email and password
 *
 * @param {string} email - User email
 * @param {string} password - User password
 * @returns {Promise<Object>} Auth data or error
 */
export async function signIn(email, password) {
  try {
    // First try regular sign-in
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (error) {
      console.error('Sign in error:', error);

      // Try to recover from error if possible
      if (await recoverFromAuthError(error)) {
        // Retry the sign-in after recovery
        const retryResult = await supabase.auth.signInWithPassword({
          email,
          password
        });

        if (retryResult.error) {
          return { data: null, error: handleAuthError(retryResult.error, 'signIn') };
        }

        // Get user role after successful sign in
        const { user, role, error: userError } = await getCurrentUser();
        console.log('[signIn] User details after retry from getCurrentUser:', { user, role, userError });

        if (userError) {
          console.error('[signIn] Error fetching user details after retry:', userError);
          // Decide if this should be a critical failure or if partial data is acceptable
          return { data: null, error: handleAuthError(userError, 'signIn_getUser_retry') };
        }

        // Role check for staff/admin after retry
        const expectedStaffRoles = ['artist', 'braider', 'admin', 'dev'];
        if (user && !expectedStaffRoles.includes(role) && role !== null) {
            console.warn(`[signIn] Unexpected role for staff login after retry: ${role} for user ${user.email}. Expected one of ${expectedStaffRoles.join(', ')}.`);
            // Potentially return an error or specific status if role is critical for staff area access
        } else if (user && role === null) {
            console.warn(`[signIn] Role is null for user ${user.email} after retry. This might indicate an issue with role assignment for an activated account.`);
        }

        return {
          data: {
            ...retryResult.data, // Original session data from Supabase
            user, // User object from getCurrentUser
            role  // Role from getCurrentUser
          },
          error: null
        };
      }

      return { data: null, error: handleAuthError(error, 'signIn') };
    }

    // Get user role after successful sign in
    const { user, role, error: userError } = await getCurrentUser();
    console.log('[signIn] User details from getCurrentUser:', { user, role, userError });

    if (userError) {
      console.error('[signIn] Error fetching user details:', userError);
      // Based on requirements, decide if this is a critical failure.
      // For now, returning the error to be handled by the caller.
      return { data: null, error: handleAuthError(userError, 'signIn_getUser') };
    }

    // Role check for staff/admin
    // These are the roles expected to be able to log into a staff/admin portal
    const expectedStaffRoles = ['artist', 'braider', 'admin', 'dev'];
    if (user && !expectedStaffRoles.includes(role) && role !== null) {
        // This condition means the user has a role, but it's not one of the expected staff roles (e.g., 'user')
        console.warn(`[signIn] Unexpected role for staff login: ${role} for user ${user.email}. Expected one of ${expectedStaffRoles.join(', ')}.`);
        // Depending on strictness, you might want to return an error here:
        // return { data: null, error: { message: `User role (${role}) is not authorized for staff login.` } };
    } else if (user && role === null) {
        // This condition means getCurrentUser returned a null role, possibly for an activated user pending role propagation
        console.warn(`[signIn] Role is null for user ${user.email}. This may be an activated account pending role assignment. Access might be restricted.`);
        // Depending on how strictly this should be handled, you could return an error or allow login with restricted access.
        // For now, we'll log it and proceed, allowing the UI to handle a null role appropriately.
    }

    // If data.user (from signInWithPassword) and user (from getCurrentUser) are redundant,
    // ensure consistency or choose one as the source of truth.
    // Here, we prioritize the user object from getCurrentUser as it includes the role.
    return {
      data: {
        session: data.session, // Keep session from signInWithPassword
        user, // User object from getCurrentUser (includes metadata)
        role  // Role from getCurrentUser
      },
      error: null
    };
  } catch (error) {
    console.error('[signIn] Outer catch error:', error);
    return { data: null, error: handleAuthError(error, 'signIn_outer') };
  }
}

/**
 * Sign out the current user
 *
 * @returns {Promise<Object>} Success or error
 */
export async function signOut() {
  try {
    const { error } = await auth.signOut();
    if (error) {
      return { error: handleAuthError(error, 'signOut') };
    }
    return { error: null };
  } catch (error) {
    console.error('Sign out error:', error);
    return { error: handleAuthError(error, 'signOut') };
  }
}

/**
 * Get the current authenticated user with role information
 *
 * @returns {Promise<Object>} User data with role or error
 */
export async function getCurrentUser() {
  try {
    const { user, role } = await getUser()

    return {
      user,
      role,
      error: null
    }
  } catch (error) {
    console.error('Authentication error in getCurrentUser:', error)
    return {
      user: null,
      role: null,
      error
    }
  }
}

/**
 * Check if the current user has admin role
 *
 * @returns {Promise<boolean>} True if user is an admin
 */
export async function isAdmin() {
  const { user, role, error } = await getCurrentUser()

  if (error || !user) {
    return false
  }

  return role === 'admin'
}

/**
 * Check if the current user has staff role or higher
 *
 * @returns {Promise<boolean>} True if user is staff or admin
 */
export async function isStaffOrAdmin() {
  const { user, role, error } = await getCurrentUser()

  if (error || !user) {
    return false
  }

  return role === 'staff' || role === 'admin'
}

/**
 * Reset password for email
 *
 * @param {string} email - User email
 * @returns {Promise<Object>} Success or error
 */
export async function resetPassword(email) {
  try {
    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${typeof window !== 'undefined' ? window.location.origin : ''}/admin/reset-password`
    })

    return { data, error }
  } catch (error) {
    console.error('Error resetting password:', error)
    return { data: null, error }
  }
}

/**
 * Manually set a user role
 *
 * @param {string} userId - User ID
 * @param {string} role - Role to set (default: 'admin')
 * @returns {Promise<Object>} Success or error
 */
export async function setUserRole(userId, role = 'admin') {
  try {
    // Check if role already exists
    const { data: existingRole, error: checkError } = await supabase
      .from('user_roles')
      .select('*')
      .eq('id', userId)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 is "not found" error
      console.error('Error checking existing role:', checkError);
      return { success: false, error: checkError };
    }

    if (existingRole) {
      // Update existing role
      const { error } = await supabase
        .from('user_roles')
        .update({ role })
        .eq('id', userId);

      return { success: !error, error };
    } else {
      // Insert new role
      const { error } = await supabase
        .from('user_roles')
        .insert([{ id: userId, role }]);

      return { success: !error, error };
    }
  } catch (error) {
    console.error('Error setting user role:', error);
    return { success: false, error };
  }
}

/**
 * Update user password
 *
 * @param {string} password - New password
 * @returns {Promise<Object>} Success or error
 */
export async function updatePassword(password) {
  try {
    const { data, error } = await supabase.auth.updateUser({
      password
    })

    return { data, error }
  } catch (error) {
    console.error('Error updating password:', error)
    return { data: null, error }
  }
}

/**
 * Create a new user (admin only)
 *
 * @param {string} email - User email
 * @param {string} password - User password
 * @param {string} role - User role (default: 'staff')
 * @returns {Promise<Object>} Success or error
 */
export async function createUser(email, password, role = 'staff') {
  const isAdminUser = await isAdmin()

  if (!isAdminUser) {
    return {
      error: { message: 'Only administrators can create new users' }
    }
  }

  try {
    // Create the user
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: `${window.location.origin}/auth/confirm`
      }
    })

    if (error) {
      return { error }
    }

    // Add the user role
    const { error: roleError } = await supabase
      .from('user_roles')
      .insert([
        { id: data.user.id, role }
      ])

    if (roleError) {
      return { error: roleError }
    }

    return { data, error: null }
  } catch (error) {
    console.error('Error creating user:', error)
    return { data: null, error }
  }
}

/**
 * Update a user's role (admin only)
 *
 * @param {string} userId - User ID
 * @param {string} role - New role
 * @returns {Promise<Object>} Success or error
 */
export async function updateUserRole(userId, role) {
  const isAdminUser = await isAdmin()

  if (!isAdminUser) {
    return {
      error: { message: 'Only administrators can update user roles' }
    }
  }

  try {
    const { data, error } = await supabase
      .from('user_roles')
      .update({ role })
      .eq('id', userId)

    return { data, error }
  } catch (error) {
    console.error('Error updating user role:', error)
    return { data: null, error }
  }
}

/**
 * Get all users (admin only)
 *
 * @returns {Promise<Object>} Users or error
 */
export async function getUsers() {
  const isAdminUser = await isAdmin()

  if (!isAdminUser) {
    return {
      error: { message: 'Only administrators can view all users' }
    }
  }

  try {
    const { data: roleData, error: roleError } = await supabase
      .from('user_roles')
      .select('*')

    if (roleError) {
      return { error: roleError }
    }

    // For client-side usage, we'll return just the role data
    // In server-side code, we would use getAdminClient() to get full user details
    return {
      users: roleData.map(r => ({
        id: r.id,
        role: r.role,
        email: '<EMAIL>', // Placeholder - ideally fetch from auth.users table
        created_at: new Date().toISOString() // Placeholder
      })),
      error: null
    }
  } catch (error) {
    console.error('Error getting users:', error)
    return { users: [], error }
  }
}

// Customer Authentication Functions

/**
 * Validates that the request comes from an admin user
 * Used for securing admin API routes
 *
 * @param {Object} req - HTTP request object
 * @returns {Promise<boolean>} True if user is an admin
 */
export async function validateAdminRole(req) {
  try {
    // Extract token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return false;
    }

    const token = authHeader.substring(7);

    // Import getAdminClient only on server-side
    const { getAdminClient } = await import('./supabase');
    const adminClient = getAdminClient();

    // Verify token
    const { data, error } = await adminClient.auth.getUser(token);

    if (error || !data.user) {
      return false;
    }

    // Check user role
    const { data: roleData, error: roleError } = await adminClient
      .from('user_roles')
      .select('role')
      .eq('id', data.user.id)
      .single();

    if (roleError || !roleData) {
      return false;
    }

    // Updated for 5-tier role system
    const adminRoles = ['dev', 'admin'];
    return adminRoles.includes(roleData.role);
  } catch (err) {
    console.error('Error validating admin role:', err);
    return false;
  }
}

/**
 * Validates that the request comes from an authenticated user
 * Used for securing API routes
 *
 * @param {Object} req - HTTP request object
 * @returns {Promise<boolean>} True if user is authenticated
 */
export async function isAuthenticated(req) {
  try {
    // Extract token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return false;
    }

    const token = authHeader.substring(7);

    // Import getAdminClient only on server-side
    const { getAdminClient } = await import('./supabase');
    const adminClient = getAdminClient();

    // Verify token
    const { data, error } = await adminClient.auth.getUser(token);

    return !error && !!data.user;
  } catch (err) {
    console.error('Error validating authentication:', err);
    return false;
  }
}

/**
 * Register a new customer with authentication
 *
 * @param {Object} customerData - Customer data
 * @param {string} password - Customer password
 * @returns {Promise<Object>} Success or error
 */
export async function registerCustomer(customerData, password) {
  try {
    const response = await fetch('/api/public/auth', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...customerData,
        password,
        action: 'signup'
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Registration failed');
    }

    return { data, error: null };
  } catch (error) {
    console.error('Customer registration error:', error);
    return { data: null, error };
  }
}

/**
 * Sign in a customer
 *
 * @param {string} email - Customer email
 * @param {string} password - Customer password
 * @returns {Promise<Object>} Success or error
 */
export async function signInCustomer(email, password) {
  try {
    // First try direct Supabase authentication
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (authError) {
      // Fall back to API endpoint for custom logic
      const response = await fetch('/api/public/auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          password,
          action: 'signin'
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Sign in failed');
      }

      return { data, error: null };
    }

    // If direct auth succeeded, get customer data
    try {
      const { data: customerData, error: customerError } = await supabase
        .from('customers')
        .select('*')
        .eq('email', email)
        .single();

      if (customerError) {
        console.warn('Error fetching customer after authentication:', customerError);
      }

      return {
        data: {
          session: authData.session,
          customer: customerData || null
        },
        error: null
      };
    } catch (customerError) {
      console.error('Error fetching customer data:', customerError);
      return {
        data: {
          session: authData.session,
          customer: null
        },
        error: null
      };
    }
  } catch (error) {
    console.error('Customer sign in error:', error);
    return { data: null, error };
  }
}

/**
 * Create a guest customer (no authentication)
 *
 * @param {Object} customerData - Customer data
 * @returns {Promise<Object>} Success or error
 */
export async function createGuestCustomer(customerData) {
  try {
    const response = await fetch('/api/public/customers', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...customerData,
        isGuest: true
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Guest customer creation failed');
    }

    return { data, error: null };
  } catch (error) {
    console.error('Guest customer creation error:', error);
    return { data: null, error };
  }
}

/**
 * Update customer profile
 *
 * @param {string} id - Customer ID
 * @param {Object} updateData - Data to update
 * @returns {Promise<Object>} Success or error
 */
export async function updateCustomerProfile(id, updateData) {
  try {
    // First try direct Supabase update if authenticated
    const session = await supabase.auth.getSession();

    if (session?.data?.session) {
      const { data, error } = await supabase
        .from('customers')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (!error) {
        return { data, error: null };
      }

      // If direct update failed, fall back to API
      console.warn('Direct customer update failed, falling back to API:', error);
    }

    // Fall back to API endpoint
    const response = await fetch('/api/public/customers', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        id,
        ...updateData
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Profile update failed');
    }

    return { data, error: null };
  } catch (error) {
    console.error('Customer profile update error:', error);
    return { data: null, error };
  }
}
