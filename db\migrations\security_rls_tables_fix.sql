-- =============================================
-- SUPABASE RLS TABLES FIX
-- Ocean Soul Sparkles Database Security Remediation - Part 2
-- =============================================

-- This script fixes remaining SECURITY DEFINER views and enables RLS on all public tables
-- Completes the security remediation for production deployment

-- =============================================
-- FIX REMAINING SECURITY DEFINER VIEWS (15-18)
-- =============================================

-- FIX VIEW 15: NOTIFICATION_STATISTICS
DROP VIEW IF EXISTS public.notification_statistics CASCADE;
CREATE VIEW public.notification_statistics
WITH (security_invoker = true) AS
SELECT 
  template_id,
  COUNT(*) as total_sent,
  COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered_count,
  COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count,
  AVG(CASE WHEN delivered_at IS NOT NULL THEN EXTRACT(EPOCH FROM (delivered_at - created_at)) END) as avg_delivery_time_seconds
FROM notifications
WHERE public.is_admin_or_staff()
GROUP BY template_id;

REVOKE ALL ON public.notification_statistics FROM anon, public;
GRANT SELECT ON public.notification_statistics TO authenticated;

-- FIX VIEW 16: EMERGENCY_NOTIFICATION_STATISTICS
DROP VIEW IF EXISTS public.emergency_notification_statistics CASCADE;
CREATE VIEW public.emergency_notification_statistics
WITH (security_invoker = true) AS
SELECT 
  DATE_TRUNC('day', created_at) as date,
  COUNT(*) as total_emergency_notifications,
  COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered_count,
  AVG(EXTRACT(EPOCH FROM (delivered_at - created_at))) as avg_delivery_time_seconds
FROM notifications
WHERE public.is_admin_or_staff()
  AND priority = 'high'
GROUP BY DATE_TRUNC('day', created_at)
ORDER BY date DESC;

REVOKE ALL ON public.emergency_notification_statistics FROM anon, public;
GRANT SELECT ON public.emergency_notification_statistics TO authenticated;

-- FIX VIEW 17: PWA_SYNC_ANALYTICS
DROP VIEW IF EXISTS public.pwa_sync_analytics CASCADE;
CREATE VIEW public.pwa_sync_analytics
WITH (security_invoker = true) AS
SELECT 
  item_type,
  status,
  COUNT(*) as count,
  AVG(retry_count) as avg_retries,
  MIN(created_at) as oldest_item,
  MAX(created_at) as newest_item
FROM pwa_sync_queue
WHERE public.is_admin_or_staff()
GROUP BY item_type, status;

REVOKE ALL ON public.pwa_sync_analytics FROM anon, public;
GRANT SELECT ON public.pwa_sync_analytics TO authenticated;

-- FIX VIEW 18: PHOTO_ANALYTICS
DROP VIEW IF EXISTS public.photo_analytics CASCADE;
CREATE VIEW public.photo_analytics
WITH (security_invoker = true) AS
SELECT 
  type,
  COUNT(*) as total_photos,
  SUM(file_size) as total_size_bytes,
  AVG(file_size) as avg_size_bytes,
  MIN(created_at) as first_photo,
  MAX(created_at) as latest_photo,
  COUNT(DISTINCT booking_id) as unique_bookings
FROM photos
WHERE public.is_admin_or_staff()
GROUP BY type;

REVOKE ALL ON public.photo_analytics FROM anon, public;
GRANT SELECT ON public.photo_analytics TO authenticated;

-- =============================================
-- ENABLE RLS ON ALL PUBLIC SCHEMA TABLES
-- =============================================

-- Get list of all tables in public schema and enable RLS
DO $$
DECLARE
    table_record RECORD;
    policy_count INTEGER;
BEGIN
    -- Loop through all tables in public schema
    FOR table_record IN 
        SELECT tablename 
        FROM pg_tables 
        WHERE schemaname = 'public'
        AND tablename NOT LIKE 'pg_%'
        AND tablename NOT LIKE 'sql_%'
    LOOP
        -- Check if RLS is already enabled
        EXECUTE format('SELECT COUNT(*) FROM pg_class WHERE relname = %L AND relrowsecurity = true', table_record.tablename) INTO policy_count;
        
        IF policy_count = 0 THEN
            -- Enable RLS on table
            EXECUTE format('ALTER TABLE public.%I ENABLE ROW LEVEL SECURITY', table_record.tablename);
            RAISE NOTICE 'Enabled RLS on table: %', table_record.tablename;
        ELSE
            RAISE NOTICE 'RLS already enabled on table: %', table_record.tablename;
        END IF;
    END LOOP;
END $$;

-- =============================================
-- CREATE DEFAULT RLS POLICIES FOR TABLES WITHOUT POLICIES
-- =============================================

-- Function to create default policies for tables that don't have any
CREATE OR REPLACE FUNCTION public.create_default_rls_policies()
RETURNS void AS $$
DECLARE
    table_record RECORD;
    policy_count INTEGER;
BEGIN
    -- Loop through all tables in public schema
    FOR table_record IN 
        SELECT tablename 
        FROM pg_tables 
        WHERE schemaname = 'public'
        AND tablename NOT LIKE 'pg_%'
        AND tablename NOT LIKE 'sql_%'
        AND tablename NOT IN (
            'user_roles', 'artist_profiles', 'bookings', 'customers', 
            'services', 'products', 'quick_events', 'notifications'
        ) -- Skip tables that already have specific policies
    LOOP
        -- Check if table has any policies
        SELECT COUNT(*) INTO policy_count
        FROM pg_policies 
        WHERE tablename = table_record.tablename 
        AND schemaname = 'public';
        
        IF policy_count = 0 THEN
            -- Create default admin/dev only policy
            EXECUTE format('
                CREATE POLICY "Admin and dev access only" ON public.%I
                FOR ALL USING (
                    EXISTS (
                        SELECT 1 FROM public.user_roles 
                        WHERE id = auth.uid() 
                        AND role IN (''dev'', ''admin'')
                    )
                )
            ', table_record.tablename);
            
            RAISE NOTICE 'Created default policy for table: %', table_record.tablename;
        END IF;
    END LOOP;
END $$;

-- Execute the function to create default policies
SELECT public.create_default_rls_policies();

-- =============================================
-- SPECIFIC TABLE POLICIES FOR CRITICAL TABLES
-- =============================================

-- Ensure critical tables have proper policies
-- BOOKING_STATUS_HISTORY
DROP POLICY IF EXISTS "Admin and dev access only" ON public.booking_status_history;
CREATE POLICY "Admin staff can manage booking status history" ON public.booking_status_history
  FOR ALL USING (public.is_admin_or_staff());

-- LOCATIONS
DROP POLICY IF EXISTS "Admin and dev access only" ON public.locations;
CREATE POLICY "Admin staff can manage locations" ON public.locations
  FOR ALL USING (public.is_admin_or_staff());

-- CUSTOMER_TAG_ASSIGNMENTS
DROP POLICY IF EXISTS "Admin and dev access only" ON public.customer_tag_assignments;
CREATE POLICY "Admin staff can manage customer tags" ON public.customer_tag_assignments
  FOR ALL USING (public.is_admin_or_staff());

-- CUSTOMER_TAGS
DROP POLICY IF EXISTS "Admin and dev access only" ON public.customer_tags;
CREATE POLICY "Admin staff can manage customer tag definitions" ON public.customer_tags
  FOR ALL USING (public.is_admin_or_staff());

-- NOTIFICATION_TEMPLATES
DROP POLICY IF EXISTS "Admin and dev access only" ON public.notification_templates;
CREATE POLICY "Admin staff can manage notification templates" ON public.notification_templates
  FOR ALL USING (public.is_admin_or_staff());

-- =============================================
-- VERIFICATION QUERIES
-- =============================================

-- Create verification function to check security status
CREATE OR REPLACE FUNCTION public.verify_security_status()
RETURNS TABLE(
  table_name text,
  rls_enabled boolean,
  policy_count bigint,
  anon_access boolean,
  security_status text
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    t.tablename::text,
    t.rowsecurity as rls_enabled,
    COALESCE(p.policy_count, 0) as policy_count,
    COALESCE(g.has_anon_access, false) as anon_access,
    CASE 
      WHEN t.rowsecurity AND COALESCE(p.policy_count, 0) > 0 AND NOT COALESCE(g.has_anon_access, false) THEN 'SECURE'
      WHEN NOT t.rowsecurity THEN 'RLS_DISABLED'
      WHEN COALESCE(p.policy_count, 0) = 0 THEN 'NO_POLICIES'
      WHEN COALESCE(g.has_anon_access, false) THEN 'ANON_ACCESS'
      ELSE 'NEEDS_REVIEW'
    END as security_status
  FROM pg_tables t
  LEFT JOIN (
    SELECT tablename, COUNT(*) as policy_count
    FROM pg_policies 
    WHERE schemaname = 'public'
    GROUP BY tablename
  ) p ON t.tablename = p.tablename
  LEFT JOIN (
    SELECT table_name, true as has_anon_access
    FROM information_schema.role_table_grants 
    WHERE grantee = 'anon' 
    AND privilege_type = 'SELECT'
    AND table_schema = 'public'
  ) g ON t.tablename = g.table_name
  WHERE t.schemaname = 'public'
  AND t.tablename NOT LIKE 'pg_%'
  AND t.tablename NOT LIKE 'sql_%'
  ORDER BY security_status DESC, t.tablename;
END $$;

-- =============================================
-- SECURITY REMEDIATION COMPLETE
-- =============================================

-- Run verification
SELECT 'SECURITY VERIFICATION RESULTS:' as status;
SELECT * FROM public.verify_security_status();

-- Summary message
SELECT 
  'SECURITY REMEDIATION COMPLETE' as status,
  'All 18 SECURITY DEFINER views converted to SECURITY INVOKER' as views_fixed,
  'RLS enabled on all public schema tables' as rls_status,
  'Default policies created for tables without policies' as policies_status,
  'Anonymous access revoked from sensitive views' as access_control;
