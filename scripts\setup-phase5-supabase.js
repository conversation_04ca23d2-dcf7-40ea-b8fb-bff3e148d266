/**
 * Phase 5 PWA Supabase Setup Script
 * Ocean Soul Sparkles - Automated setup for PWA database requirements
 */

import { createClient } from '@supabase/supabase-js'
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Load environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables')
  console.error('Please ensure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function setupPhase5Database() {
  console.log('🚀 Starting Phase 5 PWA Supabase setup...\n')

  try {
    // 1. Read and execute migration SQL
    console.log('📄 Reading migration file...')
    const migrationPath = path.join(__dirname, '..', 'db', 'migrations', 'phase5_pwa_migration.sql')
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8')
    
    console.log('🔧 Executing database migration...')
    const { error: migrationError } = await supabase.rpc('exec_sql', {
      sql: migrationSQL
    })

    if (migrationError) {
      console.error('❌ Migration failed:', migrationError)
      // Try executing in smaller chunks
      await executeMigrationInChunks(migrationSQL)
    } else {
      console.log('✅ Database migration completed successfully')
    }

    // 2. Create storage bucket
    console.log('\n📁 Setting up storage bucket...')
    await setupStorageBucket()

    // 3. Verify setup
    console.log('\n🔍 Verifying setup...')
    await verifySetup()

    console.log('\n🎉 Phase 5 PWA setup completed successfully!')
    console.log('\nNext steps:')
    console.log('1. Test photo upload functionality')
    console.log('2. Verify PWA installation works')
    console.log('3. Test offline functionality')
    console.log('4. Check storage policies in Supabase Dashboard')

  } catch (error) {
    console.error('❌ Setup failed:', error)
    process.exit(1)
  }
}

async function executeMigrationInChunks(sql) {
  console.log('🔄 Executing migration in smaller chunks...')
  
  // Split SQL into individual statements
  const statements = sql
    .split(';')
    .map(stmt => stmt.trim())
    .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))

  for (let i = 0; i < statements.length; i++) {
    const statement = statements[i]
    if (statement.includes('CREATE TABLE') || statement.includes('ALTER TABLE') || statement.includes('CREATE INDEX')) {
      try {
        console.log(`  Executing statement ${i + 1}/${statements.length}...`)
        const { error } = await supabase.rpc('exec_sql', { sql: statement + ';' })
        if (error && !error.message.includes('already exists')) {
          console.warn(`  ⚠️  Warning: ${error.message}`)
        }
      } catch (err) {
        console.warn(`  ⚠️  Warning executing statement: ${err.message}`)
      }
    }
  }
}

async function setupStorageBucket() {
  try {
    // Check if bucket exists
    const { data: buckets, error: listError } = await supabase.storage.listBuckets()
    
    if (listError) {
      console.error('❌ Failed to list buckets:', listError)
      return
    }

    const bucketExists = buckets.some(bucket => bucket.name === 'ocean-soul-sparkles')
    
    if (!bucketExists) {
      console.log('  Creating storage bucket...')
      const { error: createError } = await supabase.storage.createBucket('ocean-soul-sparkles', {
        public: true,
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp'],
        fileSizeLimit: 10485760 // 10MB
      })

      if (createError) {
        console.error('❌ Failed to create bucket:', createError)
      } else {
        console.log('✅ Storage bucket created successfully')
      }
    } else {
      console.log('✅ Storage bucket already exists')
    }

    // Set up storage policies (these need to be done via dashboard or API)
    console.log('📋 Storage policies need to be configured in Supabase Dashboard')
    console.log('   Go to Storage > Policies and add the policies from the setup guide')

  } catch (error) {
    console.error('❌ Storage setup failed:', error)
  }
}

async function verifySetup() {
  const checks = []

  try {
    // Check if photos table exists
    const { data: photosTable, error: photosError } = await supabase
      .from('photos')
      .select('*')
      .limit(1)

    checks.push({
      name: 'Photos table',
      status: !photosError ? 'success' : 'error',
      message: !photosError ? 'Table exists and accessible' : photosError.message
    })

    // Check if pwa_sync_queue table exists
    const { data: syncTable, error: syncError } = await supabase
      .from('pwa_sync_queue')
      .select('*')
      .limit(1)

    checks.push({
      name: 'PWA sync queue table',
      status: !syncError ? 'success' : 'error',
      message: !syncError ? 'Table exists and accessible' : syncError.message
    })

    // Check if bookings table has new columns
    const { data: bookingsTable, error: bookingsError } = await supabase
      .from('bookings')
      .select('before_photo_url, after_photo_url, photo_count')
      .limit(1)

    checks.push({
      name: 'Bookings table enhancement',
      status: !bookingsError ? 'success' : 'error',
      message: !bookingsError ? 'Photo columns added successfully' : bookingsError.message
    })

    // Check storage bucket
    const { data: buckets, error: bucketError } = await supabase.storage.listBuckets()
    const bucketExists = buckets && buckets.some(bucket => bucket.name === 'ocean-soul-sparkles')

    checks.push({
      name: 'Storage bucket',
      status: bucketExists ? 'success' : 'warning',
      message: bucketExists ? 'Bucket exists' : 'Bucket may need manual creation'
    })

    // Display results
    console.log('\nVerification Results:')
    checks.forEach(check => {
      const icon = check.status === 'success' ? '✅' : check.status === 'warning' ? '⚠️' : '❌'
      console.log(`  ${icon} ${check.name}: ${check.message}`)
    })

    const allSuccess = checks.every(check => check.status === 'success')
    if (allSuccess) {
      console.log('\n🎉 All checks passed! Phase 5 setup is complete.')
    } else {
      console.log('\n⚠️  Some checks failed. Please review the setup guide.')
    }

  } catch (error) {
    console.error('❌ Verification failed:', error)
  }
}

// Test photo upload functionality
async function testPhotoUpload() {
  console.log('\n🧪 Testing photo upload functionality...')
  
  try {
    // Create a test photo record
    const testPhoto = {
      filename: 'test-photo.jpg',
      file_path: 'photos/test/test-photo.jpg',
      public_url: 'https://example.com/test-photo.jpg',
      type: 'portfolio',
      file_size: 1024000,
      mime_type: 'image/jpeg',
      metadata: { test: true }
    }

    const { data, error } = await supabase
      .from('photos')
      .insert([testPhoto])
      .select()

    if (error) {
      console.error('❌ Photo upload test failed:', error)
    } else {
      console.log('✅ Photo upload test successful')
      
      // Clean up test data
      await supabase
        .from('photos')
        .delete()
        .eq('id', data[0].id)
      
      console.log('🧹 Test data cleaned up')
    }

  } catch (error) {
    console.error('❌ Photo upload test error:', error)
  }
}

// Main execution
if (import.meta.url === `file://${process.argv[1]}`) {
  setupPhase5Database()
    .then(() => {
      console.log('\n🔧 Running additional tests...')
      return testPhotoUpload()
    })
    .then(() => {
      console.log('\n✨ Phase 5 PWA setup completed successfully!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('\n❌ Setup failed:', error)
      process.exit(1)
    })
}

export {
  setupPhase5Database,
  setupStorageBucket,
  verifySetup,
  testPhotoUpload
}
