/**
 * Business Management Dashboard Component for Ocean Soul Sparkles
 * Provides business integration management interface for accounting and marketing
 * 
 * Phase 7.4: Business Management Integrations
 */

import { useState, useEffect } from 'react'
import { toast } from 'react-toastify'
import { useMobileOptimization } from '@/lib/hooks/useMobileOptimization'
import { authenticatedFetch } from '@/lib/auth-utils'
import styles from './BusinessDashboard.module.css'

export default function BusinessDashboard({ userId }) {
  const [integrations, setIntegrations] = useState([])
  const [financialReports, setFinancialReports] = useState({})
  const [marketingAnalytics, setMarketingAnalytics] = useState({})
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')
  const [syncingBookings, setSyncingBookings] = useState(false)
  const [syncingCustomers, setSyncingCustomers] = useState(false)

  const {
    isMobile,
    isTablet,
    hapticFeedback,
    shouldReduceAnimations
  } = useMobileOptimization()

  useEffect(() => {
    loadBusinessData()
  }, [userId])

  /**
   * Load business integration data
   */
  const loadBusinessData = async () => {
    try {
      setLoading(true)

      // Load integration status
      const statusResponse = await authenticatedFetch('/api/integrations/business/status')
      if (statusResponse.success) {
        setIntegrations(statusResponse.integrations || [])
      }

      // Load financial reports
      const reportsResponse = await authenticatedFetch('/api/integrations/business/reports')
      if (reportsResponse.success) {
        setFinancialReports(reportsResponse.reports || {})
      }

      // Load marketing analytics
      const analyticsResponse = await authenticatedFetch('/api/integrations/business/analytics')
      if (analyticsResponse.success) {
        setMarketingAnalytics(analyticsResponse.analytics || {})
      }

    } catch (error) {
      console.error('Failed to load business data:', error)
      toast.error('Failed to load business data')
    } finally {
      setLoading(false)
    }
  }

  /**
   * Connect to business provider
   */
  const connectProvider = async (provider) => {
    try {
      hapticFeedback('light')

      const response = await authenticatedFetch(`/api/integrations/oauth/${provider}`)
      
      if (response.success && response.authUrl) {
        // Redirect to OAuth authorization
        window.location.href = response.authUrl
      } else {
        toast.error(response.message || 'Failed to initiate business connection')
      }

    } catch (error) {
      console.error('Failed to connect business integration:', error)
      toast.error('Failed to connect business integration')
    }
  }

  /**
   * Disconnect business provider
   */
  const disconnectProvider = async (provider) => {
    try {
      hapticFeedback('medium')

      const confirmed = window.confirm(
        `Are you sure you want to disconnect ${provider}? This will stop automated syncing and financial tracking.`
      )

      if (!confirmed) return

      const response = await authenticatedFetch(`/api/integrations/business/disconnect`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ provider })
      })

      if (response.success) {
        toast.success(`${provider} disconnected successfully`)
        await loadBusinessData()
      } else {
        toast.error(response.message || 'Failed to disconnect business integration')
      }

    } catch (error) {
      console.error('Failed to disconnect business integration:', error)
      toast.error('Failed to disconnect business integration')
    }
  }

  /**
   * Sync bookings to accounting
   */
  const syncBookingsToAccounting = async () => {
    try {
      setSyncingBookings(true)
      hapticFeedback('medium')

      const confirmed = window.confirm(
        'This will sync recent bookings to your accounting system and create invoices. Continue?'
      )

      if (!confirmed) return

      const response = await authenticatedFetch('/api/integrations/business/sync-bookings', {
        method: 'POST'
      })

      if (response.success) {
        const successCount = response.results.filter(r => r.success).length
        toast.success(`Bookings synced successfully. ${successCount} invoices created.`)
        await loadBusinessData()
      } else {
        toast.error(response.message || 'Failed to sync bookings')
      }

    } catch (error) {
      console.error('Failed to sync bookings:', error)
      toast.error('Failed to sync bookings')
    } finally {
      setSyncingBookings(false)
    }
  }

  /**
   * Sync customers to marketing
   */
  const syncCustomersToMarketing = async () => {
    try {
      setSyncingCustomers(true)
      hapticFeedback('medium')

      const confirmed = window.confirm(
        'This will sync your customer database to marketing platforms for email campaigns. Continue?'
      )

      if (!confirmed) return

      const response = await authenticatedFetch('/api/integrations/business/sync-customers', {
        method: 'POST'
      })

      if (response.success) {
        const totalSynced = response.results.reduce((sum, r) => sum + (r.successCount || 0), 0)
        toast.success(`Customers synced successfully. ${totalSynced} contacts added.`)
        await loadBusinessData()
      } else {
        toast.error(response.message || 'Failed to sync customers')
      }

    } catch (error) {
      console.error('Failed to sync customers:', error)
      toast.error('Failed to sync customers')
    } finally {
      setSyncingCustomers(false)
    }
  }

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>Loading business integrations...</p>
      </div>
    )
  }

  return (
    <div className={`${styles.container} ${isMobile ? styles.mobile : ''}`}>
      {/* Header */}
      <div className={styles.header}>
        <h2>Business Management</h2>
        <p>Manage your accounting and marketing integrations</p>
      </div>

      {/* Navigation Tabs */}
      <div className={styles.tabs}>
        {['overview', 'accounting', 'marketing', 'reports'].map(tab => (
          <button
            key={tab}
            className={`${styles.tab} ${activeTab === tab ? styles.active : ''}`}
            onClick={() => {
              setActiveTab(tab)
              hapticFeedback('light')
            }}
          >
            {tab.charAt(0).toUpperCase() + tab.slice(1)}
          </button>
        ))}
      </div>

      {/* Overview Tab */}
      {activeTab === 'overview' && (
        <div className={styles.tabContent}>
          {/* Connected Integrations */}
          <div className={styles.integrations}>
            <h3>Connected Integrations</h3>
            
            {integrations.length === 0 ? (
              <div className={styles.noIntegrations}>
                <p>No business integrations connected yet</p>
                <div className={styles.connectButtons}>
                  <button
                    className={styles.connectButton}
                    onClick={() => connectProvider('quickbooks')}
                  >
                    💰 Connect QuickBooks
                  </button>
                  <button
                    className={styles.connectButton}
                    onClick={() => connectProvider('mailchimp')}
                  >
                    📧 Connect Mailchimp
                  </button>
                </div>
              </div>
            ) : (
              <div className={styles.integrationList}>
                {integrations.map(integration => (
                  <div key={integration.provider} className={styles.integrationCard}>
                    <div className={styles.integrationInfo}>
                      <h4>
                        {integration.provider === 'quickbooks' ? '💰' : '📧'} 
                        {integration.name}
                      </h4>
                      <p className={`${styles.status} ${styles[integration.status]}`}>
                        {integration.status === 'connected' ? '✓ Connected' : '⚠ Needs Attention'}
                      </p>
                      {integration.companyInfo && (
                        <div className={styles.companyInfo}>
                          <p><strong>{integration.companyInfo.Name || integration.companyInfo.account_name}</strong></p>
                        </div>
                      )}
                    </div>
                    
                    <div className={styles.integrationActions}>
                      <button
                        className={styles.settingsButton}
                        onClick={() => setActiveTab(integration.type)}
                      >
                        Manage
                      </button>
                      <button
                        className={styles.disconnectButton}
                        onClick={() => disconnectProvider(integration.provider)}
                      >
                        Disconnect
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Quick Actions */}
          <div className={styles.quickActions}>
            <h3>Quick Actions</h3>
            <div className={styles.actionButtons}>
              <button
                className={styles.actionButton}
                onClick={syncBookingsToAccounting}
                disabled={syncingBookings || integrations.filter(i => i.type === 'accounting').length === 0}
              >
                {syncingBookings ? 'Syncing...' : '💰 Sync Bookings to Accounting'}
              </button>
              <button
                className={styles.actionButton}
                onClick={syncCustomersToMarketing}
                disabled={syncingCustomers || integrations.filter(i => i.type === 'marketing').length === 0}
              >
                {syncingCustomers ? 'Syncing...' : '📧 Sync Customers to Marketing'}
              </button>
              <button
                className={styles.actionButton}
                onClick={() => setActiveTab('reports')}
                disabled={integrations.length === 0}
              >
                📊 View Reports
              </button>
            </div>
          </div>

          {/* Summary Cards */}
          <div className={styles.summaryCards}>
            <div className={styles.summaryCard}>
              <h4>Accounting</h4>
              <p>{integrations.filter(i => i.type === 'accounting').length} Connected</p>
            </div>
            <div className={styles.summaryCard}>
              <h4>Marketing</h4>
              <p>{integrations.filter(i => i.type === 'marketing').length} Connected</p>
            </div>
            <div className={styles.summaryCard}>
              <h4>Last Sync</h4>
              <p>
                {integrations.length > 0 
                  ? new Date(Math.max(...integrations.map(i => new Date(i.lastSync || 0)))).toLocaleDateString()
                  : 'Never'
                }
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Accounting Tab */}
      {activeTab === 'accounting' && (
        <div className={styles.tabContent}>
          <div className={styles.accountingSection}>
            <h3>Accounting Integration</h3>
            
            {integrations.filter(i => i.type === 'accounting').length === 0 ? (
              <div className={styles.noConnection}>
                <p>No accounting software connected</p>
                <button
                  className={styles.connectButton}
                  onClick={() => connectProvider('quickbooks')}
                >
                  💰 Connect QuickBooks
                </button>
              </div>
            ) : (
              <div className={styles.accountingContent}>
                <div className={styles.accountingActions}>
                  <button
                    className={styles.actionButton}
                    onClick={syncBookingsToAccounting}
                    disabled={syncingBookings}
                  >
                    {syncingBookings ? 'Syncing...' : 'Sync Recent Bookings'}
                  </button>
                </div>
                
                {Object.keys(financialReports).length > 0 && (
                  <div className={styles.financialSummary}>
                    <h4>Financial Summary</h4>
                    <p>Financial reports and data will be displayed here</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Marketing Tab */}
      {activeTab === 'marketing' && (
        <div className={styles.tabContent}>
          <div className={styles.marketingSection}>
            <h3>Marketing Integration</h3>
            
            {integrations.filter(i => i.type === 'marketing').length === 0 ? (
              <div className={styles.noConnection}>
                <p>No marketing platform connected</p>
                <button
                  className={styles.connectButton}
                  onClick={() => connectProvider('mailchimp')}
                >
                  📧 Connect Mailchimp
                </button>
              </div>
            ) : (
              <div className={styles.marketingContent}>
                <div className={styles.marketingActions}>
                  <button
                    className={styles.actionButton}
                    onClick={syncCustomersToMarketing}
                    disabled={syncingCustomers}
                  >
                    {syncingCustomers ? 'Syncing...' : 'Sync Customer Database'}
                  </button>
                </div>
                
                {Object.keys(marketingAnalytics).length > 0 && (
                  <div className={styles.marketingSummary}>
                    <h4>Marketing Analytics</h4>
                    <p>Email campaign performance and analytics will be displayed here</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Reports Tab */}
      {activeTab === 'reports' && (
        <div className={styles.tabContent}>
          <div className={styles.reportsSection}>
            <h3>Business Reports</h3>
            
            {integrations.length === 0 ? (
              <div className={styles.noReports}>
                <p>Connect business integrations to view reports</p>
              </div>
            ) : (
              <div className={styles.reportsContent}>
                <p>Comprehensive business reports and analytics will be available here</p>
                <p>Including financial summaries, marketing performance, and ROI analysis</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
