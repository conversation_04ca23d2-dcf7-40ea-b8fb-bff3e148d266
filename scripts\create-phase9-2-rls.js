/**
 * Create Phase 9.2 Row Level Security Policies
 * Ocean Soul Sparkles - Data Protection & Privacy Compliance
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'
import path from 'path'
import { fileURLToPath } from 'url'

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Load environment variables
dotenv.config({ path: path.join(__dirname, '..', '.env.local') })

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

async function createRLSPolicies() {
  console.log('🔐 Creating Phase 9.2 Row Level Security Policies...')
  
  const policies = [
    // Enable RLS on all tables
    {
      name: 'Enable RLS on gdpr_requests',
      sql: 'ALTER TABLE public.gdpr_requests ENABLE ROW LEVEL SECURITY;'
    },
    {
      name: 'Enable RLS on user_consents',
      sql: 'ALTER TABLE public.user_consents ENABLE ROW LEVEL SECURITY;'
    },
    {
      name: 'Enable RLS on privacy_preferences',
      sql: 'ALTER TABLE public.privacy_preferences ENABLE ROW LEVEL SECURITY;'
    },
    {
      name: 'Enable RLS on cookie_consents',
      sql: 'ALTER TABLE public.cookie_consents ENABLE ROW LEVEL SECURITY;'
    },
    {
      name: 'Enable RLS on data_access_logs',
      sql: 'ALTER TABLE public.data_access_logs ENABLE ROW LEVEL SECURITY;'
    },
    {
      name: 'Enable RLS on data_modification_logs',
      sql: 'ALTER TABLE public.data_modification_logs ENABLE ROW LEVEL SECURITY;'
    },
    {
      name: 'Enable RLS on data_retention_logs',
      sql: 'ALTER TABLE public.data_retention_logs ENABLE ROW LEVEL SECURITY;'
    },
    {
      name: 'Enable RLS on encryption_metadata',
      sql: 'ALTER TABLE public.encryption_metadata ENABLE ROW LEVEL SECURITY;'
    },

    // GDPR requests policies
    {
      name: 'Users can view their own GDPR requests',
      sql: `
        CREATE POLICY "Users can view their own GDPR requests" ON public.gdpr_requests
        FOR SELECT USING (user_id = auth.uid() OR customer_id IN (
          SELECT id FROM public.customers WHERE auth_id = auth.uid()
        ));
      `
    },
    {
      name: 'Admin can manage all GDPR requests',
      sql: `
        CREATE POLICY "Admin can manage all GDPR requests" ON public.gdpr_requests
        FOR ALL USING (
          auth.role() IN ('service_role', 'supabase_admin') OR
          EXISTS (
            SELECT 1 FROM public.user_roles ur
            WHERE ur.id = auth.uid() AND ur.role IN ('dev', 'admin')
          )
        );
      `
    },

    // User consents policies
    {
      name: 'Users can manage their own consents',
      sql: `
        CREATE POLICY "Users can manage their own consents" ON public.user_consents
        FOR ALL USING (user_id = auth.uid() OR customer_id IN (
          SELECT id FROM public.customers WHERE auth_id = auth.uid()
        ));
      `
    },
    {
      name: 'Admin can view all consents',
      sql: `
        CREATE POLICY "Admin can view all consents" ON public.user_consents
        FOR SELECT USING (
          auth.role() IN ('service_role', 'supabase_admin') OR
          EXISTS (
            SELECT 1 FROM public.user_roles ur
            WHERE ur.id = auth.uid() AND ur.role IN ('dev', 'admin')
          )
        );
      `
    },

    // Privacy preferences policies
    {
      name: 'Users can manage their own privacy preferences',
      sql: `
        CREATE POLICY "Users can manage their own privacy preferences" ON public.privacy_preferences
        FOR ALL USING (user_id = auth.uid() OR customer_id IN (
          SELECT id FROM public.customers WHERE auth_id = auth.uid()
        ));
      `
    },
    {
      name: 'Admin can view all privacy preferences',
      sql: `
        CREATE POLICY "Admin can view all privacy preferences" ON public.privacy_preferences
        FOR SELECT USING (
          auth.role() IN ('service_role', 'supabase_admin') OR
          EXISTS (
            SELECT 1 FROM public.user_roles ur
            WHERE ur.id = auth.uid() AND ur.role IN ('dev', 'admin')
          )
        );
      `
    },

    // Cookie consents policies
    {
      name: 'Users can manage their own cookie consents',
      sql: `
        CREATE POLICY "Users can manage their own cookie consents" ON public.cookie_consents
        FOR ALL USING (user_id = auth.uid() OR customer_id IN (
          SELECT id FROM public.customers WHERE auth_id = auth.uid()
        ));
      `
    },
    {
      name: 'Admin can view all cookie consents',
      sql: `
        CREATE POLICY "Admin can view all cookie consents" ON public.cookie_consents
        FOR SELECT USING (
          auth.role() IN ('service_role', 'supabase_admin') OR
          EXISTS (
            SELECT 1 FROM public.user_roles ur
            WHERE ur.id = auth.uid() AND ur.role IN ('dev', 'admin')
          )
        );
      `
    },

    // Data access logs policies
    {
      name: 'Users can view their own data access logs',
      sql: `
        CREATE POLICY "Users can view their own data access logs" ON public.data_access_logs
        FOR SELECT USING (user_id = auth.uid() OR customer_id IN (
          SELECT id FROM public.customers WHERE auth_id = auth.uid()
        ));
      `
    },
    {
      name: 'Admin can view all data access logs',
      sql: `
        CREATE POLICY "Admin can view all data access logs" ON public.data_access_logs
        FOR SELECT USING (
          auth.role() IN ('service_role', 'supabase_admin') OR
          EXISTS (
            SELECT 1 FROM public.user_roles ur
            WHERE ur.id = auth.uid() AND ur.role IN ('dev', 'admin')
          )
        );
      `
    },

    // Data modification logs policies
    {
      name: 'Users can view their own data modification logs',
      sql: `
        CREATE POLICY "Users can view their own data modification logs" ON public.data_modification_logs
        FOR SELECT USING (user_id = auth.uid());
      `
    },
    {
      name: 'Admin can view all data modification logs',
      sql: `
        CREATE POLICY "Admin can view all data modification logs" ON public.data_modification_logs
        FOR SELECT USING (
          auth.role() IN ('service_role', 'supabase_admin') OR
          EXISTS (
            SELECT 1 FROM public.user_roles ur
            WHERE ur.id = auth.uid() AND ur.role IN ('dev', 'admin')
          )
        );
      `
    },

    // Data retention logs policies
    {
      name: 'Admin can manage data retention logs',
      sql: `
        CREATE POLICY "Admin can manage data retention logs" ON public.data_retention_logs
        FOR ALL USING (
          auth.role() IN ('service_role', 'supabase_admin') OR
          EXISTS (
            SELECT 1 FROM public.user_roles ur
            WHERE ur.id = auth.uid() AND ur.role IN ('dev', 'admin')
          )
        );
      `
    },

    // Encryption metadata policies
    {
      name: 'Admin can manage encryption metadata',
      sql: `
        CREATE POLICY "Admin can manage encryption metadata" ON public.encryption_metadata
        FOR ALL USING (
          auth.role() IN ('service_role', 'supabase_admin') OR
          EXISTS (
            SELECT 1 FROM public.user_roles ur
            WHERE ur.id = auth.uid() AND ur.role IN ('dev', 'admin')
          )
        );
      `
    }
  ]

  let successCount = 0
  let errorCount = 0

  for (const policy of policies) {
    try {
      console.log(`🔒 Creating policy: ${policy.name}`)
      
      const { error } = await supabase.rpc('execute_sql', {
        sql: policy.sql
      })
      
      if (error) {
        console.error(`❌ Error creating policy "${policy.name}":`, error.message)
        errorCount++
      } else {
        console.log(`✅ Policy "${policy.name}" created successfully`)
        successCount++
      }
    } catch (err) {
      console.error(`❌ Exception creating policy "${policy.name}":`, err.message)
      errorCount++
    }
  }

  console.log('\n📊 RLS Policy Creation Summary:')
  console.log(`✅ Successful policies: ${successCount}`)
  console.log(`❌ Failed policies: ${errorCount}`)

  if (errorCount === 0) {
    console.log('🎉 All Phase 9.2 RLS policies created successfully!')
  } else {
    console.log('⚠️  Some policies failed to create. Please review the logs.')
  }
}

// Run the RLS policy creation
createRLSPolicies()
