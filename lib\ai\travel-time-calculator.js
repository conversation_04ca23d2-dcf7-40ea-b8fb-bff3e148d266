/**
 * Travel Time Calculator for Ocean Soul Sparkles AI Scheduling
 * Integrates with Google Maps Distance Matrix API for accurate travel time calculations
 */

import { getAdminClient } from '@/lib/supabase'
import { prepareFromStorage } from '@/lib/encryption'

export class TravelTimeCalculator {
  constructor() {
    this.apiKey = null
    this.cache = new Map()
    this.cacheExpiry = 15 * 60 * 1000 // 15 minutes
    this.initialized = false
  }

  /**
   * Initialize the calculator with Google Maps API key
   */
  async initialize() {
    if (this.initialized) return

    try {
      this.apiKey = await this.getGoogleMapsApiKey()
      if (!this.apiKey) {
        throw new Error('Google Maps API key not configured in admin settings')
      }
      this.initialized = true
      console.log('[TravelTimeCalculator] Initialized successfully')
    } catch (error) {
      console.error('[TravelTimeCalculator] Initialization failed:', error)
      throw error
    }
  }

  /**
   * Get Google Maps API key from admin settings
   */
  async getGoogleMapsApiKey() {
    try {
      const adminClient = getAdminClient()
      if (!adminClient) {
        throw new Error('Failed to initialize admin client')
      }

      const { data, error } = await adminClient
        .from('admin_settings')
        .select('setting_value')
        .eq('setting_key', 'google_maps_api_key')
        .single()

      if (error && error.code !== 'PGRST116') {
        throw new Error(`Database error: ${error.message}`)
      }

      if (!data?.setting_value) {
        return null
      }

      // Decrypt the API key if it's encrypted
      return prepareFromStorage(data.setting_value)
    } catch (error) {
      console.error('[TravelTimeCalculator] Error getting API key:', error)
      return null
    }
  }

  /**
   * Calculate travel time between two locations
   * @param {string} origin - Origin address or coordinates
   * @param {string} destination - Destination address or coordinates
   * @param {Date} departureTime - Departure time for traffic calculation
   * @returns {Promise<Object>} Travel time information
   */
  async calculateTravelTime(origin, destination, departureTime = new Date()) {
    if (!this.initialized) {
      await this.initialize()
    }

    const cacheKey = `${origin}-${destination}-${Math.floor(departureTime.getTime() / this.cacheExpiry)}`
    
    if (this.cache.has(cacheKey)) {
      console.log('[TravelTimeCalculator] Cache hit for:', cacheKey)
      return this.cache.get(cacheKey)
    }

    try {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/distancematrix/json?` +
        `origins=${encodeURIComponent(origin)}&` +
        `destinations=${encodeURIComponent(destination)}&` +
        `departure_time=${Math.floor(departureTime.getTime() / 1000)}&` +
        `traffic_model=best_guess&` +
        `units=metric&` +
        `key=${this.apiKey}`
      )

      if (!response.ok) {
        throw new Error(`Google Maps API request failed: ${response.status}`)
      }

      const data = await response.json()
      
      if (data.status === 'OK' && data.rows[0]?.elements[0]?.status === 'OK') {
        const element = data.rows[0].elements[0]
        const result = {
          duration: element.duration.value, // seconds
          durationInTraffic: element.duration_in_traffic?.value || element.duration.value,
          distance: element.distance.value, // meters
          travelTimeMinutes: Math.ceil((element.duration_in_traffic?.value || element.duration.value) / 60),
          bufferTimeMinutes: this.calculateBufferTime(element.distance.value),
          totalTimeMinutes: null,
          origin: origin,
          destination: destination,
          departureTime: departureTime.toISOString(),
          calculatedAt: new Date().toISOString(),
          source: 'google_maps'
        }
        
        result.totalTimeMinutes = result.travelTimeMinutes + result.bufferTimeMinutes
        
        // Cache the result
        this.cache.set(cacheKey, result)
        
        console.log(`[TravelTimeCalculator] Calculated travel time: ${result.totalTimeMinutes} minutes`)
        return result
      }
      
      throw new Error(`Google Maps API error: ${data.status} - ${data.error_message || 'Unknown error'}`)
    } catch (error) {
      console.error('[TravelTimeCalculator] API call failed:', error)
      // Fallback to estimated time based on distance
      return this.estimateTravelTime(origin, destination, departureTime)
    }
  }

  /**
   * Calculate buffer time based on distance and complexity
   * @param {number} distanceMeters - Distance in meters
   * @returns {number} Buffer time in minutes
   */
  calculateBufferTime(distanceMeters) {
    // Buffer time based on distance and Melbourne traffic patterns
    if (distanceMeters < 5000) return 15 // 15 min for local travel (< 5km)
    if (distanceMeters < 20000) return 20 // 20 min for medium distance (5-20km)
    if (distanceMeters < 50000) return 30 // 30 min for long distance (20-50km)
    return 45 // 45 min for very long distance (> 50km)
  }

  /**
   * Fallback estimation when Google Maps API is unavailable
   * @param {string} origin - Origin location
   * @param {string} destination - Destination location
   * @param {Date} departureTime - Departure time
   * @returns {Object} Estimated travel time
   */
  estimateTravelTime(origin, destination, departureTime) {
    // Melbourne average speeds: 25 km/h in traffic, 40 km/h off-peak
    const isPeakHour = this.isPeakHour(departureTime)
    const averageSpeed = isPeakHour ? 25 : 40 // km/h
    
    // Estimate distance based on Melbourne metropolitan area
    const estimatedDistance = this.estimateDistance(origin, destination)
    const estimatedMinutes = Math.ceil((estimatedDistance / 1000) / averageSpeed * 60)
    
    const result = {
      duration: estimatedMinutes * 60,
      durationInTraffic: estimatedMinutes * 60,
      distance: estimatedDistance,
      travelTimeMinutes: estimatedMinutes,
      bufferTimeMinutes: this.calculateBufferTime(estimatedDistance),
      totalTimeMinutes: estimatedMinutes + this.calculateBufferTime(estimatedDistance),
      origin: origin,
      destination: destination,
      departureTime: departureTime.toISOString(),
      calculatedAt: new Date().toISOString(),
      source: 'estimate',
      isEstimate: true,
      estimationReason: 'Google Maps API unavailable'
    }

    console.log(`[TravelTimeCalculator] Using fallback estimation: ${result.totalTimeMinutes} minutes`)
    return result
  }

  /**
   * Estimate distance between two locations (simplified)
   * @param {string} origin - Origin location
   * @param {string} destination - Destination location
   * @returns {number} Estimated distance in meters
   */
  estimateDistance(origin, destination) {
    // Simple heuristic for Melbourne area
    // In a real implementation, this could use geocoding or stored location data
    
    // Check if locations are the same or very similar
    if (origin.toLowerCase() === destination.toLowerCase()) {
      return 0
    }
    
    // Default estimates for Melbourne metropolitan area
    const melbourneSuburbs = [
      'melbourne', 'carlton', 'fitzroy', 'collingwood', 'richmond', 'south yarra',
      'toorak', 'prahran', 'st kilda', 'port melbourne', 'southbank', 'docklands'
    ]
    
    const isOriginInner = melbourneSuburbs.some(suburb => 
      origin.toLowerCase().includes(suburb)
    )
    const isDestinationInner = melbourneSuburbs.some(suburb => 
      destination.toLowerCase().includes(suburb)
    )
    
    if (isOriginInner && isDestinationInner) {
      return 8000 // 8km for inner Melbourne
    } else if (isOriginInner || isDestinationInner) {
      return 15000 // 15km for inner to outer
    } else {
      return 25000 // 25km for outer to outer
    }
  }

  /**
   * Check if the given time is during peak hours
   * @param {Date} time - Time to check
   * @returns {boolean} True if peak hour
   */
  isPeakHour(time) {
    const hour = time.getHours()
    const dayOfWeek = time.getDay()
    
    // Weekend traffic is generally lighter
    if (dayOfWeek === 0 || dayOfWeek === 6) {
      return false
    }
    
    // Weekday peak hours: 7-9 AM and 5-7 PM
    return (hour >= 7 && hour <= 9) || (hour >= 17 && hour <= 19)
  }

  /**
   * Calculate travel time for multiple destinations (batch processing)
   * @param {string} origin - Origin location
   * @param {Array<string>} destinations - Array of destination locations
   * @param {Date} departureTime - Departure time
   * @returns {Promise<Array<Object>>} Array of travel time results
   */
  async calculateMultipleTravelTimes(origin, destinations, departureTime = new Date()) {
    if (!this.initialized) {
      await this.initialize()
    }

    // Google Maps API supports up to 25 destinations per request
    const maxDestinations = 25
    const results = []

    for (let i = 0; i < destinations.length; i += maxDestinations) {
      const batch = destinations.slice(i, i + maxDestinations)
      
      try {
        const response = await fetch(
          `https://maps.googleapis.com/maps/api/distancematrix/json?` +
          `origins=${encodeURIComponent(origin)}&` +
          `destinations=${batch.map(dest => encodeURIComponent(dest)).join('|')}&` +
          `departure_time=${Math.floor(departureTime.getTime() / 1000)}&` +
          `traffic_model=best_guess&` +
          `units=metric&` +
          `key=${this.apiKey}`
        )

        if (!response.ok) {
          throw new Error(`Batch request failed: ${response.status}`)
        }

        const data = await response.json()
        
        if (data.status === 'OK') {
          data.rows[0].elements.forEach((element, index) => {
            const destination = batch[index]
            
            if (element.status === 'OK') {
              const result = {
                duration: element.duration.value,
                durationInTraffic: element.duration_in_traffic?.value || element.duration.value,
                distance: element.distance.value,
                travelTimeMinutes: Math.ceil((element.duration_in_traffic?.value || element.duration.value) / 60),
                bufferTimeMinutes: this.calculateBufferTime(element.distance.value),
                totalTimeMinutes: null,
                origin: origin,
                destination: destination,
                departureTime: departureTime.toISOString(),
                calculatedAt: new Date().toISOString(),
                source: 'google_maps'
              }
              
              result.totalTimeMinutes = result.travelTimeMinutes + result.bufferTimeMinutes
              results.push(result)
            } else {
              // Fallback for failed individual destination
              results.push(this.estimateTravelTime(origin, destination, departureTime))
            }
          })
        } else {
          // Fallback for entire batch
          batch.forEach(destination => {
            results.push(this.estimateTravelTime(origin, destination, departureTime))
          })
        }
      } catch (error) {
        console.error('[TravelTimeCalculator] Batch calculation failed:', error)
        // Fallback for entire batch
        batch.forEach(destination => {
          results.push(this.estimateTravelTime(origin, destination, departureTime))
        })
      }
    }

    return results
  }

  /**
   * Clear the cache
   */
  clearCache() {
    this.cache.clear()
    console.log('[TravelTimeCalculator] Cache cleared')
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      maxAge: this.cacheExpiry,
      initialized: this.initialized
    }
  }
}

// Export singleton instance
export const travelTimeCalculator = new TravelTimeCalculator()

// Export class for testing
export default TravelTimeCalculator
