/* Messaging Interface Styles - Phase 8: Advanced Customer Experience */

.messagingInterface {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 600px;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Conversation Header */
.conversationHeader {
  background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
  color: white;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.conversationTitle {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.conversationMeta {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.participant {
  font-size: 0.9rem;
  opacity: 0.9;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
}

/* Messages Area */
.messagesArea {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  gap: 1rem;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #4ECDC4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.emptyMessages {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #666;
  font-style: italic;
}

.messagesList {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  scrollbar-width: thin;
  scrollbar-color: #ccc transparent;
}

.messagesList::-webkit-scrollbar {
  width: 6px;
}

.messagesList::-webkit-scrollbar-track {
  background: transparent;
}

.messagesList::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 3px;
}

/* Messages */
.message {
  display: flex;
  flex-direction: column;
  max-width: 70%;
  word-wrap: break-word;
}

.ownMessage {
  align-self: flex-end;
  align-items: flex-end;
}

.otherMessage {
  align-self: flex-start;
  align-items: flex-start;
}

.messageContent {
  background: #f8f9fa;
  padding: 0.75rem 1rem;
  border-radius: 18px;
  position: relative;
}

.ownMessage .messageContent {
  background: #4ECDC4;
  color: white;
  border-bottom-right-radius: 6px;
}

.otherMessage .messageContent {
  border-bottom-left-radius: 6px;
}

.messageText {
  line-height: 1.4;
  white-space: pre-wrap;
}

.messageAttachment {
  margin-top: 0.5rem;
}

.attachmentImage {
  max-width: 200px;
  max-height: 200px;
  border-radius: 8px;
  cursor: pointer;
}

.attachmentLink {
  color: inherit;
  text-decoration: underline;
  font-size: 0.9rem;
}

.ownMessage .attachmentLink {
  color: rgba(255, 255, 255, 0.9);
}

.messageInfo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.25rem;
  font-size: 0.8rem;
  color: #666;
}

.messageTime {
  opacity: 0.7;
}

.messageStatus {
  color: #4ECDC4;
  font-weight: 600;
}

/* Message Form */
.messageForm {
  border-top: 1px solid #e9ecef;
  background: white;
}

.attachmentsPreview {
  padding: 0.75rem 1rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.attachmentPreview {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: white;
  padding: 0.5rem;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.attachmentName {
  font-size: 0.9rem;
  color: #333;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.removeAttachment {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 0.8rem;
  line-height: 1;
}

.inputArea {
  display: flex;
  align-items: flex-end;
  gap: 0.5rem;
  padding: 1rem;
}

.attachButton {
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
  flex-shrink: 0;
}

.attachButton:hover {
  background: #5a6268;
}

.attachButton:disabled {
  background: #e9ecef;
  color: #6c757d;
  cursor: not-allowed;
}

.messageInput {
  flex: 1;
  border: 2px solid #e9ecef;
  border-radius: 20px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  font-family: inherit;
  resize: none;
  max-height: 120px;
  min-height: 40px;
  transition: border-color 0.3s ease;
  scrollbar-width: thin;
}

.messageInput:focus {
  outline: none;
  border-color: #4ECDC4;
}

.messageInput:disabled {
  background: #f8f9fa;
  color: #6c757d;
}

.sendButton {
  background: #4ECDC4;
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
  flex-shrink: 0;
  font-size: 1.2rem;
}

.sendButton:hover {
  background: #44A08D;
}

.sendButton:disabled {
  background: #e9ecef;
  color: #6c757d;
  cursor: not-allowed;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .messagingInterface {
    max-height: 500px;
  }

  .conversationHeader {
    padding: 0.75rem 1rem;
  }

  .conversationTitle {
    font-size: 1.1rem;
  }

  .participant {
    font-size: 0.8rem;
  }

  .messagesList {
    padding: 0.75rem;
  }

  .message {
    max-width: 85%;
  }

  .messageContent {
    padding: 0.5rem 0.75rem;
  }

  .inputArea {
    padding: 0.75rem;
  }

  .attachButton,
  .sendButton {
    width: 36px;
    height: 36px;
  }

  .messageInput {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .messagingInterface {
    max-height: 400px;
  }

  .conversationMeta {
    gap: 0.5rem;
  }

  .participant {
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
  }

  .message {
    max-width: 90%;
  }

  .messageInfo {
    font-size: 0.7rem;
  }

  .attachmentsPreview {
    padding: 0.5rem;
  }

  .attachmentName {
    max-width: 100px;
    font-size: 0.8rem;
  }

  .inputArea {
    padding: 0.5rem;
    gap: 0.25rem;
  }

  .attachButton,
  .sendButton {
    width: 32px;
    height: 32px;
    font-size: 1rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .messagingInterface {
    background: #2d2d2d;
    color: #fff;
  }

  .messagesList {
    scrollbar-color: #555 transparent;
  }

  .otherMessage .messageContent {
    background: #3d3d3d;
    color: #fff;
  }

  .messageForm {
    background: #2d2d2d;
    border-top-color: #444;
  }

  .attachmentsPreview {
    background: #3d3d3d;
    border-bottom-color: #444;
  }

  .attachmentPreview {
    background: #2d2d2d;
    border-color: #444;
  }

  .messageInput {
    background: #3d3d3d;
    border-color: #444;
    color: #fff;
  }

  .messageInput:focus {
    border-color: #4ECDC4;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .spinner {
    animation: none;
  }

  .attachButton,
  .sendButton,
  .messageInput {
    transition: none;
  }
}
