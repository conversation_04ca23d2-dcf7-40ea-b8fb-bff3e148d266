/**
 * Social Media Status API Endpoint for Ocean Soul Sparkles
 * Provides social media integration status and information
 * 
 * Phase 7.3: Social Media Integration Layer
 */

import { authenticateAdminRequest } from '@/lib/admin-auth'
import { integrationRateLimit } from '@/lib/integrations/rate-limiter'
import { RequestValida<PERSON>, AuditLogger } from '@/lib/integrations/security-utils'
import SocialMediaManager from '@/lib/integrations/social/social-manager'
import oauthManager from '@/lib/integrations/oauth-manager'

/**
 * Social Media Status Handler
 * GET /api/integrations/social/status - Get social media integration status
 */
export default async function handler(req, res) {
  // Apply rate limiting
  await new Promise((resolve, reject) => {
    integrationRateLimit(req, res, (error) => {
      if (error) reject(error)
      else resolve()
    })
  })

  const startTime = Date.now()

  try {
    // Validate request method
    RequestValidator.validateEndpointAccess(req, ['GET'])

    // Authenticate user
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.success) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      })
    }

    const { user } = authResult
    const userId = user.id

    // Get integration status
    const integrationStatus = await oauthManager.getIntegrationStatus(userId)
    
    // Filter for social media providers
    const socialProviders = ['instagram_business', 'facebook_business', 'tiktok', 'linkedin']
    const socialIntegrations = integrationStatus.filter(integration => 
      socialProviders.includes(integration.provider)
    )

    // Get detailed social media information
    const socialManager = new SocialMediaManager(userId)
    const connectedProviders = await socialManager.getConnectedProviders()

    // Test connections
    const connectionTests = await socialManager.testAllConnections()

    // Combine all information
    const integrations = socialIntegrations.map(integration => {
      const providerInfo = connectedProviders.find(p => p.provider === integration.provider)
      const connectionTest = connectionTests.find(t => t.provider === integration.provider)
      
      return {
        provider: integration.provider,
        name: getProviderName(integration.provider),
        connected: integration.connected,
        status: connectionTest?.success ? 'connected' : 'error',
        lastUpdated: integration.lastUpdated,
        needsRefresh: integration.needsRefresh,
        settings: providerInfo?.settings || {},
        profile: connectionTest?.profile || null,
        error: connectionTest?.error || null
      }
    })

    await AuditLogger.logApiAccess(
      userId,
      req.url,
      req.method,
      200,
      Date.now() - startTime,
      { action: 'get_social_status', integrationCount: integrations.length }
    )

    return res.status(200).json({
      success: true,
      integrations,
      availableProviders: getAvailableProviders()
    })

  } catch (error) {
    console.error('Social media status error:', error)

    await AuditLogger.logApiAccess(
      req.user?.id || null,
      req.url,
      req.method,
      500,
      Date.now() - startTime,
      { error: error.message }
    )

    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to get social media status'
    })
  }
}

/**
 * Get provider display name
 */
function getProviderName(provider) {
  const names = {
    instagram_business: 'Instagram Business',
    facebook_business: 'Facebook Business',
    tiktok: 'TikTok Business',
    linkedin: 'LinkedIn Business'
  }
  return names[provider] || provider
}

/**
 * Get available social media providers
 */
function getAvailableProviders() {
  return [
    {
      id: 'instagram_business',
      name: 'Instagram Business',
      description: 'Share your portfolio and connect with clients',
      features: ['Portfolio sync', 'Automated posting', 'Analytics', 'Hashtag research'],
      available: true
    },
    {
      id: 'facebook_business',
      name: 'Facebook Business',
      description: 'Promote events and manage your business page',
      features: ['Event promotion', 'Page management', 'Analytics', 'Automated posting'],
      available: true
    },
    {
      id: 'tiktok',
      name: 'TikTok Business',
      description: 'Share creative content and reach new audiences',
      features: ['Video content', 'Trending hashtags', 'Analytics'],
      available: false,
      comingSoon: true
    },
    {
      id: 'linkedin',
      name: 'LinkedIn Business',
      description: 'Professional networking and business connections',
      features: ['Professional posts', 'Business networking', 'Industry insights'],
      available: false,
      comingSoon: true
    }
  ]
}

/**
 * API Route Configuration
 */
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
}
