/**
 * Calendar Events API Endpoint for Ocean Soul Sparkles
 * Handles calendar event operations and management
 * 
 * Phase 7: Advanced Integrations & Ecosystem
 */

import { authenticateAdminRequest } from '@/lib/admin-auth'
import { integrationRateLimit } from '@/lib/integrations/rate-limiter'
import { RequestValida<PERSON>, AuditLogger } from '@/lib/integrations/security-utils'
import CalendarManager from '@/lib/integrations/calendar/calendar-manager'

/**
 * Calendar Events Handler
 * GET /api/integrations/calendar/events - Get events from connected calendars
 * POST /api/integrations/calendar/events - Create event in calendar
 * PUT /api/integrations/calendar/events - Update event in calendar
 * DELETE /api/integrations/calendar/events - Delete event from calendar
 */
export default async function handler(req, res) {
  // Apply rate limiting
  await new Promise((resolve, reject) => {
    integrationRateLimit(req, res, (error) => {
      if (error) reject(error)
      else resolve()
    })
  })

  const startTime = Date.now()

  try {
    // Validate request method
    RequestValidator.validateEndpointAccess(req, ['GET', 'POST', 'PUT', 'DELETE'])

    // Authenticate user
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.success) {
      await AuditLogger.logSecurityEvent(
        null,
        'calendar_events_unauthorized',
        {
          endpoint: req.url,
          method: req.method,
          userAgent: req.headers['user-agent'],
          ipAddress: req.headers['x-forwarded-for'] || req.connection?.remoteAddress
        },
        'warning'
      )

      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required for calendar events'
      })
    }

    const { user } = authResult
    const userId = user.id

    // Initialize calendar manager
    const calendarManager = new CalendarManager(userId)

    if (req.method === 'GET') {
      // Get events from calendars
      const { 
        timeMin, 
        timeMax, 
        maxResults = 250,
        provider,
        calendarId 
      } = req.query

      let events
      if (provider && calendarId) {
        // Get events from specific calendar
        const client = await calendarManager.getClient(provider)
        events = await client.getEvents(calendarId, timeMin, timeMax, parseInt(maxResults))
      } else {
        // Get events from all connected calendars
        events = await calendarManager.getAllEvents(timeMin, timeMax, parseInt(maxResults))
      }

      await AuditLogger.logApiAccess(
        userId,
        req.url,
        req.method,
        200,
        Date.now() - startTime,
        { 
          action: 'get_events',
          eventCount: events.length,
          provider,
          calendarId
        }
      )

      return res.status(200).json({
        success: true,
        events,
        count: events.length
      })

    } else if (req.method === 'POST') {
      // Create new event
      const { provider, calendarId, eventData } = req.body

      if (!provider || !calendarId || !eventData) {
        return res.status(400).json({
          error: 'Missing required fields',
          message: 'Provider, calendarId, and eventData are required'
        })
      }

      // Validate event data
      const validationResult = validateEventData(eventData)
      if (!validationResult.valid) {
        return res.status(400).json({
          error: 'Invalid event data',
          message: validationResult.message
        })
      }

      const event = await calendarManager.createEvent(provider, calendarId, eventData)

      await AuditLogger.logIntegrationActivity(
        userId,
        provider,
        'event_created',
        'success',
        {
          eventId: event.id,
          calendarId,
          summary: eventData.summary
        }
      )

      await AuditLogger.logApiAccess(
        userId,
        req.url,
        req.method,
        201,
        Date.now() - startTime,
        { 
          action: 'create_event',
          provider,
          calendarId,
          eventId: event.id
        }
      )

      return res.status(201).json({
        success: true,
        message: 'Event created successfully',
        event
      })

    } else if (req.method === 'PUT') {
      // Update existing event
      const { provider, calendarId, eventId, eventData } = req.body

      if (!provider || !calendarId || !eventId || !eventData) {
        return res.status(400).json({
          error: 'Missing required fields',
          message: 'Provider, calendarId, eventId, and eventData are required'
        })
      }

      // Validate event data
      const validationResult = validateEventData(eventData)
      if (!validationResult.valid) {
        return res.status(400).json({
          error: 'Invalid event data',
          message: validationResult.message
        })
      }

      const event = await calendarManager.updateEvent(provider, calendarId, eventId, eventData)

      await AuditLogger.logIntegrationActivity(
        userId,
        provider,
        'event_updated',
        'success',
        {
          eventId,
          calendarId,
          summary: eventData.summary
        }
      )

      await AuditLogger.logApiAccess(
        userId,
        req.url,
        req.method,
        200,
        Date.now() - startTime,
        { 
          action: 'update_event',
          provider,
          calendarId,
          eventId
        }
      )

      return res.status(200).json({
        success: true,
        message: 'Event updated successfully',
        event
      })

    } else if (req.method === 'DELETE') {
      // Delete event
      const { provider, calendarId, eventId } = req.body

      if (!provider || !calendarId || !eventId) {
        return res.status(400).json({
          error: 'Missing required fields',
          message: 'Provider, calendarId, and eventId are required'
        })
      }

      await calendarManager.deleteEvent(provider, calendarId, eventId)

      await AuditLogger.logIntegrationActivity(
        userId,
        provider,
        'event_deleted',
        'success',
        {
          eventId,
          calendarId
        }
      )

      await AuditLogger.logApiAccess(
        userId,
        req.url,
        req.method,
        200,
        Date.now() - startTime,
        { 
          action: 'delete_event',
          provider,
          calendarId,
          eventId
        }
      )

      return res.status(200).json({
        success: true,
        message: 'Event deleted successfully'
      })
    }

  } catch (error) {
    console.error('Calendar events error:', error)

    await AuditLogger.logSecurityEvent(
      req.user?.id || null,
      'calendar_events_error',
      {
        error: error.message,
        stack: error.stack,
        endpoint: req.url,
        method: req.method,
        userAgent: req.headers['user-agent'],
        ipAddress: req.headers['x-forwarded-for'] || req.connection?.remoteAddress
      },
      'error'
    )

    await AuditLogger.logApiAccess(
      req.user?.id || null,
      req.url,
      req.method,
      500,
      Date.now() - startTime,
      { error: error.message }
    )

    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to perform calendar event operation'
    })
  }
}

/**
 * Validate event data
 */
function validateEventData(eventData) {
  const required = ['summary', 'start', 'end']
  
  for (const field of required) {
    if (!eventData[field]) {
      return {
        valid: false,
        message: `Missing required field: ${field}`
      }
    }
  }

  // Validate date formats
  const startDate = new Date(eventData.start)
  const endDate = new Date(eventData.end)

  if (isNaN(startDate.getTime())) {
    return {
      valid: false,
      message: 'Invalid start date format'
    }
  }

  if (isNaN(endDate.getTime())) {
    return {
      valid: false,
      message: 'Invalid end date format'
    }
  }

  if (startDate >= endDate) {
    return {
      valid: false,
      message: 'Start date must be before end date'
    }
  }

  // Validate summary length
  if (eventData.summary.length > 255) {
    return {
      valid: false,
      message: 'Summary must be 255 characters or less'
    }
  }

  // Validate description length
  if (eventData.description && eventData.description.length > 8192) {
    return {
      valid: false,
      message: 'Description must be 8192 characters or less'
    }
  }

  return { valid: true }
}

/**
 * API Route Configuration
 */
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
}
