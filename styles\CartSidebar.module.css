.sidebarOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  justify-content: flex-end;
  animation: fadeInOverlay 0.3s ease-out;
}

@keyframes fadeInOverlay {
  from { opacity: 0; }
  to { opacity: 1; }
}

.sidebar {
  width: 100%;
  max-width: 450px;
  height: 100%;
  background-color: #fff;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  animation: slideInSidebar 0.3s ease-out;
}

@keyframes slideInSidebar {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.2rem 1.5rem;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f9f9f9;
}

.header h2 {
  margin: 0;
  font-size: 1.4rem;
  color: #333;
}

.closeButton {
  background: none;
  border: none;
  font-size: 2rem;
  cursor: pointer;
  color: #777;
  padding: 0.5rem;
  line-height: 1;
}
.closeButton:hover {
  color: #333;
}

.emptyCartMessage {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.emptyCartMessage p {
  font-size: 1.1rem;
  color: #555;
  margin-bottom: 1.5rem;
}

.continueShoppingButton {
  background-color: #1A73E8;
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 5px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.continueShoppingButton:hover {
  background-color: #1557b0;
}

.cartItemsContainer {
  flex-grow: 1;
  overflow-y: auto;
  padding: 1rem;
}

.cartItem {
  display: flex;
  padding: 1rem 0.5rem;
  border-bottom: 1px solid #f0f0f0;
  gap: 1rem;
}
.cartItem:last-child {
  border-bottom: none;
}

.itemImage {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #eee;
}

.itemImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.itemDetails {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.itemName {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.itemPrice {
  font-size: 0.9rem;
  color: #555;
  margin: 0;
}

.itemQuantity {
  display: flex;
  align-items: center;
  margin-top: 0.5rem;
}

.itemQuantity button {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  color: #555;
  cursor: pointer;
  width: 30px;
  height: 30px;
  font-size: 1rem;
  transition: background-color 0.2s;
}
.itemQuantity button:hover {
  background-color: #e0e0e0;
}

.itemQuantity input {
  width: 40px;
  height: 30px;
  text-align: center;
  border: 1px solid #ddd;
  border-left: none;
  border-right: none;
  font-size: 0.95rem;
  -moz-appearance: textfield; /* Firefox */
}
.itemQuantity input::-webkit-outer-spin-button,
.itemQuantity input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}


.itemActions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
  min-width: 80px; /* Ensure consistent width */
}

.removeItemButton {
  background: none;
  border: none;
  color: #FF6B6B;
  cursor: pointer;
  font-size: 0.8rem;
  text-decoration: underline;
  padding: 0.2rem 0;
}
.removeItemButton:hover {
  color: #e05252;
}

.itemSubtotal {
  font-size: 0.95rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.footer {
  padding: 1.5rem;
  border-top: 1px solid #e0e0e0;
  background-color: #f9f9f9;
}

.subtotal {
  display: flex;
  justify-content: space-between;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: #333;
}

.checkoutButton {
  width: 100%;
  background-color: #28a745; /* Green for checkout */
  color: white;
  border: none;
  padding: 1rem;
  border-radius: 5px;
  font-weight: 700;
  font-size: 1.1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin-bottom: 1rem;
}

.checkoutButton:hover {
  background-color: #218838;
}

.footerActions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.clearCartButton {
  background: none;
  border: 1px solid #FF6B6B;
  color: #FF6B6B;
  padding: 0.6rem 1rem;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}
.clearCartButton:hover {
  background-color: #FF6B6B;
  color: white;
}

.continueShoppingButtonAlt {
  background: none;
  border: 1px solid #555;
  color: #555;
  padding: 0.6rem 1rem;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}
.continueShoppingButtonAlt:hover {
  background-color: #555;
  color: white;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .sidebar {
    max-width: 100%; /* Full width on small screens */
  }

  .header h2 {
    font-size: 1.2rem;
  }
  .closeButton {
    font-size: 1.8rem;
  }

  .itemImage {
    width: 60px;
    height: 60px;
  }
  .itemName {
    font-size: 0.9rem;
  }
  .itemPrice, .itemSubtotal {
    font-size: 0.85rem;
  }
  .itemQuantity button {
    width: 25px;
    height: 25px;
    font-size: 0.9rem;
  }
  .itemQuantity input {
    width: 30px;
    height: 25px;
    font-size: 0.9rem;
  }
  .removeItemButton {
    font-size: 0.75rem;
  }

  .footer {
    padding: 1rem;
  }
  .subtotal {
    font-size: 1.1rem;
    margin-bottom: 1rem;
  }
  .checkoutButton {
    padding: 0.8rem;
    font-size: 1rem;
  }
  .clearCartButton, .continueShoppingButtonAlt {
    padding: 0.5rem 0.8rem;
    font-size: 0.85rem;
  }
}
