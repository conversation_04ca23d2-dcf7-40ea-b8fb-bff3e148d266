/**
 * Customer Profile API - Phase 8: Advanced Customer Experience
 * Ocean Soul Sparkles - Customer Profile Management
 */

import { getAdminClient } from '@/lib/supabase'
import { getCurrentUser } from '@/lib/auth'

export default async function handler(req, res) {
  if (req.method === 'GET') {
    return handleGetProfile(req, res)
  } else if (req.method === 'PATCH') {
    return handleUpdateProfile(req, res)
  } else {
    res.setHeader('Allow', ['GET', 'PATCH'])
    return res.status(405).json({ error: 'Method not allowed' })
  }
}

/**
 * Get customer profile with enhanced data
 */
async function handleGetProfile(req, res) {
  try {
    // Get authenticated user
    const user = await getCurrentUser(req)
    if (!user) {
      return res.status(401).json({ error: 'Authentication required' })
    }

    const adminClient = getAdminClient()
    if (!adminClient) {
      return res.status(500).json({ error: 'Database connection failed' })
    }

    // Get customer data with related information
    const { data: customer, error: customerError } = await adminClient
      .from('customers')
      .select(`
        *,
        preferences:customer_advanced_preferences(*),
        loyalty:customer_loyalty(*),
        subscriptions:customer_subscriptions(
          *,
          service:services(name, category)
        )
      `)
      .eq('email', user.email)
      .single()

    if (customerError) {
      if (customerError.code === 'PGRST116') {
        return res.status(404).json({ error: 'Customer profile not found' })
      }
      console.error('Error fetching customer:', customerError)
      return res.status(500).json({ error: 'Failed to fetch customer profile' })
    }

    // Get recent booking history
    const { data: recentBookings, error: bookingsError } = await adminClient
      .from('bookings')
      .select(`
        id,
        start_time,
        end_time,
        status,
        service:services(name, category),
        artist:artist_profiles(artist_name, display_name)
      `)
      .eq('customer_id', customer.id)
      .order('start_time', { ascending: false })
      .limit(5)

    if (bookingsError) {
      console.error('Error fetching recent bookings:', bookingsError)
      // Don't fail the request if bookings can't be fetched
    }

    // Get unread message count
    const { count: unreadMessages, error: messagesError } = await adminClient
      .from('customer_messages')
      .select('id', { count: 'exact' })
      .eq('recipient_id', customer.id)
      .is('read_at', null)

    if (messagesError) {
      console.error('Error fetching message count:', messagesError)
      // Don't fail the request if messages can't be fetched
    }

    // Enhance customer data
    const enhancedCustomer = {
      ...customer,
      loyalty_info: customer.loyalty?.[0] || null,
      preferences: customer.preferences?.[0] || null,
      recent_bookings: recentBookings || [],
      unread_messages: unreadMessages || 0,
      active_subscriptions: customer.subscriptions?.filter(sub => sub.is_active) || [],
      profile_completion: calculateProfileCompletion(customer)
    }

    return res.status(200).json({
      success: true,
      customer: enhancedCustomer
    })

  } catch (error) {
    console.error('Error in customer profile retrieval:', error)
    return res.status(500).json({ 
      error: 'Internal server error',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

/**
 * Update customer profile
 */
async function handleUpdateProfile(req, res) {
  try {
    // Get authenticated user
    const user = await getCurrentUser(req)
    if (!user) {
      return res.status(401).json({ error: 'Authentication required' })
    }

    const {
      name,
      phone,
      address,
      city,
      state,
      postal_code,
      country,
      birth_date,
      marketing_consent,
      profile_image_url,
      preferences
    } = req.body

    const adminClient = getAdminClient()
    if (!adminClient) {
      return res.status(500).json({ error: 'Database connection failed' })
    }

    // Get current customer
    const { data: currentCustomer, error: fetchError } = await adminClient
      .from('customers')
      .select('id')
      .eq('email', user.email)
      .single()

    if (fetchError) {
      console.error('Error fetching current customer:', fetchError)
      return res.status(404).json({ error: 'Customer not found' })
    }

    // Update customer basic info
    const updateData = {}
    if (name !== undefined) updateData.name = name
    if (phone !== undefined) updateData.phone = phone
    if (address !== undefined) updateData.address = address
    if (city !== undefined) updateData.city = city
    if (state !== undefined) updateData.state = state
    if (postal_code !== undefined) updateData.postal_code = postal_code
    if (country !== undefined) updateData.country = country
    if (birth_date !== undefined) updateData.birth_date = birth_date
    if (marketing_consent !== undefined) updateData.marketing_consent = marketing_consent
    if (profile_image_url !== undefined) updateData.profile_image_url = profile_image_url

    // Only update if there are changes
    if (Object.keys(updateData).length > 0) {
      updateData.updated_at = new Date().toISOString()

      const { data: updatedCustomer, error: updateError } = await adminClient
        .from('customers')
        .update(updateData)
        .eq('id', currentCustomer.id)
        .select()
        .single()

      if (updateError) {
        console.error('Error updating customer:', updateError)
        return res.status(500).json({ error: 'Failed to update customer profile' })
      }
    }

    // Update preferences if provided
    if (preferences) {
      const { error: preferencesError } = await adminClient
        .from('customer_advanced_preferences')
        .upsert({
          customer_id: currentCustomer.id,
          ...preferences,
          updated_at: new Date().toISOString()
        })

      if (preferencesError) {
        console.error('Error updating preferences:', preferencesError)
        return res.status(500).json({ error: 'Failed to update preferences' })
      }
    }

    // Fetch updated customer data
    const { data: customer, error: refetchError } = await adminClient
      .from('customers')
      .select(`
        *,
        preferences:customer_advanced_preferences(*),
        loyalty:customer_loyalty(*)
      `)
      .eq('id', currentCustomer.id)
      .single()

    if (refetchError) {
      console.error('Error refetching customer:', refetchError)
      return res.status(500).json({ error: 'Failed to fetch updated profile' })
    }

    // Enhance customer data
    const enhancedCustomer = {
      ...customer,
      loyalty_info: customer.loyalty?.[0] || null,
      preferences: customer.preferences?.[0] || null,
      profile_completion: calculateProfileCompletion(customer)
    }

    return res.status(200).json({
      success: true,
      message: 'Profile updated successfully',
      customer: enhancedCustomer
    })

  } catch (error) {
    console.error('Error in customer profile update:', error)
    return res.status(500).json({ 
      error: 'Internal server error',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

/**
 * Calculate profile completion percentage
 */
function calculateProfileCompletion(customer) {
  const fields = [
    'name',
    'email', 
    'phone',
    'address',
    'city',
    'state',
    'postal_code',
    'birth_date'
  ]

  const completedFields = fields.filter(field => customer[field])
  const percentage = Math.round((completedFields.length / fields.length) * 100)

  return {
    percentage,
    completed_fields: completedFields.length,
    total_fields: fields.length,
    missing_fields: fields.filter(field => !customer[field])
  }
}
