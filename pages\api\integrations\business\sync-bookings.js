/**
 * Business Booking Sync API Endpoint for Ocean Soul Sparkles
 * Handles synchronization of bookings to accounting systems
 * 
 * Phase 7.4: Business Management Integrations
 */

import { authenticateAdminRequest } from '@/lib/admin-auth'
import { syncRateLimit } from '@/lib/integrations/rate-limiter'
import { RequestValidator, AuditLogger } from '@/lib/integrations/security-utils'
import BusinessManager from '@/lib/integrations/business-manager'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

/**
 * Booking Sync Handler
 * POST /api/integrations/business/sync-bookings - Sync bookings to accounting systems
 */
export default async function handler(req, res) {
  // Apply rate limiting
  await new Promise((resolve, reject) => {
    syncRateLimit(req, res, (error) => {
      if (error) reject(error)
      else resolve()
    })
  })

  const startTime = Date.now()

  try {
    // Validate request method
    RequestValidator.validateEndpointAccess(req, ['POST'])

    // Authenticate user
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.success) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      })
    }

    const { user } = authResult
    const userId = user.id

    // Get request parameters
    const { 
      limit = 50,
      daysBack = 30,
      status = 'completed',
      forceSync = false
    } = req.body

    // Initialize business manager
    const businessManager = new BusinessManager(userId)

    // Get bookings to sync
    const bookings = await getBookingsToSync(userId, {
      limit: parseInt(limit),
      daysBack: parseInt(daysBack),
      status,
      forceSync
    })

    if (bookings.length === 0) {
      return res.status(200).json({
        success: true,
        message: 'No bookings found to sync',
        results: []
      })
    }

    // Sync bookings to accounting systems
    const syncResults = []

    for (const booking of bookings) {
      try {
        // Format booking data for accounting sync
        const bookingData = formatBookingForAccounting(booking)
        
        // Sync to all connected accounting providers
        const results = await businessManager.syncBookingToAccounting(bookingData)
        
        // Update booking with sync status
        await updateBookingSyncStatus(booking.id, results)
        
        syncResults.push({
          bookingId: booking.id,
          customerName: booking.customer_name,
          amount: booking.total_amount,
          results
        })

      } catch (error) {
        console.error(`Failed to sync booking ${booking.id}:`, error)
        
        syncResults.push({
          bookingId: booking.id,
          customerName: booking.customer_name,
          amount: booking.total_amount,
          error: error.message,
          results: []
        })
      }
    }

    const successCount = syncResults.filter(r => 
      r.results && r.results.some(result => result.success)
    ).length

    await AuditLogger.logIntegrationActivity(
      userId,
      'business_management',
      'bookings_sync_completed',
      successCount > 0 ? 'success' : 'warning',
      {
        bookingCount: bookings.length,
        syncResults: syncResults.length,
        successCount,
        failedCount: syncResults.length - successCount
      }
    )

    await AuditLogger.logApiAccess(
      userId,
      req.url,
      req.method,
      200,
      Date.now() - startTime,
      { 
        action: 'sync_bookings_to_accounting',
        bookings: bookings.length,
        syncResults: syncResults.length,
        successCount
      }
    )

    return res.status(200).json({
      success: true,
      message: `Booking sync completed. ${successCount} bookings synced successfully.`,
      results: syncResults,
      summary: {
        totalBookings: bookings.length,
        successful: successCount,
        failed: syncResults.length - successCount
      }
    })

  } catch (error) {
    console.error('Booking sync error:', error)

    await AuditLogger.logSecurityEvent(
      req.user?.id || null,
      'booking_sync_error',
      {
        error: error.message,
        stack: error.stack,
        endpoint: req.url,
        method: req.method,
        userAgent: req.headers['user-agent'],
        ipAddress: req.headers['x-forwarded-for'] || req.connection?.remoteAddress
      },
      'error'
    )

    await AuditLogger.logApiAccess(
      req.user?.id || null,
      req.url,
      req.method,
      500,
      Date.now() - startTime,
      { error: error.message }
    )

    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to sync bookings to accounting systems'
    })
  }
}

/**
 * Get bookings to sync to accounting systems
 */
async function getBookingsToSync(userId, options) {
  try {
    let query = supabase
      .from('bookings')
      .select(`
        *,
        customers (
          id,
          name,
          email,
          phone
        ),
        services (
          id,
          name,
          price,
          duration
        ),
        artists (
          id,
          name
        )
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    // Filter by status
    if (options.status) {
      query = query.eq('status', options.status)
    }

    // Filter by date range
    if (options.daysBack) {
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - options.daysBack)
      query = query.gte('created_at', cutoffDate.toISOString())
    }

    // Filter out already synced bookings unless force sync
    if (!options.forceSync) {
      query = query.is('accounting_sync_data', null)
    }

    // Apply limit
    if (options.limit) {
      query = query.limit(options.limit)
    }

    const { data: bookings, error } = await query

    if (error) {
      throw new Error(`Failed to fetch bookings: ${error.message}`)
    }

    return bookings || []

  } catch (error) {
    console.error('Error getting bookings to sync:', error)
    return []
  }
}

/**
 * Format booking data for accounting system sync
 */
function formatBookingForAccounting(booking) {
  return {
    bookingId: booking.id,
    customerId: booking.customer_id,
    customerName: booking.customers?.name || booking.customer_name,
    customerEmail: booking.customers?.email || booking.customer_email,
    customerPhone: booking.customers?.phone || booking.customer_phone,
    serviceName: booking.services?.name || booking.service_name,
    serviceItemId: booking.services?.quickbooks_item_id || null,
    artistName: booking.artists?.name || booking.artist_name,
    totalAmount: parseFloat(booking.total_amount || 0),
    serviceDate: booking.service_date,
    bookingDate: booking.created_at,
    status: booking.status,
    notes: booking.notes,
    quickbooksCustomerId: booking.quickbooks_customer_id || null
  }
}

/**
 * Update booking with accounting sync status
 */
async function updateBookingSyncStatus(bookingId, syncResults) {
  try {
    const syncData = {
      lastSyncAt: new Date().toISOString(),
      syncResults,
      syncedProviders: syncResults.filter(r => r.success).map(r => r.provider),
      invoiceIds: syncResults
        .filter(r => r.success && r.invoiceId)
        .reduce((acc, r) => {
          acc[r.provider] = r.invoiceId
          return acc
        }, {})
    }

    await supabase
      .from('bookings')
      .update({
        accounting_sync_data: syncData,
        updated_at: new Date().toISOString()
      })
      .eq('id', bookingId)

  } catch (error) {
    console.error('Error updating booking sync status:', error)
  }
}

/**
 * API Route Configuration
 */
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
}
