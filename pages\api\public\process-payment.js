/**
 * Public API endpoint for processing Square payments in public checkout
 * This endpoint handles Square payment processing for customer orders
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8)
  console.log(`[${requestId}] Public Process Payment API called: ${req.method}`)

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Extract payment data from request
    const { token, amount, currency = 'AUD', orderDetails = {} } = req.body

    // Validate required fields
    if (!token || !amount) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'Payment token and amount are required'
      })
    }

    // Validate amount
    const paymentAmount = parseFloat(amount)
    if (isNaN(paymentAmount) || paymentAmount <= 0) {
      return res.status(400).json({
        error: 'Invalid amount',
        message: 'Amount must be a positive number'
      })
    }

    // Validate currency
    const paymentCurrency = currency.toUpperCase()
    if (!['AUD', 'USD'].includes(paymentCurrency)) {
      return res.status(400).json({
        error: 'Invalid currency',
        message: 'Currency must be AUD or USD'
      })
    }

    console.log(`[${requestId}] Processing public Square payment for amount: ${paymentAmount} ${paymentCurrency}`)

    // Check if Square SDK is configured
    const squareAccessToken = process.env.SQUARE_ACCESS_TOKEN
    const squareApplicationId = process.env.NEXT_PUBLIC_SQUARE_APPLICATION_ID
    const squareLocationId = process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID
    const squareEnvironment = process.env.SQUARE_ENVIRONMENT || 'sandbox'

    if (!squareAccessToken || !squareApplicationId || !squareLocationId) {
      console.error(`[${requestId}] Square configuration missing`)
      return res.status(500).json({
        error: 'Configuration error',
        message: 'Square payment processing is not properly configured'
      })
    }

    console.log(`[${requestId}] Square configuration loaded:`, {
      environment: squareEnvironment,
      applicationId: squareApplicationId.substring(0, 10) + '...',
      locationId: squareLocationId,
      hasAccessToken: !!squareAccessToken
    })

    try {
      // Process payment using Square API
      const paymentResult = await processSquarePayment(
        token,
        paymentAmount,
        paymentCurrency,
        squareAccessToken,
        squareLocationId,
        squareEnvironment,
        orderDetails
      )

      if (paymentResult.success) {
        console.log(`[${requestId}] Square payment successful: ${paymentResult.paymentId}`)

        return res.status(200).json({
          success: true,
          paymentId: paymentResult.paymentId,
          transactionId: paymentResult.transactionId,
          amount: paymentAmount,
          currency: paymentCurrency,
          status: 'COMPLETED',
          receiptUrl: paymentResult.receiptUrl,
          message: 'Payment processed successfully'
        })
      } else {
        throw new Error(paymentResult.error || 'Payment processing failed')
      }
    } catch (paymentError) {
      console.error(`[${requestId}] Payment processing error:`, paymentError)
      
      return res.status(400).json({
        error: 'Payment failed',
        message: paymentError.message || 'Unable to process payment. Please try again.',
        details: paymentError.details || null
      })
    }
  } catch (error) {
    console.error(`[${requestId}] API error:`, error)
    
    return res.status(500).json({
      error: 'Internal server error',
      message: 'An unexpected error occurred while processing your payment'
    })
  }
}

/**
 * Process Square payment using fetch API
 * This makes direct API calls to Square's payments endpoint
 */
async function processSquarePayment(token, amount, currency, accessToken, locationId, environment, orderDetails) {
  try {
    // Validate token format
    const tokenValidation = validateSquareToken(token)
    console.log('Square token validation:', tokenValidation)

    if (!tokenValidation.isValid) {
      const errorMessage = tokenValidation.error || 'Invalid Square payment token format'
      console.error('Token validation failed:', errorMessage)
      throw new Error(errorMessage)
    }

    // Square API endpoint
    const baseUrl = environment === 'production'
      ? 'https://connect.squareup.com'
      : 'https://connect.squareupsandbox.com'

    const url = `${baseUrl}/v2/payments`

    // Generate idempotency key for payment
    const idempotencyKey = `public_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`

    // Prepare payment request
    const paymentRequest = {
      source_id: token,
      idempotency_key: idempotencyKey,
      amount_money: {
        amount: Math.round(amount * 100), // Convert to cents
        currency: currency.toUpperCase()
      },
      location_id: locationId,
      note: `Public Checkout Payment - ${orderDetails.productName || 'Product Purchase'}`
    }

    console.log('Square payment request:', {
      ...paymentRequest,
      source_id: token.substring(0, 10) + '...'
    })

    // Make API request to Square
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Square-Version': '2023-10-18',
        'User-Agent': 'OceanSoulSparkles-Public/1.0'
      },
      body: JSON.stringify(paymentRequest)
    })

    const responseData = await response.json()

    console.log('Square API response status:', response.status)
    console.log('Square API response data:', JSON.stringify(responseData, null, 2))

    if (!response.ok) {
      console.error('Square API error response:', {
        status: response.status,
        statusText: response.statusText,
        data: responseData
      })

      // Extract error details from Square response
      const errorMessage = responseData.errors?.[0]?.detail || 
                          responseData.errors?.[0]?.code || 
                          'Payment processing failed'
      
      throw new Error(errorMessage)
    }

    // Extract payment details from successful response
    const payment = responseData.payment
    
    return {
      success: true,
      paymentId: payment.id,
      transactionId: payment.order_id || payment.id,
      receiptUrl: payment.receipt_url,
      status: payment.status,
      amount: payment.amount_money.amount / 100, // Convert back from cents
      currency: payment.amount_money.currency
    }
  } catch (error) {
    console.error('Square payment processing error:', error)
    
    return {
      success: false,
      error: error.message || 'Payment processing failed',
      details: error.details || null
    }
  }
}

/**
 * Validate Square payment token format
 */
function validateSquareToken(token) {
  if (!token || typeof token !== 'string') {
    return {
      isValid: false,
      error: 'Token is required and must be a string'
    }
  }

  // Check for valid Square token prefixes
  const validPrefixes = ['sq0idb', 'sq0idp', 'cnon:', 'ccof:', 'sq0ids']
  const hasValidPrefix = validPrefixes.some(prefix => token.startsWith(prefix))

  return {
    isValid: token && token.length > 10,
    hasValidPrefix,
    tokenType: token.substring(0, 6),
    length: token.length
  }
}
