import { supabase } from '@/lib/supabase'; // Use the direct Supabase client
import { authenticateAdminRequest } from '@/lib/admin-auth'; // Re-evaluate if this is best, or adapt

export default async function handler(req, res) {
  // Authenticate the request - this helper might need adjustment
  // or a new one created if it's too admin-specific.
  // For now, assume it provides { authorized, error, user, role }
  // and `user.id` is the authenticated user's ID.
  const authResult = await authenticateAdminRequest(req); // Placeholder for actual auth

  if (!authResult.authorized) {
    return res.status(401).json({ error: authResult.error?.message || 'Unauthorized' });
  }

  const { user, role } = authResult;

  // Ensure user is an artist or braider
  if (role !== 'artist' && role !== 'braider') {
    return res.status(403).json({ error: 'Forbidden: Access restricted to artists and braiders.' });
  }

  const userId = user.id;

  if (req.method === 'GET') {
    try {
      const { data: artistProfile, error: artistProfileError } = await supabase
        .from('artist_profiles')
        .select(`
          display_name,
          bio,
          portfolio_urls,
          specializations,
          skill_level,
          is_available_today,
          artist_name
        `)
        .eq('user_id', userId)
        .single();

      if (artistProfileError && artistProfileError.code !== 'PGRST116') { // PGRST116: row not found
        throw artistProfileError;
      }

      const { data: userProfile, error: userProfileDataError } = await supabase
        .from('user_profiles')
        .select('name, phone, notes') // Include notes if artists should see them
        .eq('id', userId)
        .single();

      if (userProfileDataError && userProfileDataError.code !== 'PGRST116') {
        throw userProfileDataError;
      }

      // Get email from auth.users if not readily available
      // const { data: authUser, error: authUserError } = await supabase.auth.admin.getUserById(userId); // Needs admin client
      // For simplicity, assuming email is in user object from authenticateAdminRequest or not needed for GET display here.
      // If needed: use getAdminClient().auth.admin.getUserById(userId)

      if (!artistProfile && !userProfile) {
         return res.status(404).json({ error: 'Profile not found.' });
      }

      res.status(200).json({
        // Fallback to userProfile.name or artistProfile.artist_name if display_name is null
        display_name: artistProfile?.display_name || userProfile?.name || artistProfile?.artist_name || '',
        bio: artistProfile?.bio || '',
        portfolio_urls: artistProfile?.portfolio_urls || [],
        phone: userProfile?.phone || '',
        // Read-only fields for display, not for PUT:
        email: user.email, // from authResult
        artist_name: artistProfile?.artist_name || userProfile?.name || '',
        specializations: artistProfile?.specializations || [],
        skill_level: artistProfile?.skill_level || '',
        is_available_today: artistProfile?.is_available_today,
        notes: userProfile?.notes // if artists should see their own notes
      });

    } catch (error) {
      console.error('Error fetching artist profile:', error);
      res.status(500).json({ error: 'Failed to fetch profile', details: error.message });
    }
  } else if (req.method === 'PUT') {
    try {
      const { display_name, bio, portfolio_urls, phone } = req.body;

      // Validate input (basic example)
      if (display_name && typeof display_name !== 'string' || display_name.length > 100) {
        return res.status(400).json({ error: 'Invalid display name.' });
      }
      if (bio && typeof bio !== 'string' || bio.length > 2000) {
        return res.status(400).json({ error: 'Bio is too long.' });
      }
      if (portfolio_urls && !Array.isArray(portfolio_urls)) { // Basic check, could be more robust
          return res.status(400).json({ error: 'Portfolio URLs must be an array of strings.' });
      }
      if (phone && (typeof phone !== 'string' || phone.length > 20)) {
           return res.status(400).json({ error: 'Invalid phone number.' });
      }

      let userProfileName = user.email; // Default to email if no other name source
      const { data: existingUserProfile, error: existingUserProfileError } = await supabase
        .from('user_profiles')
        .select('name')
        .eq('id', userId)
        .single();

      if (existingUserProfileError && existingUserProfileError.code !== 'PGRST116') {
        // Log error but don't necessarily fail if other updates can proceed
        console.error('Error fetching existing user_profile name:', existingUserProfileError.message);
      } else if (existingUserProfile) {
        userProfileName = existingUserProfile.name || user.email;
      }


      // Update artist_profiles table
      let updatedArtistProfile;
      const { data: artistProfileData, error: artistProfileUpdateError } = await supabase
        .from('artist_profiles')
        .update({
          display_name: display_name,
          bio: bio,
          portfolio_urls: portfolio_urls,
        })
        .eq('user_id', userId)
        .select()
        .single();

      if (artistProfileUpdateError) {
        if (artistProfileUpdateError.code === 'PGRST116') { // Row not found, create it
          const { data: createdArtistProfile, error: artistProfileCreateError } = await supabase
            .from('artist_profiles')
            .insert({
              user_id: userId,
              display_name: display_name,
              bio: bio,
              portfolio_urls: portfolio_urls,
              artist_name: display_name || userProfileName // Use display_name or fallback
            })
            .select()
            .single();
          if (artistProfileCreateError) throw artistProfileCreateError;
          updatedArtistProfile = createdArtistProfile;
        } else {
          throw artistProfileUpdateError;
        }
      } else {
        updatedArtistProfile = artistProfileData;
      }

      // Update user_profiles table for phone and potentially name if display_name is the primary source
      let updatedUserProfileData = {};
      const userProfileUpdates = {};
      if (phone !== undefined) userProfileUpdates.phone = phone;
      // If display_name is considered the primary name and should sync to user_profiles.name
      if (display_name !== undefined && (!existingUserProfile || existingUserProfile.name !== display_name)) {
        userProfileUpdates.name = display_name;
      }


      if (Object.keys(userProfileUpdates).length > 0) {
        const { data: updatedUP, error: userProfileUpdateError } = await supabase
          .from('user_profiles')
          .update(userProfileUpdates)
          .eq('id', userId)
          .select('phone, name') // Select the fields that were potentially updated
          .single();

        if (userProfileUpdateError) {
            if (userProfileUpdateError.code === 'PGRST116') { // Row not found, create it
                 const { data: createdUserProfile, error: userProfileCreateError } = await supabase
                    .from('user_profiles')
                    .insert({
                        id: userId,
                        name: display_name || user.email, // Use display_name or fallback to email
                        phone: phone
                    })
                    .select('phone, name')
                    .single();
                if (userProfileCreateError) console.error('Error creating user_profile entry during PUT:', userProfileCreateError.message);
                else updatedUserProfileData = createdUserProfile;
            } else {
                console.error('Error updating user_profile:', userProfileUpdateError.message);
            }
        } else {
            updatedUserProfileData = updatedUP;
        }
      }


      res.status(200).json({
        message: 'Profile updated successfully',
        artist_profile: updatedArtistProfile || {},
        user_profile: updatedUserProfileData
      });

    } catch (error) {
      console.error('Error updating artist profile:', error);
      res.status(500).json({ error: 'Failed to update profile', details: error.message });
    }
  } else {
    res.setHeader('Allow', ['GET', 'PUT']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
