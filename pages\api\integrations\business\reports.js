/**
 * Business Reports API Endpoint for Ocean Soul Sparkles
 * Provides financial and marketing reports from business integrations
 * 
 * Phase 7.4: Business Management Integrations
 */

import { authenticateAdminRequest } from '@/lib/admin-auth'
import { integrationRateLimit } from '@/lib/integrations/rate-limiter'
import { RequestValidator, AuditLogger } from '@/lib/integrations/security-utils'
import BusinessManager from '@/lib/integrations/business-manager'

/**
 * Business Reports Handler
 * GET /api/integrations/business/reports - Get financial and marketing reports
 */
export default async function handler(req, res) {
  // Apply rate limiting
  await new Promise((resolve, reject) => {
    integrationRateLimit(req, res, (error) => {
      if (error) reject(error)
      else resolve()
    })
  })

  const startTime = Date.now()

  try {
    // Validate request method
    RequestValidator.validateEndpointAccess(req, ['GET'])

    // Authenticate user
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.success) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      })
    }

    const { user } = authResult
    const userId = user.id

    // Get query parameters
    const { 
      startDate = getDefaultStartDate(),
      endDate = getDefaultEndDate(),
      reportType = 'all'
    } = req.query

    // Validate date parameters
    if (!isValidDate(startDate) || !isValidDate(endDate)) {
      return res.status(400).json({
        error: 'Invalid date format',
        message: 'Dates must be in YYYY-MM-DD format'
      })
    }

    if (new Date(startDate) > new Date(endDate)) {
      return res.status(400).json({
        error: 'Invalid date range',
        message: 'Start date must be before end date'
      })
    }

    // Initialize business manager
    const businessManager = new BusinessManager(userId)

    const reports = {}

    // Get financial reports if requested
    if (reportType === 'all' || reportType === 'financial') {
      try {
        const financialReports = await businessManager.getFinancialReports(startDate, endDate)
        reports.financial = financialReports
      } catch (error) {
        console.error('Failed to get financial reports:', error)
        reports.financial = { error: error.message }
      }
    }

    // Get marketing analytics if requested
    if (reportType === 'all' || reportType === 'marketing') {
      try {
        const marketingAnalytics = await businessManager.getMarketingAnalytics()
        reports.marketing = marketingAnalytics
      } catch (error) {
        console.error('Failed to get marketing analytics:', error)
        reports.marketing = { error: error.message }
      }
    }

    // Calculate summary metrics
    const summary = calculateSummaryMetrics(reports, startDate, endDate)

    await AuditLogger.logApiAccess(
      userId,
      req.url,
      req.method,
      200,
      Date.now() - startTime,
      { 
        action: 'get_business_reports',
        reportType,
        startDate,
        endDate,
        reportCount: Object.keys(reports).length
      }
    )

    return res.status(200).json({
      success: true,
      reports,
      summary,
      dateRange: {
        startDate,
        endDate
      }
    })

  } catch (error) {
    console.error('Business reports error:', error)

    await AuditLogger.logApiAccess(
      req.user?.id || null,
      req.url,
      req.method,
      500,
      Date.now() - startTime,
      { error: error.message }
    )

    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to get business reports'
    })
  }
}

/**
 * Get default start date (30 days ago)
 */
function getDefaultStartDate() {
  const date = new Date()
  date.setDate(date.getDate() - 30)
  return date.toISOString().split('T')[0]
}

/**
 * Get default end date (today)
 */
function getDefaultEndDate() {
  return new Date().toISOString().split('T')[0]
}

/**
 * Validate date format (YYYY-MM-DD)
 */
function isValidDate(dateString) {
  const regex = /^\d{4}-\d{2}-\d{2}$/
  if (!regex.test(dateString)) return false
  
  const date = new Date(dateString)
  return date instanceof Date && !isNaN(date)
}

/**
 * Calculate summary metrics from reports
 */
function calculateSummaryMetrics(reports, startDate, endDate) {
  const summary = {
    financial: {
      totalRevenue: 0,
      totalExpenses: 0,
      netProfit: 0,
      invoiceCount: 0,
      expenseCount: 0
    },
    marketing: {
      totalCampaigns: 0,
      totalSubscribers: 0,
      averageOpenRate: 0,
      averageClickRate: 0,
      totalEmailsSent: 0
    },
    dateRange: {
      startDate,
      endDate,
      dayCount: Math.ceil((new Date(endDate) - new Date(startDate)) / (1000 * 60 * 60 * 24))
    }
  }

  // Process financial reports
  if (reports.financial && !reports.financial.error) {
    for (const [provider, data] of Object.entries(reports.financial)) {
      if (data.error) continue

      // Process QuickBooks data
      if (provider === 'quickbooks') {
        if (data.profitLoss) {
          // Extract revenue and expense data from profit & loss report
          // This would need to be customized based on actual QuickBooks API response structure
          summary.financial.totalRevenue += extractRevenueFromProfitLoss(data.profitLoss)
          summary.financial.totalExpenses += extractExpensesFromProfitLoss(data.profitLoss)
        }

        if (data.salesReport) {
          // Extract invoice count from sales report
          summary.financial.invoiceCount += extractInvoiceCountFromSalesReport(data.salesReport)
        }
      }
    }

    summary.financial.netProfit = summary.financial.totalRevenue - summary.financial.totalExpenses
  }

  // Process marketing analytics
  if (reports.marketing && !reports.marketing.error) {
    let totalOpenRate = 0
    let totalClickRate = 0
    let campaignCount = 0

    for (const [provider, data] of Object.entries(reports.marketing)) {
      if (data.error) continue

      // Process Mailchimp data
      if (provider === 'mailchimp') {
        if (data.campaigns) {
          summary.marketing.totalCampaigns += data.campaigns.length
        }

        if (data.campaignReports) {
          for (const report of data.campaignReports) {
            if (report.opens && report.clicks) {
              totalOpenRate += (report.opens.open_rate || 0)
              totalClickRate += (report.clicks.click_rate || 0)
              campaignCount++
              summary.marketing.totalEmailsSent += (report.emails_sent || 0)
            }
          }
        }
      }
    }

    if (campaignCount > 0) {
      summary.marketing.averageOpenRate = totalOpenRate / campaignCount
      summary.marketing.averageClickRate = totalClickRate / campaignCount
    }
  }

  return summary
}

/**
 * Extract revenue from QuickBooks Profit & Loss report
 */
function extractRevenueFromProfitLoss(profitLossData) {
  // This would need to be implemented based on actual QuickBooks API response structure
  // For now, return 0 as placeholder
  return 0
}

/**
 * Extract expenses from QuickBooks Profit & Loss report
 */
function extractExpensesFromProfitLoss(profitLossData) {
  // This would need to be implemented based on actual QuickBooks API response structure
  // For now, return 0 as placeholder
  return 0
}

/**
 * Extract invoice count from QuickBooks Sales report
 */
function extractInvoiceCountFromSalesReport(salesReportData) {
  // This would need to be implemented based on actual QuickBooks API response structure
  // For now, return 0 as placeholder
  return 0
}

/**
 * API Route Configuration
 */
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
}
