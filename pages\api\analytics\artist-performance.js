import { supabase } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';
import { ArtistPerformanceAnalyzer } from '@/lib/analytics/performance-metrics';

/**
 * Artist Performance Analytics API Endpoint
 * Provides comprehensive performance metrics for individual artists
 */
export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed' 
    });
  }

  try {
    // Authenticate request
    const authResult = await authenticateAdminRequest(req);
    if (!authResult.success) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const { artistId, timeframe = 'monthly', startDate, endDate } = req.query;

    // Validate required parameters
    if (!artistId) {
      return res.status(400).json({
        success: false,
        error: 'Artist ID is required'
      });
    }

    // Calculate date range based on timeframe
    const dateRange = calculateDateRange(timeframe, startDate, endDate);

    // Get artist data
    const artistData = await getArtistData(artistId);
    if (!artistData) {
      return res.status(404).json({
        success: false,
        error: 'Artist not found'
      });
    }

    // Get booking data
    const bookingData = await getBookingData(artistId, dateRange);

    // Get revenue data
    const revenueData = await getRevenueData(artistId, dateRange);

    // Calculate performance metrics
    const analyzer = new ArtistPerformanceAnalyzer();
    const performanceMetrics = analyzer.calculateArtistMetrics(
      artistData,
      bookingData,
      revenueData,
      timeframe
    );

    // Add additional insights
    const insights = await generatePerformanceInsights(performanceMetrics, bookingData, revenueData);

    return res.status(200).json({
      success: true,
      data: {
        ...performanceMetrics,
        insights,
        dateRange,
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Artist performance analytics error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to generate artist performance analytics: ' + error.message
    });
  }
}

/**
 * Calculate date range based on timeframe
 */
function calculateDateRange(timeframe, startDate, endDate) {
  const now = new Date();
  let start, end;

  if (startDate && endDate) {
    start = new Date(startDate);
    end = new Date(endDate);
  } else {
    end = now;
    
    switch (timeframe) {
      case 'weekly':
        start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'monthly':
        start = new Date(now.getFullYear(), now.getMonth() - 6, 1); // Last 6 months
        break;
      case 'quarterly':
        start = new Date(now.getFullYear() - 1, now.getMonth(), 1); // Last year
        break;
      case 'yearly':
        start = new Date(now.getFullYear() - 2, 0, 1); // Last 2 years
        break;
      default:
        start = new Date(now.getFullYear(), now.getMonth() - 6, 1);
    }
  }

  return {
    start: start.toISOString(),
    end: end.toISOString(),
    timeframe
  };
}

/**
 * Get artist data from database
 */
async function getArtistData(artistId) {
  try {
    const { data: artist, error } = await supabase
      .from('user_profiles')
      .select(`
        id,
        name,
        email,
        created_at,
        user_roles!inner(role)
      `)
      .eq('id', artistId)
      .eq('user_roles.role', 'artist')
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Artist not found
      }
      throw error;
    }

    return {
      id: artist.id,
      name: artist.name,
      email: artist.email,
      joinedDate: artist.created_at,
      role: artist.user_roles?.role || 'artist'
    };

  } catch (error) {
    console.error('Error fetching artist data:', error);
    throw error;
  }
}

/**
 * Get booking data for artist within date range
 */
async function getBookingData(artistId, dateRange) {
  try {
    const { data: bookings, error } = await supabase
      .from('bookings')
      .select(`
        id,
        start_time,
        end_time,
        status,
        total_amount,
        customer_id,
        service_id,
        location,
        notes,
        created_at,
        services(
          id,
          name,
          duration,
          price
        ),
        user_profiles!bookings_customer_id_fkey(
          id,
          name,
          email
        ),
        booking_reviews(
          rating,
          comment
        )
      `)
      .eq('artist_id', artistId)
      .gte('start_time', dateRange.start)
      .lte('start_time', dateRange.end)
      .order('start_time', { ascending: true });

    if (error) throw error;

    // Transform booking data for analysis
    return bookings.map(booking => ({
      id: booking.id,
      date: booking.start_time,
      startTime: booking.start_time,
      endTime: booking.end_time,
      status: booking.status,
      amount: booking.total_amount || 0,
      customerId: booking.customer_id,
      serviceId: booking.service_id,
      service: booking.services?.name || 'Unknown Service',
      duration: booking.services?.duration || 1,
      location: booking.location,
      rating: booking.booking_reviews?.[0]?.rating,
      customerName: booking.user_profiles?.name || 'Unknown Customer',
      createdAt: booking.created_at
    }));

  } catch (error) {
    console.error('Error fetching booking data:', error);
    throw error;
  }
}

/**
 * Get revenue data for artist within date range
 */
async function getRevenueData(artistId, dateRange) {
  try {
    // Get revenue from completed bookings
    const { data: bookingRevenue, error: bookingError } = await supabase
      .from('bookings')
      .select(`
        start_time,
        total_amount,
        service_id,
        services(name)
      `)
      .eq('artist_id', artistId)
      .eq('status', 'completed')
      .gte('start_time', dateRange.start)
      .lte('start_time', dateRange.end)
      .order('start_time', { ascending: true });

    if (bookingError) throw bookingError;

    // Get additional revenue from payments table if it exists
    const { data: paymentRevenue, error: paymentError } = await supabase
      .from('payments')
      .select(`
        created_at,
        amount,
        status,
        booking_id,
        bookings!inner(
          artist_id,
          services(name)
        )
      `)
      .eq('bookings.artist_id', artistId)
      .eq('status', 'completed')
      .gte('created_at', dateRange.start)
      .lte('created_at', dateRange.end)
      .order('created_at', { ascending: true });

    // If payments table doesn't exist or has no data, use booking revenue
    const revenueData = paymentRevenue && paymentRevenue.length > 0 
      ? paymentRevenue.map(payment => ({
          date: payment.created_at,
          amount: payment.amount || 0,
          service: payment.bookings?.services?.name || 'Unknown Service',
          source: 'payment'
        }))
      : bookingRevenue.map(booking => ({
          date: booking.start_time,
          amount: booking.total_amount || 0,
          service: booking.services?.name || 'Unknown Service',
          source: 'booking'
        }));

    return revenueData;

  } catch (error) {
    console.error('Error fetching revenue data:', error);
    // Return booking-based revenue as fallback
    try {
      const { data: fallbackRevenue, error: fallbackError } = await supabase
        .from('bookings')
        .select(`
          start_time,
          total_amount,
          services(name)
        `)
        .eq('artist_id', artistId)
        .eq('status', 'completed')
        .gte('start_time', dateRange.start)
        .lte('start_time', dateRange.end);

      if (fallbackError) throw fallbackError;

      return fallbackRevenue.map(booking => ({
        date: booking.start_time,
        amount: booking.total_amount || 0,
        service: booking.services?.name || 'Unknown Service',
        source: 'booking'
      }));

    } catch (fallbackError) {
      console.error('Error fetching fallback revenue data:', fallbackError);
      return [];
    }
  }
}

/**
 * Generate additional performance insights
 */
async function generatePerformanceInsights(metrics, bookingData, revenueData) {
  const insights = {
    recommendations: [],
    alerts: [],
    opportunities: [],
    benchmarks: {}
  };

  // Performance recommendations
  if (metrics.performance?.utilizationRate < 50) {
    insights.recommendations.push({
      type: 'utilization',
      priority: 'high',
      message: 'Low utilization rate detected. Consider adjusting availability or marketing efforts.',
      action: 'Increase marketing or adjust schedule'
    });
  }

  if (metrics.bookings?.cancellationRate > 15) {
    insights.recommendations.push({
      type: 'cancellation',
      priority: 'medium',
      message: 'High cancellation rate may indicate scheduling or communication issues.',
      action: 'Review booking confirmation process'
    });
  }

  if (metrics.quality?.averageRating < 4.0) {
    insights.recommendations.push({
      type: 'quality',
      priority: 'high',
      message: 'Customer satisfaction below optimal level.',
      action: 'Focus on service quality improvement'
    });
  }

  // Growth opportunities
  if (metrics.performance?.repeatCustomerRate < 30) {
    insights.opportunities.push({
      type: 'retention',
      message: 'Low repeat customer rate indicates opportunity for loyalty programs.',
      potential: 'Implement customer retention strategies'
    });
  }

  if (metrics.revenue?.growthRate > 20) {
    insights.opportunities.push({
      type: 'expansion',
      message: 'Strong growth trend indicates potential for service expansion.',
      potential: 'Consider adding new services or increasing capacity'
    });
  }

  // Performance alerts
  const recentRevenue = revenueData.slice(-7); // Last 7 data points
  const avgRecentRevenue = recentRevenue.reduce((sum, r) => sum + r.amount, 0) / recentRevenue.length;
  
  if (avgRecentRevenue < metrics.revenue?.average * 0.7) {
    insights.alerts.push({
      type: 'revenue_decline',
      severity: 'warning',
      message: 'Recent revenue below average performance.',
      value: avgRecentRevenue
    });
  }

  // Benchmarks (these would typically come from industry data)
  insights.benchmarks = {
    utilizationRate: {
      industry: 65,
      artist: metrics.performance?.utilizationRate || 0,
      status: (metrics.performance?.utilizationRate || 0) >= 65 ? 'above' : 'below'
    },
    repeatCustomerRate: {
      industry: 40,
      artist: metrics.performance?.repeatCustomerRate || 0,
      status: (metrics.performance?.repeatCustomerRate || 0) >= 40 ? 'above' : 'below'
    },
    averageRating: {
      industry: 4.2,
      artist: metrics.quality?.averageRating || 0,
      status: (metrics.quality?.averageRating || 0) >= 4.2 ? 'above' : 'below'
    }
  };

  return insights;
}
