/**
 * Multi-Service Booking API - Phase 8: Advanced Customer Experience
 * Ocean Soul Sparkles - Advanced Booking Features
 */

import { getAdminClient } from '@/lib/supabase'
import { getCurrentUser } from '@/lib/auth'
import { sendBookingNotification } from '@/lib/notifications-server'
import { v4 as uuidv4 } from 'uuid'

export default async function handler(req, res) {
  if (req.method === 'POST') {
    return handleCreateMultiBooking(req, res)
  } else if (req.method === 'GET') {
    return handleGetMultiBookings(req, res)
  } else {
    res.setHeader('Allow', ['POST', 'GET'])
    return res.status(405).json({ error: 'Method not allowed' })
  }
}

/**
 * Create a new multi-service booking
 */
async function handleCreateMultiBooking(req, res) {
  try {
    // Get authenticated user
    const user = await getCurrentUser(req)
    if (!user) {
      return res.status(401).json({ error: 'Authentication required' })
    }

    const {
      customer_id,
      services,
      total_amount,
      discount_amount = 0,
      final_amount,
      booking_date,
      notes = ''
    } = req.body

    // Validate required fields
    if (!customer_id || !services || !Array.isArray(services) || services.length === 0) {
      return res.status(400).json({ 
        error: 'Missing required fields: customer_id, services' 
      })
    }

    // Validate that user can book for this customer
    if (user.id !== customer_id && user.role !== 'admin') {
      return res.status(403).json({ error: 'Not authorized to book for this customer' })
    }

    // Validate services data
    for (const service of services) {
      if (!service.service_id || !service.start_time || !service.end_time || !service.service_amount) {
        return res.status(400).json({ 
          error: 'Each service must have service_id, start_time, end_time, and service_amount' 
        })
      }
    }

    const adminClient = getAdminClient()
    if (!adminClient) {
      return res.status(500).json({ error: 'Database connection failed' })
    }

    // Generate unique booking session ID
    const bookingSessionId = uuidv4()

    // Start transaction
    const { data: multiBooking, error: multiBookingError } = await adminClient
      .from('customer_multi_bookings')
      .insert([{
        customer_id,
        booking_session_id: bookingSessionId,
        total_amount: parseFloat(total_amount),
        discount_amount: parseFloat(discount_amount),
        final_amount: parseFloat(final_amount),
        status: 'pending',
        notes
      }])
      .select()
      .single()

    if (multiBookingError) {
      console.error('Error creating multi-booking:', multiBookingError)
      return res.status(500).json({ error: 'Failed to create multi-booking' })
    }

    // Create individual service bookings
    const serviceBookings = []
    const individualBookings = []

    for (const service of services) {
      // Create individual booking record
      const { data: booking, error: bookingError } = await adminClient
        .from('bookings')
        .insert([{
          customer_id,
          service_id: service.service_id,
          artist_id: service.artist_id,
          start_time: service.start_time,
          end_time: service.end_time,
          status: 'confirmed',
          location: service.location || 'Studio',
          notes: service.notes || '',
          booking_session_id: bookingSessionId
        }])
        .select(`
          *,
          service:services(*),
          artist:artist_profiles(*),
          customer:customers(*)
        `)
        .single()

      if (bookingError) {
        console.error('Error creating individual booking:', bookingError)
        // Rollback multi-booking if individual booking fails
        await adminClient
          .from('customer_multi_bookings')
          .delete()
          .eq('id', multiBooking.id)
        
        return res.status(500).json({ error: 'Failed to create service booking' })
      }

      individualBookings.push(booking)

      // Create multi-booking service record
      const { data: multiBookingService, error: serviceError } = await adminClient
        .from('multi_booking_services')
        .insert([{
          multi_booking_id: multiBooking.id,
          service_id: service.service_id,
          artist_id: service.artist_id,
          start_time: service.start_time,
          end_time: service.end_time,
          service_amount: parseFloat(service.service_amount),
          status: 'confirmed',
          notes: service.notes || ''
        }])
        .select()
        .single()

      if (serviceError) {
        console.error('Error creating multi-booking service:', serviceError)
        return res.status(500).json({ error: 'Failed to create service record' })
      }

      serviceBookings.push(multiBookingService)
    }

    // Send notifications for each booking
    for (const booking of individualBookings) {
      try {
        await sendBookingNotification({
          bookingId: booking.id,
          customerId: booking.customer_id,
          customerName: booking.customer.name,
          customerEmail: booking.customer.email,
          serviceName: booking.service.name,
          startTime: booking.start_time,
          endTime: booking.end_time,
          location: booking.location,
          status: 'confirmed',
          artistId: booking.artist_id,
          isMultiBooking: true,
          multiBookingId: multiBooking.id
        })
      } catch (notificationError) {
        console.error('Error sending booking notification:', notificationError)
        // Don't fail the booking if notification fails
      }
    }

    // Update customer booking count and last booking date
    await adminClient
      .from('customers')
      .update({
        booking_count: adminClient.raw('booking_count + ?', [services.length]),
        last_booking_date: new Date().toISOString()
      })
      .eq('id', customer_id)

    // Award loyalty points if customer has loyalty program
    try {
      const pointsToAward = Math.floor(parseFloat(final_amount) / 10) // 1 point per $10 spent
      
      if (pointsToAward > 0) {
        await adminClient.rpc('award_loyalty_points', {
          customer_id,
          points_amount: pointsToAward,
          transaction_description: `Multi-service booking #${multiBooking.id}`,
          booking_id: multiBooking.id
        })
      }
    } catch (loyaltyError) {
      console.error('Error awarding loyalty points:', loyaltyError)
      // Don't fail booking if loyalty points fail
    }

    // Return complete booking data
    const completeBooking = {
      ...multiBooking,
      services: serviceBookings,
      individual_bookings: individualBookings
    }

    return res.status(201).json({
      success: true,
      message: 'Multi-service booking created successfully',
      booking: completeBooking
    })

  } catch (error) {
    console.error('Error in multi-booking creation:', error)
    return res.status(500).json({ 
      error: 'Internal server error',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

/**
 * Get multi-service bookings for a customer
 */
async function handleGetMultiBookings(req, res) {
  try {
    // Get authenticated user
    const user = await getCurrentUser(req)
    if (!user) {
      return res.status(401).json({ error: 'Authentication required' })
    }

    const { customer_id, status, limit = 10, offset = 0 } = req.query

    // Validate customer access
    const targetCustomerId = customer_id || user.id
    if (user.id !== targetCustomerId && user.role !== 'admin') {
      return res.status(403).json({ error: 'Not authorized to view these bookings' })
    }

    const adminClient = getAdminClient()
    if (!adminClient) {
      return res.status(500).json({ error: 'Database connection failed' })
    }

    // Build query
    let query = adminClient
      .from('customer_multi_bookings')
      .select(`
        *,
        services:multi_booking_services(
          *,
          service:services(*),
          artist:artist_profiles(*)
        ),
        customer:customers(*)
      `)
      .eq('customer_id', targetCustomerId)
      .order('created_at', { ascending: false })

    // Apply filters
    if (status) {
      query = query.eq('status', status)
    }

    // Apply pagination
    query = query.range(parseInt(offset), parseInt(offset) + parseInt(limit) - 1)

    const { data: multiBookings, error } = await query

    if (error) {
      console.error('Error fetching multi-bookings:', error)
      return res.status(500).json({ error: 'Failed to fetch bookings' })
    }

    return res.status(200).json({
      success: true,
      data: multiBookings || [],
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset),
        total: multiBookings?.length || 0
      }
    })

  } catch (error) {
    console.error('Error in multi-booking retrieval:', error)
    return res.status(500).json({ 
      error: 'Internal server error',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}
