/**
 * Security Utilities for Ocean Soul Sparkles Integrations
 * Provides security functions for third-party integrations
 * 
 * Phase 7: Advanced Integrations & Ecosystem
 */

import crypto from 'crypto'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

/**
 * Security Configuration
 */
const SECURITY_CONFIG = {
  maxRequestsPerMinute: 60,
  maxRequestsPerHour: 1000,
  tokenExpiryBuffer: 300000, // 5 minutes
  maxRetries: 3,
  retryDelay: 1000, // 1 second
  webhookSignatureAlgorithm: 'sha256',
  allowedOrigins: [
    process.env.NEXT_PUBLIC_SITE_URL,
    'https://oceansoulsparkles.com.au',
    'https://www.oceansoulsparkles.com.au'
  ]
}

/**
 * Rate Limiter Class
 * Implements token bucket algorithm for API rate limiting
 */
export class RateLimiter {
  constructor() {
    this.buckets = new Map()
  }

  /**
   * Check if request is allowed based on rate limits
   */
  async isAllowed(identifier, maxRequests = SECURITY_CONFIG.maxRequestsPerMinute, windowMs = 60000) {
    const now = Date.now()
    const windowStart = now - windowMs
    
    // Get or create bucket for identifier
    if (!this.buckets.has(identifier)) {
      this.buckets.set(identifier, [])
    }
    
    const bucket = this.buckets.get(identifier)
    
    // Remove old requests outside the window
    const validRequests = bucket.filter(timestamp => timestamp > windowStart)
    
    // Check if under limit
    if (validRequests.length >= maxRequests) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: Math.min(...validRequests) + windowMs
      }
    }
    
    // Add current request
    validRequests.push(now)
    this.buckets.set(identifier, validRequests)
    
    return {
      allowed: true,
      remaining: maxRequests - validRequests.length,
      resetTime: now + windowMs
    }
  }

  /**
   * Clean up old buckets to prevent memory leaks
   */
  cleanup() {
    const now = Date.now()
    const maxAge = 3600000 // 1 hour
    
    for (const [identifier, bucket] of this.buckets.entries()) {
      const validRequests = bucket.filter(timestamp => timestamp > now - maxAge)
      
      if (validRequests.length === 0) {
        this.buckets.delete(identifier)
      } else {
        this.buckets.set(identifier, validRequests)
      }
    }
  }
}

/**
 * Request Validator Class
 * Validates and sanitizes integration requests
 */
export class RequestValidator {
  /**
   * Validate OAuth callback request
   */
  static validateOAuthCallback(req) {
    const { code, state, error } = req.query
    
    if (error) {
      throw new SecurityError(`OAuth error: ${error}`, 'OAUTH_ERROR')
    }
    
    if (!code) {
      throw new SecurityError('Missing authorization code', 'MISSING_CODE')
    }
    
    if (!state) {
      throw new SecurityError('Missing state parameter', 'MISSING_STATE')
    }
    
    // Validate state format
    if (!this.isValidBase64Url(state)) {
      throw new SecurityError('Invalid state format', 'INVALID_STATE')
    }
    
    return { code, state }
  }

  /**
   * Validate webhook request
   */
  static validateWebhookRequest(req, expectedSignature, secret) {
    const body = JSON.stringify(req.body)
    const signature = this.generateWebhookSignature(body, secret)
    
    if (!this.verifySignature(signature, expectedSignature)) {
      throw new SecurityError('Invalid webhook signature', 'INVALID_SIGNATURE')
    }
    
    return true
  }

  /**
   * Sanitize user input
   */
  static sanitizeInput(input) {
    if (typeof input !== 'string') {
      return input
    }
    
    return input
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/javascript:/gi, '') // Remove javascript: URLs
      .replace(/on\w+=/gi, '') // Remove event handlers
      .trim()
  }

  /**
   * Validate API endpoint access
   */
  static validateEndpointAccess(req, allowedMethods = ['GET', 'POST']) {
    if (!allowedMethods.includes(req.method)) {
      throw new SecurityError(`Method ${req.method} not allowed`, 'METHOD_NOT_ALLOWED')
    }
    
    const origin = req.headers.origin
    if (origin && !SECURITY_CONFIG.allowedOrigins.includes(origin)) {
      throw new SecurityError(`Origin ${origin} not allowed`, 'ORIGIN_NOT_ALLOWED')
    }
    
    return true
  }

  /**
   * Check if string is valid base64url
   */
  static isValidBase64Url(str) {
    try {
      const decoded = Buffer.from(str, 'base64url').toString()
      return decoded.length > 0
    } catch {
      return false
    }
  }

  /**
   * Generate webhook signature
   */
  static generateWebhookSignature(body, secret) {
    return crypto
      .createHmac(SECURITY_CONFIG.webhookSignatureAlgorithm, secret)
      .update(body)
      .digest('hex')
  }

  /**
   * Verify webhook signature
   */
  static verifySignature(expected, received) {
    if (!expected || !received) {
      return false
    }
    
    // Use timing-safe comparison
    return crypto.timingSafeEqual(
      Buffer.from(expected, 'hex'),
      Buffer.from(received.replace('sha256=', ''), 'hex')
    )
  }
}

/**
 * Audit Logger Class
 * Logs security events and integration activities
 */
export class AuditLogger {
  /**
   * Log security event
   */
  static async logSecurityEvent(userId, event, details = {}, severity = 'info') {
    const logEntry = {
      user_id: userId,
      event_type: 'security',
      event_name: event,
      severity,
      details: {
        ...details,
        timestamp: new Date().toISOString(),
        userAgent: details.userAgent || 'unknown',
        ipAddress: details.ipAddress || 'unknown'
      },
      created_at: new Date().toISOString()
    }

    try {
      const { error } = await supabase
        .from('security_logs')
        .insert(logEntry)

      if (error) {
        console.error('Failed to log security event:', error)
      }
    } catch (error) {
      console.error('Audit logging error:', error)
    }
  }

  /**
   * Log integration activity
   */
  static async logIntegrationActivity(userId, provider, action, status, details = {}) {
    const logEntry = {
      user_id: userId,
      provider,
      action,
      status,
      details: {
        ...details,
        timestamp: new Date().toISOString()
      },
      created_at: new Date().toISOString()
    }

    try {
      const { error } = await supabase
        .from('integration_logs')
        .insert(logEntry)

      if (error) {
        console.error('Failed to log integration activity:', error)
      }
    } catch (error) {
      console.error('Integration logging error:', error)
    }
  }

  /**
   * Log API access
   */
  static async logApiAccess(userId, endpoint, method, status, responseTime, details = {}) {
    const logEntry = {
      user_id: userId,
      endpoint,
      method,
      status,
      response_time: responseTime,
      details: {
        ...details,
        timestamp: new Date().toISOString()
      },
      created_at: new Date().toISOString()
    }

    try {
      const { error } = await supabase
        .from('api_access_logs')
        .insert(logEntry)

      if (error) {
        console.error('Failed to log API access:', error)
      }
    } catch (error) {
      console.error('API access logging error:', error)
    }
  }
}

/**
 * Security Error Class
 * Custom error class for security-related issues
 */
export class SecurityError extends Error {
  constructor(message, code, statusCode = 400) {
    super(message)
    this.name = 'SecurityError'
    this.code = code
    this.statusCode = statusCode
  }
}

/**
 * Token Manager Class
 * Manages token lifecycle and security
 */
export class TokenManager {
  /**
   * Check if token needs refresh
   */
  static needsRefresh(expiresAt, bufferMs = SECURITY_CONFIG.tokenExpiryBuffer) {
    if (!expiresAt) return false
    
    const expiryTime = new Date(expiresAt).getTime()
    const now = Date.now()
    
    return (expiryTime - now) <= bufferMs
  }

  /**
   * Generate secure random token
   */
  static generateSecureToken(length = 32) {
    return crypto.randomBytes(length).toString('hex')
  }

  /**
   * Hash token for storage
   */
  static hashToken(token) {
    return crypto
      .createHash('sha256')
      .update(token)
      .digest('hex')
  }

  /**
   * Verify token hash
   */
  static verifyToken(token, hash) {
    const tokenHash = this.hashToken(token)
    return crypto.timingSafeEqual(
      Buffer.from(tokenHash, 'hex'),
      Buffer.from(hash, 'hex')
    )
  }
}

/**
 * Retry Handler Class
 * Handles retries for failed requests with exponential backoff
 */
export class RetryHandler {
  /**
   * Execute function with retry logic
   */
  static async withRetry(fn, maxRetries = SECURITY_CONFIG.maxRetries, baseDelay = SECURITY_CONFIG.retryDelay) {
    let lastError
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await fn()
      } catch (error) {
        lastError = error
        
        if (attempt === maxRetries) {
          break
        }
        
        // Don't retry on certain errors
        if (error.statusCode === 401 || error.statusCode === 403) {
          break
        }
        
        // Exponential backoff with jitter
        const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 1000
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
    
    throw lastError
  }
}

// Export singleton instances
export const rateLimiter = new RateLimiter()

// Cleanup rate limiter buckets every hour
setInterval(() => {
  rateLimiter.cleanup()
}, 3600000)

export {
  SECURITY_CONFIG
}
