/**
 * Social Media Disconnect API Endpoint for Ocean Soul Sparkles
 * Handles disconnection of social media integrations
 * 
 * Phase 7.3: Social Media Integration Layer
 */

import { authenticateAdminRequest } from '@/lib/admin-auth'
import { integrationRateLimit } from '@/lib/integrations/rate-limiter'
import { RequestV<PERSON>da<PERSON>, AuditLogger } from '@/lib/integrations/security-utils'
import oauthManager from '@/lib/integrations/oauth-manager'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

/**
 * Social Media Disconnect Handler
 * POST /api/integrations/social/disconnect - Disconnect social media integration
 */
export default async function handler(req, res) {
  // Apply rate limiting
  await new Promise((resolve, reject) => {
    integrationRateLimit(req, res, (error) => {
      if (error) reject(error)
      else resolve()
    })
  })

  const startTime = Date.now()

  try {
    // Validate request method
    RequestValidator.validateEndpointAccess(req, ['POST'])

    // Authenticate user
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.success) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      })
    }

    const { user } = authResult
    const userId = user.id

    const { provider } = req.body

    if (!provider) {
      return res.status(400).json({
        error: 'Missing provider',
        message: 'Provider is required'
      })
    }

    // Validate provider
    const validProviders = ['instagram_business', 'facebook_business', 'tiktok', 'linkedin']
    if (!validProviders.includes(provider)) {
      return res.status(400).json({
        error: 'Invalid provider',
        message: `Provider must be one of: ${validProviders.join(', ')}`
      })
    }

    // Check if integration exists
    const integrationStatus = await oauthManager.getIntegrationStatus(userId, provider)
    const integration = integrationStatus.find(i => i.provider === provider)

    if (!integration || !integration.connected) {
      return res.status(404).json({
        error: 'Integration not found',
        message: `No active ${provider} integration found`
      })
    }

    // Perform disconnection
    const disconnectionResult = await performDisconnection(userId, provider)

    await AuditLogger.logIntegrationActivity(
      userId,
      provider,
      'social_integration_disconnected',
      'success',
      disconnectionResult
    )

    await AuditLogger.logApiAccess(
      userId,
      req.url,
      req.method,
      200,
      Date.now() - startTime,
      { action: 'disconnect_social_media', provider }
    )

    return res.status(200).json({
      success: true,
      message: `${provider} integration disconnected successfully`,
      result: disconnectionResult
    })

  } catch (error) {
    console.error('Social media disconnect error:', error)

    await AuditLogger.logSecurityEvent(
      req.user?.id || null,
      'social_disconnect_error',
      {
        error: error.message,
        provider: req.body?.provider,
        userAgent: req.headers['user-agent'],
        ipAddress: req.headers['x-forwarded-for'] || req.connection?.remoteAddress
      },
      'error'
    )

    await AuditLogger.logApiAccess(
      req.user?.id || null,
      req.url,
      req.method,
      500,
      Date.now() - startTime,
      { error: error.message }
    )

    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to disconnect social media integration'
    })
  }
}

/**
 * Perform the disconnection process
 */
async function performDisconnection(userId, provider) {
  const result = {
    credentialsRemoved: false,
    settingsRemoved: false,
    syncStatusCleared: false,
    portfolioSyncCleared: false,
    errors: []
  }

  try {
    // Remove OAuth credentials
    await oauthManager.removeIntegration(userId, provider)
    result.credentialsRemoved = true

  } catch (error) {
    console.error('Failed to remove credentials:', error)
    result.errors.push(`Failed to remove credentials: ${error.message}`)
  }

  try {
    // Remove integration settings
    const { error: settingsError } = await supabase
      .from('integration_settings')
      .delete()
      .eq('user_id', userId)
      .eq('provider', provider)

    if (settingsError) {
      throw settingsError
    }
    result.settingsRemoved = true

  } catch (error) {
    console.error('Failed to remove settings:', error)
    result.errors.push(`Failed to remove settings: ${error.message}`)
  }

  try {
    // Clear sync status
    const { error: syncError } = await supabase
      .from('integration_sync_status')
      .delete()
      .eq('user_id', userId)
      .eq('provider', provider)

    if (syncError) {
      throw syncError
    }
    result.syncStatusCleared = true

  } catch (error) {
    console.error('Failed to clear sync status:', error)
    result.errors.push(`Failed to clear sync status: ${error.message}`)
  }

  try {
    // Clear portfolio sync data
    const { error: portfolioError } = await supabase
      .from('portfolio_items')
      .update({
        social_sync_data: null,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .not('social_sync_data', 'is', null)

    if (portfolioError) {
      throw portfolioError
    }
    result.portfolioSyncCleared = true

  } catch (error) {
    console.error('Failed to clear portfolio sync data:', error)
    result.errors.push(`Failed to clear portfolio sync data: ${error.message}`)
  }

  // Log cleanup activity
  try {
    await AuditLogger.logIntegrationActivity(
      userId,
      provider,
      'social_integration_cleanup_completed',
      result.errors.length === 0 ? 'success' : 'warning',
      {
        result,
        cleanupSteps: {
          credentialsRemoved: result.credentialsRemoved,
          settingsRemoved: result.settingsRemoved,
          syncStatusCleared: result.syncStatusCleared,
          portfolioSyncCleared: result.portfolioSyncCleared,
          errorCount: result.errors.length
        }
      }
    )

  } catch (error) {
    console.error('Failed to log cleanup activity:', error)
  }

  return result
}

/**
 * API Route Configuration
 */
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
}
