/**
 * PWA Icon Generation Script
 * Ocean Soul Sparkles - Generates PWA icons from the main logo
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Icon sizes needed for PWA
const iconSizes = [
  { size: 72, name: 'icon-72x72.png' },
  { size: 96, name: 'icon-96x96.png' },
  { size: 128, name: 'icon-128x128.png' },
  { size: 144, name: 'icon-144x144.png' },
  { size: 152, name: 'icon-152x152.png' },
  { size: 192, name: 'icon-192x192.png' },
  { size: 384, name: 'icon-384x384.png' },
  { size: 512, name: 'icon-512x512.png' }
]

// Shortcut icons
const shortcutIcons = [
  { name: 'shortcut-book.png', size: 96 },
  { name: 'shortcut-admin.png', size: 96 },
  { name: 'shortcut-pos.png', size: 96 }
]

// Badge icon
const badgeIcon = { name: 'badge-72x72.png', size: 72 }

function generatePlaceholderIcon(size, outputPath, type = 'main') {
  // Create SVG content based on type
  let svgContent
  
  if (type === 'main') {
    svgContent = `
      <svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#7B68EE;stop-opacity:1" />
          </linearGradient>
        </defs>
        <rect width="${size}" height="${size}" rx="${size * 0.2}" fill="url(#grad)"/>
        <circle cx="${size * 0.3}" cy="${size * 0.3}" r="${size * 0.08}" fill="rgba(255,255,255,0.8)"/>
        <circle cx="${size * 0.7}" cy="${size * 0.4}" r="${size * 0.06}" fill="rgba(255,255,255,0.6)"/>
        <circle cx="${size * 0.5}" cy="${size * 0.7}" r="${size * 0.1}" fill="rgba(255,255,255,0.9)"/>
        <text x="${size * 0.5}" y="${size * 0.85}" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="${size * 0.12}" font-weight="bold">OSS</text>
      </svg>
    `
  } else if (type === 'book') {
    svgContent = `
      <svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
        <rect width="${size}" height="${size}" rx="${size * 0.2}" fill="#10B981"/>
        <rect x="${size * 0.2}" y="${size * 0.25}" width="${size * 0.6}" height="${size * 0.5}" rx="${size * 0.05}" fill="white"/>
        <line x1="${size * 0.3}" y1="${size * 0.4}" x2="${size * 0.7}" y2="${size * 0.4}" stroke="#10B981" stroke-width="${size * 0.02}"/>
        <line x1="${size * 0.3}" y1="${size * 0.5}" x2="${size * 0.7}" y2="${size * 0.5}" stroke="#10B981" stroke-width="${size * 0.02}"/>
        <line x1="${size * 0.3}" y1="${size * 0.6}" x2="${size * 0.6}" y2="${size * 0.6}" stroke="#10B981" stroke-width="${size * 0.02}"/>
      </svg>
    `
  } else if (type === 'admin') {
    svgContent = `
      <svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
        <rect width="${size}" height="${size}" rx="${size * 0.2}" fill="#6366F1"/>
        <rect x="${size * 0.15}" y="${size * 0.2}" width="${size * 0.7}" height="${size * 0.6}" rx="${size * 0.05}" fill="white"/>
        <rect x="${size * 0.2}" y="${size * 0.3}" width="${size * 0.6}" height="${size * 0.05}" fill="#6366F1"/>
        <rect x="${size * 0.25}" y="${size * 0.4}" width="${size * 0.5}" height="${size * 0.03}" fill="#E5E7EB"/>
        <rect x="${size * 0.25}" y="${size * 0.5}" width="${size * 0.4}" height="${size * 0.03}" fill="#E5E7EB"/>
        <rect x="${size * 0.25}" y="${size * 0.6}" width="${size * 0.3}" height="${size * 0.03}" fill="#E5E7EB"/>
      </svg>
    `
  } else if (type === 'pos') {
    svgContent = `
      <svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
        <rect width="${size}" height="${size}" rx="${size * 0.2}" fill="#F59E0B"/>
        <rect x="${size * 0.2}" y="${size * 0.15}" width="${size * 0.6}" height="${size * 0.4}" rx="${size * 0.05}" fill="white"/>
        <rect x="${size * 0.25}" y="${size * 0.2}" width="${size * 0.5}" height="${size * 0.05}" fill="#F59E0B"/>
        <circle cx="${size * 0.35}" cy="${size * 0.7}" r="${size * 0.08}" fill="white"/>
        <circle cx="${size * 0.65}" cy="${size * 0.7}" r="${size * 0.08}" fill="white"/>
        <text x="${size * 0.5}" y="${size * 0.85}" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="${size * 0.08}" font-weight="bold">$</text>
      </svg>
    `
  } else if (type === 'badge') {
    svgContent = `
      <svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
        <circle cx="${size * 0.5}" cy="${size * 0.5}" r="${size * 0.45}" fill="#EF4444"/>
        <text x="${size * 0.5}" y="${size * 0.6}" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="${size * 0.4}" font-weight="bold">!</text>
      </svg>
    `
  }

  // Write SVG file (for development, in production you'd convert to PNG)
  const svgPath = outputPath.replace('.png', '.svg')
  fs.writeFileSync(svgPath, svgContent.trim())
  
  console.log(`Generated placeholder icon: ${svgPath}`)
  
  // Note: In a real implementation, you'd use a library like sharp or canvas
  // to convert SVG to PNG. For now, we'll create SVG placeholders.
  return svgPath
}

function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true })
    console.log(`Created directory: ${dirPath}`)
  }
}

function generateAllIcons() {
  const iconsDir = path.join(process.cwd(), 'public', 'images', 'icons')
  ensureDirectoryExists(iconsDir)

  console.log('Generating PWA icons...')

  // Generate main app icons
  iconSizes.forEach(({ size, name }) => {
    const outputPath = path.join(iconsDir, name)
    generatePlaceholderIcon(size, outputPath, 'main')
  })

  // Generate shortcut icons
  shortcutIcons.forEach(({ name, size }) => {
    const outputPath = path.join(iconsDir, name)
    let type = 'main'
    
    if (name.includes('book')) type = 'book'
    else if (name.includes('admin')) type = 'admin'
    else if (name.includes('pos')) type = 'pos'
    
    generatePlaceholderIcon(size, outputPath, type)
  })

  // Generate badge icon
  const badgePath = path.join(iconsDir, badgeIcon.name)
  generatePlaceholderIcon(badgeIcon.size, badgePath, 'badge')

  console.log('PWA icon generation completed!')
  console.log('\nGenerated icons:')
  console.log('- Main app icons (72x72 to 512x512)')
  console.log('- Shortcut icons for booking, admin, and POS')
  console.log('- Badge icon for notifications')
  console.log('\nNote: These are SVG placeholders. In production, convert to PNG using a tool like sharp.')
}

// Create screenshots directory and placeholder files
function generateScreenshots() {
  const screenshotsDir = path.join(process.cwd(), 'public', 'images', 'screenshots')
  ensureDirectoryExists(screenshotsDir)

  // Mobile screenshot placeholder
  const mobileScreenshot = `
    <svg width="540" height="720" xmlns="http://www.w3.org/2000/svg">
      <rect width="540" height="720" fill="#F8FAFC"/>
      <rect x="0" y="0" width="540" height="80" fill="#4A90E2"/>
      <text x="270" y="50" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="24" font-weight="bold">Ocean Soul Sparkles</text>
      <rect x="20" y="100" width="500" height="200" rx="10" fill="white" stroke="#E5E7EB"/>
      <text x="270" y="210" text-anchor="middle" fill="#374151" font-family="Arial, sans-serif" font-size="18">Mobile Home Screen</text>
      <rect x="20" y="320" width="500" height="300" rx="10" fill="white" stroke="#E5E7EB"/>
      <text x="270" y="480" text-anchor="middle" fill="#6B7280" font-family="Arial, sans-serif" font-size="14">Services and Booking</text>
    </svg>
  `

  // Desktop screenshot placeholder
  const desktopScreenshot = `
    <svg width="1280" height="720" xmlns="http://www.w3.org/2000/svg">
      <rect width="1280" height="720" fill="#F8FAFC"/>
      <rect x="0" y="0" width="1280" height="60" fill="#4A90E2"/>
      <text x="640" y="40" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="20" font-weight="bold">Ocean Soul Sparkles - Admin Dashboard</text>
      <rect x="20" y="80" width="300" height="620" rx="10" fill="white" stroke="#E5E7EB"/>
      <text x="170" y="120" text-anchor="middle" fill="#374151" font-family="Arial, sans-serif" font-size="16">Navigation</text>
      <rect x="340" y="80" width="920" height="620" rx="10" fill="white" stroke="#E5E7EB"/>
      <text x="800" y="400" text-anchor="middle" fill="#6B7280" font-family="Arial, sans-serif" font-size="18">Dashboard Content</text>
    </svg>
  `

  fs.writeFileSync(path.join(screenshotsDir, 'mobile-home.svg'), mobileScreenshot.trim())
  fs.writeFileSync(path.join(screenshotsDir, 'desktop-dashboard.svg'), desktopScreenshot.trim())

  console.log('Generated screenshot placeholders')
}

// Run the generation
if (import.meta.url === `file://${process.argv[1]}`) {
  generateAllIcons()
  generateScreenshots()

  console.log('\n✅ PWA assets generation completed!')
  console.log('\nNext steps:')
  console.log('1. Replace SVG placeholders with actual PNG icons using your logo')
  console.log('2. Take real screenshots of your app for the manifest')
  console.log('3. Test PWA installation on various devices')
  console.log('4. Verify service worker caching strategies')
}

export {
  generateAllIcons,
  generateScreenshots,
  generatePlaceholderIcon
}
