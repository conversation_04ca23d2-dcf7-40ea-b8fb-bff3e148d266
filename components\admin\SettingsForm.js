import { useState, useEffect } from 'react';
import styles from '@/styles/admin/SettingsForm.module.css';

const SettingsForm = ({ settings, onSave, loading }) => {
  const [formData, setFormData] = useState({
    site_name: '',
    site_description: '',
    contact_email: '',
    contact_phone: '',
    business_hours: '',
    booking_lead_time: '24',
    booking_max_days_ahead: '60',
    enable_online_bookings: 'true',
    enable_online_payments: 'true',
    notification_email: '',
    google_analytics_id: '',
    facebook_pixel_id: '',
    theme_primary_color: '#3788d8',
    theme_secondary_color: '#2c3e50',
    theme_accent_color: '#e74c3c',
    logo_url: '',
    favicon_url: '',
    terms_url: '',
    privacy_url: '',
    social_facebook: '',
    social_instagram: '',
    social_twitter: '',
    social_linkedin: '',
    social_youtube: '',
    custom_css: '',
    custom_js: '',

    // Square API Settings
    square_application_id: '',
    square_access_token: '',
    square_environment: 'sandbox',
    square_location_id: '',
    square_webhook_signature_key: '',
    square_currency: 'AUD',
    enable_square_payments: 'false',

    // Google Integrations
    google_analytics_measurement_id: '',
    google_tag_manager_id: '',
    google_search_console_verification: '',
    google_business_profile_id: '',
    google_my_business_api_key: '',
    google_maps_api_key: '',
    google_ads_customer_id: '',
    google_ads_conversion_id: '',
    enable_google_business_integration: 'false',
    enable_google_reviews_widget: 'false',

    // SEO Settings
    seo_meta_title: '',
    seo_meta_description: '',
    seo_keywords: '',
    seo_canonical_url: '',
    enable_schema_markup: 'true',

    // Email Settings
    smtp_host: '',
    smtp_port: '587',
    smtp_username: '',
    smtp_password: '',
    smtp_encryption: 'tls',
    email_from_address: '',
    email_from_name: '',
    enable_email_notifications: 'true',

    // Backup & Security
    enable_auto_backup: 'false',
    backup_frequency: 'weekly',
    enable_two_factor_auth: 'false',
    session_timeout: '60',
  });

  const [activeTab, setActiveTab] = useState('general');
  const [connectionStatus, setConnectionStatus] = useState({
    square: null,
    google_analytics: null,
    google_maps: null,
    email: null
  });

  // Google verification file upload state
  const [googleVerificationFiles, setGoogleVerificationFiles] = useState([]);
  const [uploadingVerificationFile, setUploadingVerificationFile] = useState(false);
  const [verificationFileError, setVerificationFileError] = useState(null);

  // Initialize form with settings
  useEffect(() => {
    if (settings) {
      setFormData(prevData => ({
        ...prevData,
        ...settings
      }));
    }
  }, [settings]);

  // Load Google verification files on component mount
  useEffect(() => {
    loadGoogleVerificationFiles();
  }, []);

  // Handle input changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;

    // Handle checkboxes
    if (type === 'checkbox') {
      setFormData({
        ...formData,
        [name]: checked.toString()
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  // Test Square API connection
  const testSquareConnection = async () => {
    setConnectionStatus(prev => ({ ...prev, square: 'testing' }));

    try {
      const response = await fetch('/api/admin/test-connections', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'square',
          settings: {
            application_id: formData.square_application_id,
            access_token: formData.square_access_token,
            environment: formData.square_environment,
            location_id: formData.square_location_id
          }
        })
      });

      const result = await response.json();
      setConnectionStatus(prev => ({
        ...prev,
        square: result.success ? 'success' : 'error'
      }));
    } catch (error) {
      setConnectionStatus(prev => ({ ...prev, square: 'error' }));
    }
  };

  // Test Google Analytics connection
  const testGoogleAnalytics = async () => {
    setConnectionStatus(prev => ({ ...prev, google_analytics: 'testing' }));

    try {
      const response = await fetch('/api/admin/test-connections', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'google_analytics',
          settings: {
            measurement_id: formData.google_analytics_measurement_id
          }
        })
      });

      const result = await response.json();
      setConnectionStatus(prev => ({
        ...prev,
        google_analytics: result.success ? 'success' : 'error'
      }));
    } catch (error) {
      setConnectionStatus(prev => ({ ...prev, google_analytics: 'error' }));
    }
  };

  // Test Google Maps API connection
  const testGoogleMaps = async () => {
    setConnectionStatus(prev => ({ ...prev, google_maps: 'testing' }));

    try {
      const response = await fetch('/api/admin/test-connections', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'google_maps',
          settings: {
            api_key: formData.google_maps_api_key
          }
        })
      });

      const result = await response.json();
      setConnectionStatus(prev => ({
        ...prev,
        google_maps: result.success ? 'success' : 'error'
      }));
    } catch (error) {
      setConnectionStatus(prev => ({ ...prev, google_maps: 'error' }));
    }
  };

  // Test email settings
  const testEmailConnection = async () => {
    setConnectionStatus(prev => ({ ...prev, email: 'testing' }));

    try {
      const response = await fetch('/api/admin/test-connections', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'email',
          settings: {
            smtp_host: formData.smtp_host,
            smtp_port: formData.smtp_port,
            smtp_username: formData.smtp_username,
            smtp_password: formData.smtp_password,
            smtp_encryption: formData.smtp_encryption
          }
        })
      });

      const result = await response.json();
      setConnectionStatus(prev => ({
        ...prev,
        email: result.success ? 'success' : 'error'
      }));
    } catch (error) {
      setConnectionStatus(prev => ({ ...prev, email: 'error' }));
    }
  };

  // Test Gmail SMTP connection
  const testGmailConnection = async () => {
    setConnectionStatus(prev => ({ ...prev, gmail: 'testing' }));

    try {
      const response = await fetch('/api/admin/test-connections', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'gmail'
        })
      });

      const result = await response.json();
      setConnectionStatus(prev => ({
        ...prev,
        gmail: result.success ? 'success' : 'error'
      }));
    } catch (error) {
      setConnectionStatus(prev => ({ ...prev, gmail: 'error' }));
    }
  };

  // Test Google Workspace SMTP connection
  const testWorkspaceConnection = async () => {
    setConnectionStatus(prev => ({ ...prev, workspace: 'testing' }));

    try {
      const response = await fetch('/api/admin/test-connections', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'workspace'
        })
      });

      const result = await response.json();
      setConnectionStatus(prev => ({
        ...prev,
        workspace: result.success ? 'success' : 'error'
      }));
    } catch (error) {
      setConnectionStatus(prev => ({ ...prev, workspace: 'error' }));
    }
  };
  // Check email service status
  const checkEmailServiceStatus = async () => {
    setConnectionStatus(prev => ({ ...prev, emailStatus: 'testing' }));

    try {
      const response = await fetch('/api/admin/test-connections', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'email-status'
        })
      });

      const result = await response.json();
      setConnectionStatus(prev => ({
        ...prev,
        emailStatus: result.success ? 'success' : 'error'
      }));
    } catch (error) {
      setConnectionStatus(prev => ({ ...prev, emailStatus: 'error' }));
    }
  };

  // Gmail API Functions
  const checkGmailAPIStatus = async () => {
    setConnectionStatus(prev => ({ ...prev, gmailAPI: { loading: true } }));

    try {
      const response = await fetch('/api/admin/google-cloud-email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'gmail-api-status'
        })
      });

      const result = await response.json();
      setConnectionStatus(prev => ({
        ...prev,
        gmailAPI: {
          loading: false,
          success: result.success,
          data: result
        }
      }));
    } catch (error) {
      setConnectionStatus(prev => ({
        ...prev,
        gmailAPI: {
          loading: false,
          success: false,
          error: error.message
        }
      }));
    }
  };

  const authorizeGmailAPI = async () => {
    try {
      const response = await fetch('/api/admin/google-cloud-email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'gmail-api-authorize'
        })
      });

      const result = await response.json();
      if (result.success && result.auth_url) {
        // Redirect to authorization URL
        window.location.href = result.auth_url;
      } else {
        alert('Failed to generate authorization URL: ' + (result.error || 'Unknown error'));
      }
    } catch (error) {
      alert('Error initiating Gmail API authorization: ' + error.message);
    }
  };

  const revokeGmailAPI = async () => {
    if (!confirm('Are you sure you want to revoke Gmail API access? This will disable Gmail API email sending.')) {
      return;
    }

    try {
      const response = await fetch('/api/admin/google-cloud-email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'gmail-api-revoke'
        })
      });

      const result = await response.json();
      if (result.success) {
        alert('Gmail API access has been revoked successfully.');
        // Refresh status
        checkGmailAPIStatus();
      } else {
        alert('Failed to revoke Gmail API access: ' + (result.error || 'Unknown error'));
      }
    } catch (error) {
      alert('Error revoking Gmail API access: ' + error.message);
    }
  };

  const testGmailAPIConnection = async () => {
    setConnectionStatus(prev => ({ 
      ...prev, 
      gmailAPITest: 'testing' 
    }));

    try {
      const response = await fetch('/api/admin/google-cloud-email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'test-email',
          service: 'gmail-api'
        })
      });

      const result = await response.json();
      setConnectionStatus(prev => ({
        ...prev,
        gmailAPITest: result.success ? 'success' : 'error',
        gmailAPITestDetails: result
      }));
    } catch (error) {
      setConnectionStatus(prev => ({ 
        ...prev, 
        gmailAPITest: 'error',
        gmailAPITestDetails: { error: error.message }
      }));
    }
  };

  // Load Google verification files
  const loadGoogleVerificationFiles = async () => {
    try {
      const response = await fetch('/api/admin/google-verification-files', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        setGoogleVerificationFiles(data.files || []);
      }
    } catch (error) {
      console.error('Error loading Google verification files:', error);
    }
  };

  // Handle Google verification file upload
  const handleGoogleVerificationUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    setUploadingVerificationFile(true);
    setVerificationFileError(null);

    try {
      const formData = new FormData();
      formData.append('verificationFile', file);

      const response = await fetch('/api/admin/uploads/google-verification', {
        method: 'POST',
        body: formData,
        credentials: 'include'
      });

      const result = await response.json();

      if (response.ok) {
        // Reload the verification files list
        await loadGoogleVerificationFiles();
        // Clear the file input
        event.target.value = '';
        console.log('Google verification file uploaded successfully:', result.filename);
      } else {
        setVerificationFileError(result.error || 'Failed to upload verification file');
      }
    } catch (error) {
      console.error('Error uploading Google verification file:', error);
      setVerificationFileError('Failed to upload verification file');
    } finally {
      setUploadingVerificationFile(false);
    }
  };

  // Delete Google verification file
  const deleteGoogleVerificationFile = async (filename) => {
    try {
      const response = await fetch(`/api/admin/google-verification-files?filename=${encodeURIComponent(filename)}`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include'
      });

      if (response.ok) {
        // Reload the verification files list
        await loadGoogleVerificationFiles();
        console.log('Google verification file deleted:', filename);
      } else {
        const result = await response.json();
        setVerificationFileError(result.error || 'Failed to delete verification file');
      }
    } catch (error) {
      console.error('Error deleting Google verification file:', error);
      setVerificationFileError('Failed to delete verification file');
    }
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
  };

  const renderConnectionStatus = (status) => {
    if (status === 'testing') return <span className={styles.statusTesting}>Testing...</span>;
    if (status === 'success') return <span className={styles.statusSuccess}>✓ Connected</span>;
    if (status === 'error') return <span className={styles.statusError}>✗ Failed</span>;
    return null;
  };

  return (
    <div className={styles.settingsContainer}>
      <div className={styles.tabNavigation}>
        <button
          className={`${styles.tab} ${activeTab === 'general' ? styles.active : ''}`}
          onClick={() => setActiveTab('general')}
        >
          General
        </button>
        <button
          className={`${styles.tab} ${activeTab === 'payments' ? styles.active : ''}`}
          onClick={() => setActiveTab('payments')}
        >
          Payments & Square
        </button>
        <button
          className={`${styles.tab} ${activeTab === 'google' ? styles.active : ''}`}
          onClick={() => setActiveTab('google')}
        >
          Google Integration
        </button>
        <button
          className={`${styles.tab} ${activeTab === 'seo' ? styles.active : ''}`}
          onClick={() => setActiveTab('seo')}
        >
          SEO & Analytics
        </button>
        <button
          className={`${styles.tab} ${activeTab === 'email' ? styles.active : ''}`}
          onClick={() => setActiveTab('email')}
        >
          Email Settings
        </button>
        <button
          className={`${styles.tab} ${activeTab === 'advanced' ? styles.active : ''}`}
          onClick={() => setActiveTab('advanced')}
        >
          Advanced
        </button>
      </div>

      <form className={styles.settingsForm} onSubmit={handleSubmit}>
        {activeTab === 'general' && (
          <div className={styles.tabContent}>
            <div className={styles.formSection}>
              <h2>General Settings</h2>

              <div className={styles.formGroup}>
                <label htmlFor="site_name">Site Name</label>
                <input
                  type="text"
                  id="site_name"
                  name="site_name"
                  value={formData.site_name || ''}
                  onChange={handleChange}
                  placeholder="Your Business Name"
                />
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="site_description">Site Description</label>
                <textarea
                  id="site_description"
                  name="site_description"
                  value={formData.site_description || ''}
                  onChange={handleChange}
                  placeholder="Brief description of your business"
                  rows={3}
                />
              </div>

              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label htmlFor="contact_email">Contact Email</label>
                  <input
                    type="email"
                    id="contact_email"
                    name="contact_email"
                    value={formData.contact_email || ''}
                    onChange={handleChange}
                    placeholder="<EMAIL>"
                  />
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="contact_phone">Contact Phone</label>
                  <input
                    type="tel"
                    id="contact_phone"
                    name="contact_phone"
                    value={formData.contact_phone || ''}
                    onChange={handleChange}
                    placeholder="+61 (xxx) xxx-xxx"
                  />
                </div>
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="business_hours">Business Hours</label>
                <textarea
                  id="business_hours"
                  name="business_hours"
                  value={formData.business_hours || ''}
                  onChange={handleChange}
                  placeholder="Mon-Fri: 9am-5pm, Sat: 10am-3pm, Sun: Closed"
                  rows={2}
                />
              </div>
            </div>

            <div className={styles.formSection}>
              <h2>Booking Settings</h2>

              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label htmlFor="booking_lead_time">Booking Lead Time (hours)</label>
                  <input
                    type="number"
                    id="booking_lead_time"
                    name="booking_lead_time"
                    value={formData.booking_lead_time || '24'}
                    onChange={handleChange}
                    min="0"
                    max="168"
                  />
                  <small>Minimum hours in advance a booking can be made</small>
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="booking_max_days_ahead">Max Booking Days Ahead</label>
                  <input
                    type="number"
                    id="booking_max_days_ahead"
                    name="booking_max_days_ahead"
                    value={formData.booking_max_days_ahead || '60'}
                    onChange={handleChange}
                    min="1"
                    max="365"
                  />
                  <small>Maximum days in advance a booking can be made</small>
                </div>
              </div>

              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label className={styles.checkboxLabel}>
                    <input
                      type="checkbox"
                      name="enable_online_bookings"
                      checked={formData.enable_online_bookings === 'true'}
                      onChange={handleChange}
                    />
                    Enable Online Bookings
                  </label>
                </div>

                <div className={styles.formGroup}>
                  <label className={styles.checkboxLabel}>
                    <input
                      type="checkbox"
                      name="enable_online_payments"
                      checked={formData.enable_online_payments === 'true'}
                      onChange={handleChange}
                    />
                    Enable Online Payments
                  </label>
                </div>
              </div>
            </div>

            <div className={styles.formSection}>
              <h2>Theme Settings</h2>

              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label htmlFor="theme_primary_color">Primary Color</label>
                  <div className={styles.colorPickerWrapper}>
                    <input
                      type="color"
                      id="theme_primary_color"
                      name="theme_primary_color"
                      value={formData.theme_primary_color || '#3788d8'}
                      onChange={handleChange}
                    />
                    <input
                      type="text"
                      value={formData.theme_primary_color || '#3788d8'}
                      onChange={handleChange}
                      name="theme_primary_color"
                      placeholder="#3788d8"
                    />
                  </div>
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="theme_secondary_color">Secondary Color</label>
                  <div className={styles.colorPickerWrapper}>
                    <input
                      type="color"
                      id="theme_secondary_color"
                      name="theme_secondary_color"
                      value={formData.theme_secondary_color || '#2c3e50'}
                      onChange={handleChange}
                    />
                    <input
                      type="text"
                      value={formData.theme_secondary_color || '#2c3e50'}
                      onChange={handleChange}
                      name="theme_secondary_color"
                      placeholder="#2c3e50"
                    />
                  </div>
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="theme_accent_color">Accent Color</label>
                  <div className={styles.colorPickerWrapper}>
                    <input
                      type="color"
                      id="theme_accent_color"
                      name="theme_accent_color"
                      value={formData.theme_accent_color || '#e74c3c'}
                      onChange={handleChange}
                    />
                    <input
                      type="text"
                      value={formData.theme_accent_color || '#e74c3c'}
                      onChange={handleChange}
                      name="theme_accent_color"
                      placeholder="#e74c3c"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'payments' && (
          <div className={styles.tabContent}>
            <div className={styles.formSection}>
              <h2>Square Payment Integration</h2>

              <div className={styles.formGroup}>
                <label className={styles.checkboxLabel}>
                  <input
                    type="checkbox"
                    name="enable_square_payments"
                    checked={formData.enable_square_payments === 'true'}
                    onChange={handleChange}
                  />
                  Enable Square Payments
                </label>
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="square_environment">Environment</label>
                <select
                  id="square_environment"
                  name="square_environment"
                  value={formData.square_environment || 'sandbox'}
                  onChange={handleChange}
                >
                  <option value="sandbox">Sandbox (Testing)</option>
                  <option value="production">Production (Live)</option>
                </select>
                <small>Use Sandbox for testing, Production for live payments</small>
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="square_application_id">Application ID</label>
                <input
                  type="text"
                  id="square_application_id"
                  name="square_application_id"
                  value={formData.square_application_id || ''}
                  onChange={handleChange}
                  placeholder="sq0idp-..."
                />
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="square_access_token">Access Token</label>
                <input
                  type="password"
                  id="square_access_token"
                  name="square_access_token"
                  value={formData.square_access_token || ''}
                  onChange={handleChange}
                  placeholder="EAAAEOuZ..."
                />
                <small>Keep this secure - it will be encrypted when saved</small>
              </div>

              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label htmlFor="square_location_id">Location ID</label>
                  <input
                    type="text"
                    id="square_location_id"
                    name="square_location_id"
                    value={formData.square_location_id || ''}
                    onChange={handleChange}
                    placeholder="L123..."
                  />
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="square_currency">Currency</label>
                  <select
                    id="square_currency"
                    name="square_currency"
                    value={formData.square_currency || 'AUD'}
                    onChange={handleChange}
                  >
                    <option value="AUD">Australian Dollar (AUD)</option>
                    <option value="USD">US Dollar (USD)</option>
                    <option value="EUR">Euro (EUR)</option>
                    <option value="GBP">British Pound (GBP)</option>
                  </select>
                </div>
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="square_webhook_signature_key">Webhook Signature Key</label>
                <input
                  type="password"
                  id="square_webhook_signature_key"
                  name="square_webhook_signature_key"
                  value={formData.square_webhook_signature_key || ''}
                  onChange={handleChange}
                  placeholder="webhook_signature_key..."
                />
                <small>For webhook verification (optional)</small>
              </div>

              <div className={styles.connectionTest}>
                <button
                  type="button"
                  onClick={testSquareConnection}
                  className={styles.testButton}
                  disabled={!formData.square_application_id || !formData.square_access_token}
                >
                  Test Square Connection
                </button>
                {renderConnectionStatus(connectionStatus.square)}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'google' && (
          <div className={styles.tabContent}>
            <div className={styles.formSection}>
              <h2>Google Business Profile</h2>

              <div className={styles.formGroup}>
                <label className={styles.checkboxLabel}>
                  <input
                    type="checkbox"
                    name="enable_google_business_integration"
                    checked={formData.enable_google_business_integration === 'true'}
                    onChange={handleChange}
                  />
                  Enable Google Business Profile Integration
                </label>
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="google_business_profile_id">Business Profile ID</label>
                <input
                  type="text"
                  id="google_business_profile_id"
                  name="google_business_profile_id"
                  value={formData.google_business_profile_id || ''}
                  onChange={handleChange}
                  placeholder="accounts/123456789012345678901/locations/987654321098765432109"
                />
                <small>Find this in your Google Business Profile dashboard</small>
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="google_my_business_api_key">My Business API Key</label>
                <input
                  type="password"
                  id="google_my_business_api_key"
                  name="google_my_business_api_key"
                  value={formData.google_my_business_api_key || ''}
                  onChange={handleChange}
                  placeholder="AIzaSy..."
                />
              </div>

              <div className={styles.formGroup}>
                <label className={styles.checkboxLabel}>
                  <input
                    type="checkbox"
                    name="enable_google_reviews_widget"
                    checked={formData.enable_google_reviews_widget === 'true'}
                    onChange={handleChange}
                  />
                  Enable Google Reviews Widget
                </label>
              </div>
            </div>

            <div className={styles.formSection}>
              <h2>Google Maps</h2>

              <div className={styles.formGroup}>
                <label htmlFor="google_maps_api_key">Google Maps API Key</label>
                <div className={styles.inputWithButton}>
                  <input
                    type="password"
                    id="google_maps_api_key"
                    name="google_maps_api_key"
                    value={formData.google_maps_api_key || ''}
                    onChange={handleChange}
                    placeholder="AIzaSy..."
                  />
                  <button
                    type="button"
                    onClick={testGoogleMaps}
                    className={`${styles.testButton} ${
                      connectionStatus.google_maps === 'testing' ? styles.testing :
                      connectionStatus.google_maps === 'success' ? styles.success :
                      connectionStatus.google_maps === 'error' ? styles.error : ''
                    }`}
                    disabled={!formData.google_maps_api_key || connectionStatus.google_maps === 'testing'}
                  >
                    {connectionStatus.google_maps === 'testing' ? 'Testing...' :
                     connectionStatus.google_maps === 'success' ? '✓ Connected' :
                     connectionStatus.google_maps === 'error' ? '✗ Failed' : 'Test'}
                  </button>
                </div>
                <small>Used for location display, directions, and AI scheduling optimization</small>
              </div>
            </div>

            <div className={styles.formSection}>
              <h2>Google Ads</h2>

              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label htmlFor="google_ads_customer_id">Customer ID</label>
                  <input
                    type="text"
                    id="google_ads_customer_id"
                    name="google_ads_customer_id"
                    value={formData.google_ads_customer_id || ''}
                    onChange={handleChange}
                    placeholder="123-456-7890"
                  />
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="google_ads_conversion_id">Conversion ID</label>
                  <input
                    type="text"
                    id="google_ads_conversion_id"
                    name="google_ads_conversion_id"
                    value={formData.google_ads_conversion_id || ''}
                    onChange={handleChange}
                    placeholder="AW-123456789"
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'seo' && (
          <div className={styles.tabContent}>
            <div className={styles.formSection}>
              <h2>SEO Settings</h2>

              <div className={styles.formGroup}>
                <label htmlFor="seo_meta_title">Default Meta Title</label>
                <input
                  type="text"
                  id="seo_meta_title"
                  name="seo_meta_title"
                  value={formData.seo_meta_title || ''}
                  onChange={handleChange}
                  placeholder="Ocean Soul Sparkles - Professional Face Painting Services"
                />
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="seo_meta_description">Default Meta Description</label>
                <textarea
                  id="seo_meta_description"
                  name="seo_meta_description"
                  value={formData.seo_meta_description || ''}
                  onChange={handleChange}
                  placeholder="Professional face painting and body art services for events, parties, and special occasions."
                  rows={3}
                />
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="seo_keywords">Keywords</label>
                <input
                  type="text"
                  id="seo_keywords"
                  name="seo_keywords"
                  value={formData.seo_keywords || ''}
                  onChange={handleChange}
                  placeholder="face painting, body art, events, parties, kids entertainment"
                />
                <small>Separate keywords with commas</small>
              </div>

              <div className={styles.formGroup}>
                <label className={styles.checkboxLabel}>
                  <input
                    type="checkbox"
                    name="enable_schema_markup"
                    checked={formData.enable_schema_markup === 'true'}
                    onChange={handleChange}
                  />
                  Enable Schema Markup (Structured Data)
                </label>
              </div>
            </div>

            <div className={styles.formSection}>
              <h2>Analytics & Tracking</h2>

              <div className={styles.formGroup}>
                <label htmlFor="google_analytics_measurement_id">Google Analytics Measurement ID</label>
                <input
                  type="text"
                  id="google_analytics_measurement_id"
                  name="google_analytics_measurement_id"
                  value={formData.google_analytics_measurement_id || ''}
                  onChange={handleChange}
                  placeholder="G-XXXXXXXXXX"
                />
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="google_tag_manager_id">Google Tag Manager ID</label>
                <input
                  type="text"
                  id="google_tag_manager_id"
                  name="google_tag_manager_id"
                  value={formData.google_tag_manager_id || ''}
                  onChange={handleChange}
                  placeholder="GTM-XXXXXXX"
                />
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="google_search_console_verification">Google Search Console Verification (Meta Tag)</label>
                <input
                  type="text"
                  id="google_search_console_verification"
                  name="google_search_console_verification"
                  value={formData.google_search_console_verification || ''}
                  onChange={handleChange}
                  placeholder="HtjqFmAXzFBvlS4lE"
                />
                <small>
                  Enter only the verification code from Google Search Console (e.g., "HtjqFmAXzFBvlS4lE").
                  This will be added as a meta tag to verify your website ownership.
                  <strong>Security:</strong> This verification tag will only appear on public pages, not admin pages.
                </small>
              </div>

              <div className={styles.formGroup}>
                <label>Google Search Console Verification (HTML File)</label>
                <div className={styles.fileUploadSection}>
                  <div className={styles.uploadArea}>
                    <input
                      type="file"
                      id="google-verification-file"
                      accept=".html"
                      onChange={handleGoogleVerificationUpload}
                      disabled={uploadingVerificationFile}
                      className={styles.fileInput}
                      style={{ display: 'none' }}
                    />
                    <label htmlFor="google-verification-file" className={styles.uploadButton}>
                      {uploadingVerificationFile ? 'Uploading...' : '📁 Upload Google Verification HTML File'}
                    </label>
                    <small>
                      Upload the HTML file provided by Google Search Console (e.g., google7703f3860eb21a44.html).
                      This file will be accessible at your website root for Google verification.
                    </small>
                  </div>

                  {verificationFileError && (
                    <div className={styles.errorMessage}>
                      {verificationFileError}
                    </div>
                  )}

                  {googleVerificationFiles.length > 0 && (
                    <div className={styles.existingFiles}>
                      <h4>Current Verification Files:</h4>
                      {googleVerificationFiles.map((file, index) => (
                        <div key={index} className={styles.fileItem}>
                          <div className={styles.fileInfo}>
                            <span className={styles.fileName}>{file.filename}</span>
                            <a
                              href={file.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className={styles.fileLink}
                            >
                              View File
                            </a>
                          </div>
                          <button
                            type="button"
                            onClick={() => deleteGoogleVerificationFile(file.filename)}
                            className={styles.deleteButton}
                          >
                            Delete
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              <div className={styles.connectionTest}>
                <button
                  type="button"
                  onClick={testGoogleAnalytics}
                  className={styles.testButton}
                  disabled={!formData.google_analytics_measurement_id}
                >
                  Test Google Analytics
                </button>
                {renderConnectionStatus(connectionStatus.google_analytics)}
              </div>
            </div>
          </div>
        )}        {activeTab === 'email' && (
          <div className={styles.tabContent}>
            <div className={styles.formSection}>
              <h2>Email Configuration</h2>

              <div className={styles.formGroup}>
                <label className={styles.checkboxLabel}>
                  <input
                    type="checkbox"
                    name="enable_email_notifications"
                    checked={formData.enable_email_notifications === 'true'}
                    onChange={handleChange}
                  />
                  Enable Email Notifications
                </label>
              </div>

              {/* Google Cloud Email Configuration */}
              <div className={styles.formSection}>
                <h3>Google Cloud Email (Recommended)</h3>
                <p className={styles.sectionDescription}>
                  Configure Gmail SMTP or Google Workspace for reliable email delivery
                </p>
                
                <div className={styles.formRow}>
                  <div className={styles.formGroup}>
                    <label htmlFor="gmail_smtp_user">Gmail Email Address</label>
                    <input
                      type="email"
                      id="gmail_smtp_user"
                      name="gmail_smtp_user"
                      value={formData.gmail_smtp_user || ''}
                      onChange={handleChange}
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div className={styles.formGroup}>
                    <label htmlFor="gmail_smtp_app_password">Gmail App Password</label>
                    <input
                      type="password"
                      id="gmail_smtp_app_password"
                      name="gmail_smtp_app_password"
                      value={formData.gmail_smtp_app_password || ''}
                      onChange={handleChange}
                      placeholder="16-character app password"
                    />
                    <small className={styles.fieldHelp}>
                      Generate an app password in your Google Account settings
                    </small>
                  </div>
                </div>

                <div className={styles.connectionTest}>
                  <button
                    type="button"
                    onClick={testGmailConnection}
                    className={styles.testButton}
                    disabled={!formData.gmail_smtp_user || !formData.gmail_smtp_app_password}
                  >
                    Test Gmail Connection
                  </button>
                  {renderConnectionStatus(connectionStatus.gmail)}
                </div>

                {/* Google Workspace Configuration */}
                <div className={styles.formSubsection}>
                  <h4>Google Workspace (Business Email)</h4>
                  <div className={styles.formRow}>
                    <div className={styles.formGroup}>
                      <label htmlFor="workspace_smtp_user">Workspace Email</label>
                      <input
                        type="email"
                        id="workspace_smtp_user"
                        name="workspace_smtp_user"
                        value={formData.workspace_smtp_user || ''}
                        onChange={handleChange}
                        placeholder="<EMAIL>"
                      />
                    </div>

                    <div className={styles.formGroup}>
                      <label htmlFor="workspace_smtp_app_password">Workspace App Password</label>
                      <input
                        type="password"
                        id="workspace_smtp_app_password"
                        name="workspace_smtp_app_password"
                        value={formData.workspace_smtp_app_password || ''}
                        onChange={handleChange}
                        placeholder="16-character app password"
                      />
                    </div>
                  </div>

                  <div className={styles.connectionTest}>
                    <button
                      type="button"
                      onClick={testWorkspaceConnection}
                      className={styles.testButton}
                      disabled={!formData.workspace_smtp_user || !formData.workspace_smtp_app_password}
                    >
                      Test Workspace Connection
                    </button>
                    {renderConnectionStatus(connectionStatus.workspace)}
                  </div>                </div>
              </div>

              {/* Gmail API Configuration */}
              <div className={styles.formSection}>
                <h3>Gmail API (Most Reliable)</h3>
                <p className={styles.sectionDescription}>
                  Use Gmail API with OAuth for the most reliable email delivery. No app passwords required.
                </p>
                
                <div className={styles.connectionTest}>
                  <button
                    type="button"
                    onClick={checkGmailAPIStatus}
                    className={styles.testButton}
                  >
                    Check Gmail API Status
                  </button>
                  {renderConnectionStatus(connectionStatus.gmailAPI)}
                </div>

                {connectionStatus.gmailAPI && !connectionStatus.gmailAPI.loading && (
                  <div className={styles.gmailAPIControls}>
                    {connectionStatus.gmailAPI.data?.authorized ? (
                      <div className={styles.authorizedSection}>
                        <div className={styles.statusIndicator}>
                          <span className={styles.statusIcon}>✅</span>
                          <span>Gmail API Authorized</span>
                          {connectionStatus.gmailAPI.data?.token_valid && (
                            <span className={styles.validToken}> - Active</span>
                          )}
                          {!connectionStatus.gmailAPI.data?.token_valid && (
                            <span className={styles.invalidToken}> - Needs Re-authorization</span>
                          )}
                        </div>
                        
                        <div className={styles.buttonGroup}>
                          <button
                            type="button"
                            onClick={testGmailAPIConnection}
                            className={styles.testButton}
                          >
                            Test Gmail API
                          </button>
                          <button
                            type="button"
                            onClick={revokeGmailAPI}
                            className={styles.revokeButton}
                          >
                            Revoke Access
                          </button>
                          {!connectionStatus.gmailAPI.data?.token_valid && (
                            <button
                              type="button"
                              onClick={authorizeGmailAPI}
                              className={styles.authorizeButton}
                            >
                              Re-authorize
                            </button>
                          )}
                        </div>
                      </div>
                    ) : (
                      <div className={styles.unauthorizedSection}>
                        <div className={styles.statusIndicator}>
                          <span className={styles.statusIcon}>❌</span>
                          <span>Gmail API Not Authorized</span>
                        </div>
                        {connectionStatus.gmailAPI.data?.oauth_configured ? (
                          <button
                            type="button"
                            onClick={authorizeGmailAPI}
                            className={styles.authorizeButton}
                          >
                            Authorize Gmail API
                          </button>
                        ) : (
                          <div className={styles.configError}>
                            <p>Gmail API OAuth credentials not configured. Please set GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET in your environment variables.</p>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Legacy SMTP Configuration */}
              <div className={styles.formSection}>
                <h3>Legacy SMTP Configuration</h3>
                <p className={styles.sectionDescription}>
                  Alternative SMTP configuration (not recommended for production)
                </p>

                <div className={styles.formRow}>
                  <div className={styles.formGroup}>
                    <label htmlFor="smtp_host">SMTP Host</label>
                    <input
                      type="text"
                      id="smtp_host"
                      name="smtp_host"
                      value={formData.smtp_host || ''}
                      onChange={handleChange}
                      placeholder="smtp.gmail.com"
                    />
                  </div>

                  <div className={styles.formGroup}>
                    <label htmlFor="smtp_port">SMTP Port</label>
                    <input
                      type="number"
                      id="smtp_port"
                      name="smtp_port"
                      value={formData.smtp_port || '587'}
                      onChange={handleChange}
                      placeholder="587"
                    />
                  </div>
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="smtp_encryption">Encryption</label>
                  <select
                    id="smtp_encryption"
                    name="smtp_encryption"
                    value={formData.smtp_encryption || 'tls'}
                    onChange={handleChange}
                  >
                    <option value="tls">TLS</option>
                    <option value="ssl">SSL</option>
                    <option value="none">None</option>
                  </select>
                </div>

                <div className={styles.formRow}>
                  <div className={styles.formGroup}>
                    <label htmlFor="smtp_username">SMTP Username</label>
                    <input
                      type="text"
                      id="smtp_username"
                      name="smtp_username"
                      value={formData.smtp_username || ''}
                      onChange={handleChange}
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div className={styles.formGroup}>
                    <label htmlFor="smtp_password">SMTP Password</label>
                    <input
                      type="password"
                      id="smtp_password"
                      name="smtp_password"
                      value={formData.smtp_password || ''}
                      onChange={handleChange}
                      placeholder="App Password or Email Password"
                    />
                  </div>
                </div>

                <div className={styles.formRow}>
                  <div className={styles.formGroup}>
                    <label htmlFor="email_from_address">From Email Address</label>
                    <input
                      type="email"
                      id="email_from_address"
                      name="email_from_address"
                      value={formData.email_from_address || ''}
                      onChange={handleChange}
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div className={styles.formGroup}>
                    <label htmlFor="email_from_name">From Name</label>
                    <input
                      type="text"
                      id="email_from_name"
                      name="email_from_name"
                      value={formData.email_from_name || ''}
                      onChange={handleChange}
                      placeholder="Ocean Soul Sparkles"
                    />
                  </div>
                </div>

                <div className={styles.connectionTest}>
                  <button
                    type="button"
                    onClick={testEmailConnection}
                    className={styles.testButton}
                    disabled={!formData.smtp_host || !formData.smtp_username}
                  >
                    Test Legacy SMTP Connection
                  </button>
                  {renderConnectionStatus(connectionStatus.email)}
                </div>
              </div>

              {/* Email Service Status */}
              <div className={styles.formSection}>
                <h3>Email Service Status</h3>
                <div className={styles.connectionTest}>
                  <button
                    type="button"
                    onClick={checkEmailServiceStatus}
                    className={styles.testButton}
                  >
                    Check Email Service Status
                  </button>
                  {renderConnectionStatus(connectionStatus.emailStatus)}
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'advanced' && (
          <div className={styles.tabContent}>
            <div className={styles.formSection}>
              <h2>Social Media</h2>

              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label htmlFor="social_facebook">Facebook URL</label>
                  <input
                    type="url"
                    id="social_facebook"
                    name="social_facebook"
                    value={formData.social_facebook || ''}
                    onChange={handleChange}
                    placeholder="https://facebook.com/your-page"
                  />
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="social_instagram">Instagram URL</label>
                  <input
                    type="url"
                    id="social_instagram"
                    name="social_instagram"
                    value={formData.social_instagram || ''}
                    onChange={handleChange}
                    placeholder="https://instagram.com/your-account"
                  />
                </div>
              </div>

              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label htmlFor="social_twitter">Twitter URL</label>
                  <input
                    type="url"
                    id="social_twitter"
                    name="social_twitter"
                    value={formData.social_twitter || ''}
                    onChange={handleChange}
                    placeholder="https://twitter.com/your-account"
                  />
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="social_youtube">YouTube URL</label>
                  <input
                    type="url"
                    id="social_youtube"
                    name="social_youtube"
                    value={formData.social_youtube || ''}
                    onChange={handleChange}
                    placeholder="https://youtube.com/your-channel"
                  />
                </div>
              </div>
            </div>

            <div className={styles.formSection}>
              <h2>Security & Backup</h2>

              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label className={styles.checkboxLabel}>
                    <input
                      type="checkbox"
                      name="enable_auto_backup"
                      checked={formData.enable_auto_backup === 'true'}
                      onChange={handleChange}
                    />
                    Enable Automatic Backups
                  </label>
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="backup_frequency">Backup Frequency</label>
                  <select
                    id="backup_frequency"
                    name="backup_frequency"
                    value={formData.backup_frequency || 'weekly'}
                    onChange={handleChange}
                  >
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                  </select>
                </div>
              </div>

              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label className={styles.checkboxLabel}>
                    <input
                      type="checkbox"
                      name="enable_two_factor_auth"
                      checked={formData.enable_two_factor_auth === 'true'}
                      onChange={handleChange}
                    />
                    Enable Two-Factor Authentication
                  </label>
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="session_timeout">Session Timeout (minutes)</label>
                  <input
                    type="number"
                    id="session_timeout"
                    name="session_timeout"
                    value={formData.session_timeout || '60'}
                    onChange={handleChange}
                    min="15"
                    max="480"
                  />
                </div>
              </div>
            </div>

            <div className={styles.formSection}>
              <h2>Custom Code</h2>

              <div className={styles.formGroup}>
                <label htmlFor="custom_css">Custom CSS</label>
                <textarea
                  id="custom_css"
                  name="custom_css"
                  value={formData.custom_css || ''}
                  onChange={handleChange}
                  placeholder="/* Add your custom CSS here */"
                  rows={8}
                  className={styles.codeTextarea}
                />
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="custom_js">Custom JavaScript</label>
                <textarea
                  id="custom_js"
                  name="custom_js"
                  value={formData.custom_js || ''}
                  onChange={handleChange}
                  placeholder="// Add your custom JavaScript here"
                  rows={8}
                  className={styles.codeTextarea}
                />
              </div>
            </div>
          </div>
        )}

        <div className={styles.formActions}>
          <button
            type="submit"
            className={styles.saveButton}
            disabled={loading}
          >
            {loading ? 'Saving...' : 'Save Settings'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default SettingsForm;
