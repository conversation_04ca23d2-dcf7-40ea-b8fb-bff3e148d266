/**
 * Mailchimp API Integration for Ocean Soul Sparkles
 * Provides Mailchimp API integration with customer segmentation and email campaigns
 * 
 * Phase 7.4: Business Management Integrations
 */

import oauthManager from '../oauth-manager'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, AuditLogger } from '../security-utils'

/**
 * Mailchimp API Client
 */
export class MailchimpClient {
  constructor(userId) {
    this.userId = userId
    this.baseUrl = null // Will be set based on server prefix
    this.accessToken = null
    this.serverPrefix = null
  }

  /**
   * Initialize Mailchimp client with user credentials
   */
  async initialize() {
    try {
      // Get user's OAuth tokens
      const tokens = await oauthManager.getTokens(this.userId, 'mailchimp')
      
      if (!tokens) {
        throw new Error('No Mailchimp credentials found for user')
      }

      // Check if tokens need refresh
      if (oauthManager.needsRefresh(tokens.expires_at)) {
        const refreshedTokens = await oauthManager.refreshTokens(this.userId, 'mailchimp')
        tokens.access_token = refreshedTokens.access_token
      }

      this.accessToken = tokens.access_token

      // Get server prefix from metadata endpoint
      await this.getServerPrefix()

      return true
    } catch (error) {
      console.error('Failed to initialize Mailchimp client:', error)
      
      await AuditLogger.logIntegrationActivity(
        this.userId,
        'mailchimp',
        'initialization_failed',
        'error',
        { error: error.message }
      )
      
      return false
    }
  }

  /**
   * Get server prefix for API calls
   */
  async getServerPrefix() {
    const response = await fetch('https://login.mailchimp.com/oauth2/metadata', {
      headers: {
        'Authorization': `OAuth ${this.accessToken}`
      }
    })

    if (!response.ok) {
      throw new Error('Failed to get Mailchimp server prefix')
    }

    const metadata = await response.json()
    this.serverPrefix = metadata.dc
    this.baseUrl = `https://${this.serverPrefix}.api.mailchimp.com/3.0`
  }

  /**
   * Make authenticated API request to Mailchimp
   */
  async makeRequest(endpoint, method = 'GET', data = null) {
    return await RetryHandler.withRetry(async () => {
      const url = `${this.baseUrl}/${endpoint}`
      
      const options = {
        method,
        headers: {
          'Authorization': `OAuth ${this.accessToken}`,
          'Content-Type': 'application/json'
        }
      }

      if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
        options.body = JSON.stringify(data)
      }

      const response = await fetch(url, options)

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Mailchimp API error: ${response.status} - ${errorText}`)
      }

      return await response.json()
    })
  }

  /**
   * Get account information
   */
  async getAccount() {
    return await this.makeRequest('')
  }

  /**
   * Get all lists (audiences)
   */
  async getLists(count = 100, offset = 0) {
    const params = new URLSearchParams({
      count: count.toString(),
      offset: offset.toString()
    })

    const response = await this.makeRequest(`lists?${params}`)
    
    return {
      lists: response.lists || [],
      totalItems: response.total_items || 0,
      hasMore: (offset + count) < (response.total_items || 0)
    }
  }

  /**
   * Create new list (audience)
   */
  async createList(listData) {
    const list = {
      name: listData.name,
      contact: {
        company: listData.company || 'Ocean Soul Sparkles',
        address1: listData.address1 || '',
        city: listData.city || '',
        state: listData.state || '',
        zip: listData.zip || '',
        country: listData.country || 'US'
      },
      permission_reminder: listData.permissionReminder || 'You are receiving this email because you signed up for Ocean Soul Sparkles updates.',
      campaign_defaults: {
        from_name: listData.fromName || 'Ocean Soul Sparkles',
        from_email: listData.fromEmail,
        subject: listData.defaultSubject || 'Ocean Soul Sparkles Update',
        language: 'en'
      },
      email_type_option: true
    }

    const response = await this.makeRequest('lists', 'POST', list)
    
    await AuditLogger.logIntegrationActivity(
      this.userId,
      'mailchimp',
      'list_created',
      'success',
      { listId: response.id, listName: listData.name }
    )

    return response
  }

  /**
   * Get list members
   */
  async getListMembers(listId, count = 100, offset = 0) {
    const params = new URLSearchParams({
      count: count.toString(),
      offset: offset.toString()
    })

    const response = await this.makeRequest(`lists/${listId}/members?${params}`)
    
    return {
      members: response.members || [],
      totalItems: response.total_items || 0,
      hasMore: (offset + count) < (response.total_items || 0)
    }
  }

  /**
   * Add member to list
   */
  async addMemberToList(listId, memberData) {
    const member = {
      email_address: memberData.email,
      status: memberData.status || 'subscribed',
      merge_fields: {
        FNAME: memberData.firstName || '',
        LNAME: memberData.lastName || '',
        PHONE: memberData.phone || '',
        ...memberData.customFields
      },
      tags: memberData.tags || []
    }

    const response = await this.makeRequest(`lists/${listId}/members`, 'POST', member)
    
    await AuditLogger.logIntegrationActivity(
      this.userId,
      'mailchimp',
      'member_added',
      'success',
      { 
        listId,
        email: memberData.email,
        status: memberData.status
      }
    )

    return response
  }

  /**
   * Update list member
   */
  async updateListMember(listId, memberEmail, updateData) {
    const subscriberHash = this.getSubscriberHash(memberEmail)
    
    const response = await this.makeRequest(
      `lists/${listId}/members/${subscriberHash}`,
      'PATCH',
      updateData
    )
    
    await AuditLogger.logIntegrationActivity(
      this.userId,
      'mailchimp',
      'member_updated',
      'success',
      { listId, email: memberEmail }
    )

    return response
  }

  /**
   * Get subscriber hash for email
   */
  getSubscriberHash(email) {
    const crypto = require('crypto')
    return crypto.createHash('md5').update(email.toLowerCase()).digest('hex')
  }

  /**
   * Get campaigns
   */
  async getCampaigns(count = 100, offset = 0, status = null) {
    const params = new URLSearchParams({
      count: count.toString(),
      offset: offset.toString()
    })

    if (status) {
      params.append('status', status)
    }

    const response = await this.makeRequest(`campaigns?${params}`)
    
    return {
      campaigns: response.campaigns || [],
      totalItems: response.total_items || 0,
      hasMore: (offset + count) < (response.total_items || 0)
    }
  }

  /**
   * Create campaign
   */
  async createCampaign(campaignData) {
    const campaign = {
      type: campaignData.type || 'regular',
      recipients: {
        list_id: campaignData.listId
      },
      settings: {
        subject_line: campaignData.subject,
        title: campaignData.title || campaignData.subject,
        from_name: campaignData.fromName || 'Ocean Soul Sparkles',
        reply_to: campaignData.replyTo,
        to_name: campaignData.toName || '*|FNAME|*'
      }
    }

    // Add segmentation if provided
    if (campaignData.segmentOptions) {
      campaign.recipients.segment_opts = campaignData.segmentOptions
    }

    const response = await this.makeRequest('campaigns', 'POST', campaign)
    
    await AuditLogger.logIntegrationActivity(
      this.userId,
      'mailchimp',
      'campaign_created',
      'success',
      { 
        campaignId: response.id,
        subject: campaignData.subject,
        listId: campaignData.listId
      }
    )

    return response
  }

  /**
   * Set campaign content
   */
  async setCampaignContent(campaignId, content) {
    const response = await this.makeRequest(
      `campaigns/${campaignId}/content`,
      'PUT',
      content
    )
    
    await AuditLogger.logIntegrationActivity(
      this.userId,
      'mailchimp',
      'campaign_content_set',
      'success',
      { campaignId }
    )

    return response
  }

  /**
   * Send campaign
   */
  async sendCampaign(campaignId) {
    const response = await this.makeRequest(
      `campaigns/${campaignId}/actions/send`,
      'POST'
    )
    
    await AuditLogger.logIntegrationActivity(
      this.userId,
      'mailchimp',
      'campaign_sent',
      'success',
      { campaignId }
    )

    return response
  }

  /**
   * Get campaign reports
   */
  async getCampaignReports(campaignId) {
    return await this.makeRequest(`reports/${campaignId}`)
  }

  /**
   * Create automation workflow
   */
  async createAutomation(automationData) {
    const automation = {
      recipients: {
        list_id: automationData.listId
      },
      settings: {
        title: automationData.title,
        from_name: automationData.fromName || 'Ocean Soul Sparkles',
        reply_to: automationData.replyTo
      },
      trigger_settings: automationData.triggerSettings
    }

    const response = await this.makeRequest('automations', 'POST', automation)
    
    await AuditLogger.logIntegrationActivity(
      this.userId,
      'mailchimp',
      'automation_created',
      'success',
      { 
        automationId: response.id,
        title: automationData.title,
        listId: automationData.listId
      }
    )

    return response
  }

  /**
   * Sync Ocean Soul Sparkles customers to Mailchimp
   */
  async syncCustomersToMailchimp(customers, listId, segmentTags = []) {
    const syncResults = []

    for (const customer of customers) {
      try {
        // Determine customer segment based on booking history
        const customerTags = [...segmentTags]
        
        if (customer.totalBookings > 5) {
          customerTags.push('VIP Customer')
        }
        
        if (customer.lastBookingDate) {
          const daysSinceLastBooking = Math.floor(
            (new Date() - new Date(customer.lastBookingDate)) / (1000 * 60 * 60 * 24)
          )
          
          if (daysSinceLastBooking > 90) {
            customerTags.push('Inactive')
          } else if (daysSinceLastBooking < 30) {
            customerTags.push('Active')
          }
        }

        const memberData = {
          email: customer.email,
          firstName: customer.firstName,
          lastName: customer.lastName,
          phone: customer.phone,
          status: 'subscribed',
          tags: customerTags,
          customFields: {
            BOOKINGS: customer.totalBookings?.toString() || '0',
            LASTBOOK: customer.lastBookingDate || '',
            TOTALSPENT: customer.totalSpent?.toString() || '0'
          }
        }

        const result = await this.addMemberToList(listId, memberData)
        
        syncResults.push({
          customerId: customer.id,
          email: customer.email,
          success: true,
          mailchimpId: result.id
        })

      } catch (error) {
        console.error(`Failed to sync customer ${customer.email}:`, error)
        
        syncResults.push({
          customerId: customer.id,
          email: customer.email,
          success: false,
          error: error.message
        })
      }
    }

    const successCount = syncResults.filter(r => r.success).length
    
    await AuditLogger.logIntegrationActivity(
      this.userId,
      'mailchimp',
      'customer_sync_completed',
      'success',
      { 
        totalCustomers: customers.length,
        successCount,
        failedCount: customers.length - successCount,
        listId
      }
    )

    return syncResults
  }

  /**
   * Test connection to Mailchimp API
   */
  async testConnection() {
    try {
      if (!await this.initialize()) {
        return { success: false, error: 'Failed to initialize Mailchimp client' }
      }

      // Try to get account info as a test
      const account = await this.getAccount()
      
      await AuditLogger.logIntegrationActivity(
        this.userId,
        'mailchimp',
        'connection_test',
        'success',
        { accountName: account?.account_name }
      )

      return { 
        success: true, 
        account,
        message: 'Mailchimp connection successful'
      }
    } catch (error) {
      await AuditLogger.logIntegrationActivity(
        this.userId,
        'mailchimp',
        'connection_test',
        'error',
        { error: error.message }
      )

      return { 
        success: false, 
        error: error.message 
      }
    }
  }
}

export default MailchimpClient
