/* Communication Center Styles - Phase 8: Advanced Customer Experience */

.communicationCenter {
  display: flex;
  height: 600px;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* Empty State */
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  width: 100%;
}

.emptyIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.emptyTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

.emptyDescription {
  color: #666;
  margin-bottom: 2rem;
  line-height: 1.5;
}

.startConversationButton {
  background: #4ECDC4;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.startConversationButton:hover {
  background: #44A08D;
  transform: translateY(-1px);
}

/* Conversations Sidebar */
.conversationsSidebar {
  width: 300px;
  border-right: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}

.sidebarHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
  background: white;
}

.sidebarTitle {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.newConversationButton {
  background: #4ECDC4;
  color: white;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.2rem;
}

.newConversationButton:hover {
  background: #44A08D;
  transform: scale(1.1);
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  gap: 1rem;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #4ECDC4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.conversationsList {
  flex: 1;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #ccc transparent;
}

.conversationsList::-webkit-scrollbar {
  width: 6px;
}

.conversationsList::-webkit-scrollbar-track {
  background: transparent;
}

.conversationsList::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 3px;
}

.conversationItem {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid #e9ecef;
}

.conversationItem:hover {
  background: rgba(78, 205, 196, 0.05);
}

.conversationItem.selected {
  background: rgba(78, 205, 196, 0.1);
  border-right: 3px solid #4ECDC4;
}

.conversationInfo {
  flex: 1;
  min-width: 0;
}

.conversationTitle {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.25rem 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversationPreview {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversationMeta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.conversationTime {
  font-size: 0.8rem;
  color: #999;
}

.unreadBadge {
  background: #ff4757;
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

/* Messages Area */
.messagesArea {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.noConversationSelected {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  height: 100%;
}

.noConversationIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.noConversationTitle {
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.noConversationDescription {
  color: #666;
  line-height: 1.5;
}

/* New Conversation Modal */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 12px;
  padding: 0;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e9ecef;
}

.modalTitle {
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #666;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.closeButton:hover {
  background: #f8f9fa;
  color: #333;
}

.modalContent {
  padding: 1.5rem;
}

.modalDescription {
  color: #666;
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.conversationTopics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.topicButton {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border: 2px solid transparent;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.topicButton:hover {
  background: rgba(78, 205, 196, 0.05);
  border-color: #4ECDC4;
  transform: translateY(-2px);
}

.topicIcon {
  font-size: 2rem;
}

.topicLabel {
  font-weight: 500;
  color: #333;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .communicationCenter {
    flex-direction: column;
    height: 500px;
  }

  .conversationsSidebar {
    width: 100%;
    height: 200px;
    border-right: none;
    border-bottom: 1px solid #e9ecef;
  }

  .conversationItem {
    padding: 0.75rem;
  }

  .conversationTitle {
    font-size: 0.9rem;
  }

  .conversationPreview {
    font-size: 0.8rem;
  }

  .modal {
    width: 95%;
    margin: 1rem;
  }

  .conversationTopics {
    grid-template-columns: 1fr;
  }

  .topicButton {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .communicationCenter {
    height: 400px;
  }

  .conversationsSidebar {
    height: 150px;
  }

  .sidebarHeader {
    padding: 0.75rem;
  }

  .sidebarTitle {
    font-size: 1rem;
  }

  .newConversationButton {
    width: 28px;
    height: 28px;
    font-size: 1rem;
  }

  .conversationItem {
    padding: 0.5rem;
  }

  .emptyState,
  .noConversationSelected {
    padding: 2rem 1rem;
  }

  .emptyIcon,
  .noConversationIcon {
    font-size: 2.5rem;
  }

  .emptyTitle,
  .noConversationTitle {
    font-size: 1.2rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .communicationCenter {
    background: #2d2d2d;
  }

  .conversationsSidebar {
    background: #3d3d3d;
    border-right-color: #444;
  }

  .sidebarHeader {
    background: #2d2d2d;
    border-bottom-color: #444;
  }

  .sidebarTitle,
  .conversationTitle,
  .emptyTitle,
  .noConversationTitle,
  .modalTitle {
    color: #fff;
  }

  .conversationPreview,
  .emptyDescription,
  .noConversationDescription,
  .modalDescription {
    color: #ccc;
  }

  .conversationItem {
    border-bottom-color: #444;
  }

  .conversationItem:hover {
    background: rgba(78, 205, 196, 0.1);
  }

  .modal {
    background: #2d2d2d;
  }

  .modalHeader {
    border-bottom-color: #444;
  }

  .closeButton {
    color: #ccc;
  }

  .closeButton:hover {
    background: #444;
    color: #fff;
  }

  .topicButton {
    background: #3d3d3d;
    color: #fff;
  }

  .topicButton:hover {
    background: rgba(78, 205, 196, 0.1);
  }
}
