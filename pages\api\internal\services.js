import { supabase } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth'; // Placeholder for staff-level auth

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }

  // Authenticate the request - ensure it's a logged-in staff member
  const authResult = await authenticateAdminRequest(req); // Replace/refine if a more specific staff auth is needed

  if (!authResult.authorized) {
    return res.status(401).json({ error: authResult.error?.message || 'Unauthorized' });
  }

  const { role } = authResult;

  // Restrict to roles that might need this internal list.
  // Artists/Braiders need it for specialization. Admins/Devs for management.
  if (!['artist', 'braider', 'admin', 'dev'].includes(role)) {
    return res.status(403).json({ error: 'Forbidden: Access restricted to authorized staff.' });
  }

  try {
    const { data: services, error } = await supabase
      .from('services')
      .select('id, name, category, status, duration, price') // Include fields useful for selection/display
      // Potentially filter out truly 'deleted' or 'archived' services if such a status exists
      // For now, fetching most services to allow selection.
      // Add .eq('status', 'active') if artists should only pick from active services.
      // Or add more complex filtering based on query params if needed in future.
      .order('category')
      .order('name');

    if (error) {
      console.error('Error fetching internal services list:', error);
      throw error;
    }

    res.status(200).json(services || []);

  } catch (error) {
    console.error('Error in /api/internal/services:', error);
    res.status(500).json({ error: 'Failed to fetch services list', details: error.message });
  }
}
