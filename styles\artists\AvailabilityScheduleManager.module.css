/* styles/artists/AvailabilityScheduleManager.module.css */
.scheduleManager {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.scheduleManager h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
  text-align: center;
}

.daySection {
  margin-bottom: 25px;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background-color: #f9f9f9;
}

.daySection h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #555;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.unavailableText {
  color: #888;
  font-style: italic;
  text-align: center;
  padding: 10px 0;
}

.timeSlot {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
  padding: 8px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.timeInput {
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 0.95rem;
  width: 120px; /* Adjust as needed */
}

.timeSeparator {
  color: #555;
}

.removeButton,
.addButton,
.unavailableButton {
  padding: 6px 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.removeButton {
  background-color: #fbe0e0; /* Light red */
  color: #c0392b;
  margin-left: auto; /* Pushes to the right */
}
.removeButton:hover {
  background-color: #f8caca;
}

.dayActions {
  margin-top: 10px;
  display: flex;
  gap: 10px;
}

.addButton {
  background-color: #e0f0e0; /* Light green */
  color: #27ae60;
}
.addButton:hover {
  background-color: #c8e6c9;
}

.unavailableButton {
    background-color: #f9e79f; /* Light yellow */
    color: #f39c12;
}
.unavailableButton:hover {
    background-color: #f7dc6f;
}

.formActions {
  margin-top: 30px;
  text-align: center;
}

.saveButton {
  padding: 12px 25px;
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
}

.saveButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.saveButton:hover:not(:disabled) {
  background: linear-gradient(135deg, #5a78e4, #8f62cb);
}
