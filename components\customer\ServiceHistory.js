/**
 * Service History Component - Phase 8: Advanced Customer Experience
 * Ocean Soul Sparkles - Customer Service History Display
 */

import { useState } from 'react'
import Link from 'next/link'
import { useMobileOptimization } from '@/lib/hooks/useMobileOptimization'
import { toast } from 'react-toastify'
import styles from '@/styles/customer/ServiceHistory.module.css'

export default function ServiceHistory({ history, customer, onHistoryUpdate }) {
  const { isMobile, viewport } = useMobileOptimization()
  
  const [selectedService, setSelectedService] = useState(null)
  const [showRatingModal, setShowRatingModal] = useState(false)
  const [rating, setRating] = useState(0)
  const [review, setReview] = useState('')
  const [loading, setLoading] = useState(false)

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-AU', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatTime = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleTimeString('en-AU', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const renderStars = (score, interactive = false, size = 'medium') => {
    const stars = []
    const maxStars = 5
    
    for (let i = 1; i <= maxStars; i++) {
      stars.push(
        <span
          key={i}
          className={`${styles.star} ${
            i <= score ? styles.filled : styles.empty
          } ${interactive ? styles.interactive : ''} ${styles[size]}`}
          onClick={interactive ? () => setRating(i) : undefined}
        >
          ⭐
        </span>
      )
    }
    
    return <div className={styles.starRating}>{stars}</div>
  }

  const handleRateService = (service) => {
    setSelectedService(service)
    setRating(service.customer_satisfaction_score || 0)
    setReview(service.customer_notes || '')
    setShowRatingModal(true)
  }

  const submitRating = async () => {
    if (!selectedService || rating === 0) {
      toast.error('Please provide a rating')
      return
    }

    try {
      setLoading(true)
      
      const response = await fetch('/api/customer/service-history/rate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          service_history_id: selectedService.id,
          rating: rating,
          review: review.trim()
        })
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Rating submitted successfully!')
        setShowRatingModal(false)
        setSelectedService(null)
        setRating(0)
        setReview('')
        
        if (onHistoryUpdate) {
          onHistoryUpdate()
        }
      } else {
        throw new Error(data.error || 'Failed to submit rating')
      }
    } catch (error) {
      console.error('Error submitting rating:', error)
      toast.error('Failed to submit rating')
    } finally {
      setLoading(false)
    }
  }

  const handleBookAgain = (service) => {
    // Navigate to booking page with pre-selected service
    const params = new URLSearchParams({
      service: service.service?.id || '',
      artist: service.artist?.id || ''
    })
    window.location.href = `/book-online?${params.toString()}`
  }

  if (!history || history.length === 0) {
    return (
      <div className={styles.emptyState}>
        <div className={styles.emptyIcon}>📋</div>
        <h3 className={styles.emptyTitle}>No Service History</h3>
        <p className={styles.emptyDescription}>
          Your completed services will appear here.
        </p>
        <Link href="/book-online" className={styles.bookNowButton}>
          Book Your First Service
        </Link>
      </div>
    )
  }

  return (
    <div className={styles.serviceHistory}>
      <div className={styles.header}>
        <h2 className={styles.title}>Service History</h2>
        <span className={styles.count}>
          {history.length} service{history.length > 1 ? 's' : ''} completed
        </span>
      </div>

      <div className={styles.historyList}>
        {history.map(service => (
          <div key={service.id} className={styles.serviceCard}>
            {/* Service Header */}
            <div className={styles.serviceHeader}>
              <div className={styles.serviceInfo}>
                <h3 className={styles.serviceName}>
                  {service.service?.name || 'Service'}
                </h3>
                <div className={styles.serviceDate}>
                  {formatDate(service.service_date)} at {formatTime(service.service_date)}
                </div>
              </div>
              
              {service.customer_satisfaction_score ? (
                <div className={styles.existingRating}>
                  {renderStars(service.customer_satisfaction_score, false, 'small')}
                </div>
              ) : (
                <button
                  onClick={() => handleRateService(service)}
                  className={styles.rateButton}
                >
                  Rate Service
                </button>
              )}
            </div>

            {/* Service Details */}
            <div className={styles.serviceDetails}>
              {service.artist && (
                <div className={styles.detailRow}>
                  <span className={styles.detailIcon}>👤</span>
                  <span className={styles.detailLabel}>Artist:</span>
                  <span className={styles.detailValue}>
                    {service.artist.display_name || service.artist.artist_name}
                  </span>
                </div>
              )}

              {service.service_duration && (
                <div className={styles.detailRow}>
                  <span className={styles.detailIcon}>⏱️</span>
                  <span className={styles.detailLabel}>Duration:</span>
                  <span className={styles.detailValue}>
                    {service.service_duration} minutes
                  </span>
                </div>
              )}

              {service.service_outcome && (
                <div className={styles.detailRow}>
                  <span className={styles.detailIcon}>✨</span>
                  <span className={styles.detailLabel}>Outcome:</span>
                  <span className={styles.detailValue}>
                    {service.service_outcome}
                  </span>
                </div>
              )}

              {service.customer_notes && (
                <div className={styles.detailRow}>
                  <span className={styles.detailIcon}>📝</span>
                  <span className={styles.detailLabel}>Your Notes:</span>
                  <span className={styles.detailValue}>
                    {service.customer_notes}
                  </span>
                </div>
              )}

              {service.artist_notes && (
                <div className={styles.detailRow}>
                  <span className={styles.detailIcon}>💬</span>
                  <span className={styles.detailLabel}>Artist Notes:</span>
                  <span className={styles.detailValue}>
                    {service.artist_notes}
                  </span>
                </div>
              )}
            </div>

            {/* Service Photos */}
            {(service.before_photos?.length > 0 || service.after_photos?.length > 0) && (
              <div className={styles.servicePhotos}>
                {service.before_photos?.length > 0 && (
                  <div className={styles.photoSection}>
                    <h4 className={styles.photoTitle}>Before</h4>
                    <div className={styles.photoGrid}>
                      {service.before_photos.slice(0, 3).map((photo, index) => (
                        <img
                          key={index}
                          src={photo}
                          alt={`Before photo ${index + 1}`}
                          className={styles.servicePhoto}
                        />
                      ))}
                    </div>
                  </div>
                )}

                {service.after_photos?.length > 0 && (
                  <div className={styles.photoSection}>
                    <h4 className={styles.photoTitle}>After</h4>
                    <div className={styles.photoGrid}>
                      {service.after_photos.slice(0, 3).map((photo, index) => (
                        <img
                          key={index}
                          src={photo}
                          alt={`After photo ${index + 1}`}
                          className={styles.servicePhoto}
                        />
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Service Actions */}
            <div className={styles.serviceActions}>
              <button
                onClick={() => handleBookAgain(service)}
                className={styles.bookAgainButton}
              >
                Book Again
              </button>

              <Link
                href={`/customer/history/${service.id}`}
                className={styles.viewDetailsButton}
              >
                View Details
              </Link>
            </div>
          </div>
        ))}
      </div>

      {/* Rating Modal */}
      {showRatingModal && selectedService && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <div className={styles.modalHeader}>
              <h3 className={styles.modalTitle}>Rate Your Service</h3>
              <button
                onClick={() => setShowRatingModal(false)}
                className={styles.closeButton}
              >
                ×
              </button>
            </div>

            <div className={styles.modalContent}>
              <div className={styles.serviceInfo}>
                <h4>{selectedService.service?.name}</h4>
                <p>{formatDate(selectedService.service_date)}</p>
              </div>

              <div className={styles.ratingSection}>
                <label className={styles.ratingLabel}>Your Rating:</label>
                {renderStars(rating, true, 'large')}
              </div>

              <div className={styles.reviewSection}>
                <label htmlFor="review" className={styles.reviewLabel}>
                  Your Review (Optional):
                </label>
                <textarea
                  id="review"
                  value={review}
                  onChange={(e) => setReview(e.target.value)}
                  placeholder="Share your experience..."
                  className={styles.reviewTextarea}
                  rows={4}
                />
              </div>
            </div>

            <div className={styles.modalActions}>
              <button
                onClick={() => setShowRatingModal(false)}
                className={styles.cancelButton}
                disabled={loading}
              >
                Cancel
              </button>
              <button
                onClick={submitRating}
                className={styles.submitButton}
                disabled={loading || rating === 0}
              >
                {loading ? 'Submitting...' : 'Submit Rating'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
