#!/usr/bin/env node

/**
 * Phase 7 Database Schema Verification Script
 * Verifies all integration tables and columns are properly created
 * Ocean Soul Sparkles - Phase 7: Advanced Integrations & Ecosystem
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables')
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

console.log('🔍 Phase 7 Database Schema Verification')
console.log('=====================================\n')

/**
 * Verify integration tables exist
 */
async function verifyIntegrationTables() {
  console.log('📋 Verifying Integration Tables...')
  
  const requiredTables = [
    'integration_credentials',
    'integration_logs', 
    'integration_settings',
    'integration_sync_status',
    'security_logs',
    'api_access_logs',
    'rate_limit_requests'
  ]

  for (const tableName of requiredTables) {
    try {
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1)

      if (error) {
        console.log(`❌ Table '${tableName}' - ERROR: ${error.message}`)
      } else {
        console.log(`✅ Table '${tableName}' - EXISTS`)
      }
    } catch (error) {
      console.log(`❌ Table '${tableName}' - ERROR: ${error.message}`)
    }
  }
  console.log('')
}

/**
 * Verify integration columns in existing tables
 */
async function verifyIntegrationColumns() {
  console.log('🔧 Verifying Integration Columns...')

  const tableColumns = {
    'bookings': [
      'quickbooks_customer_id',
      'quickbooks_invoice_id', 
      'accounting_sync_data',
      'accounting_sync_status',
      'calendar_event_id',
      'calendar_sync_data',
      'calendar_sync_status'
    ],
    'customers': [
      'quickbooks_customer_id',
      'mailchimp_subscriber_id',
      'marketing_sync_data',
      'marketing_sync_status',
      'customer_segment',
      'customer_tags',
      'email_subscription_status'
    ],
    'portfolio_items': [
      'social_sync_data',
      'social_sync_status',
      'instagram_post_id',
      'facebook_post_id',
      'social_media_urls'
    ],
    'services': [
      'quickbooks_item_id',
      'accounting_sync_data',
      'accounting_sync_status'
    ]
  }

  for (const [tableName, columns] of Object.entries(tableColumns)) {
    console.log(`\n  Table: ${tableName}`)
    
    for (const columnName of columns) {
      try {
        const { data, error } = await supabase.rpc('check_column_exists', {
          table_name: tableName,
          column_name: columnName
        })

        if (error) {
          // Fallback method - try to query the column
          try {
            await supabase
              .from(tableName)
              .select(columnName)
              .limit(1)
            console.log(`    ✅ Column '${columnName}' - EXISTS`)
          } catch (fallbackError) {
            console.log(`    ❌ Column '${columnName}' - MISSING`)
          }
        } else {
          console.log(`    ✅ Column '${columnName}' - EXISTS`)
        }
      } catch (error) {
        console.log(`    ❌ Column '${columnName}' - ERROR: ${error.message}`)
      }
    }
  }
  console.log('')
}

/**
 * Verify RLS policies are enabled
 */
async function verifyRLSPolicies() {
  console.log('🔒 Verifying RLS Policies...')

  const integrationTables = [
    'integration_credentials',
    'integration_logs',
    'integration_settings', 
    'integration_sync_status',
    'security_logs',
    'api_access_logs',
    'rate_limit_requests'
  ]

  for (const tableName of integrationTables) {
    try {
      const { data, error } = await supabase
        .rpc('check_rls_enabled', { table_name: tableName })

      if (error) {
        console.log(`  ❌ RLS for '${tableName}' - ERROR: ${error.message}`)
      } else if (data) {
        console.log(`  ✅ RLS for '${tableName}' - ENABLED`)
      } else {
        console.log(`  ⚠️  RLS for '${tableName}' - DISABLED`)
      }
    } catch (error) {
      console.log(`  ❌ RLS for '${tableName}' - ERROR: ${error.message}`)
    }
  }
  console.log('')
}

/**
 * Verify indexes exist for performance
 */
async function verifyIndexes() {
  console.log('📊 Verifying Performance Indexes...')

  const requiredIndexes = [
    'idx_integration_credentials_user_id',
    'idx_integration_logs_user_id',
    'idx_integration_settings_user_id',
    'idx_integration_sync_status_user_id',
    'idx_bookings_quickbooks_customer_id',
    'idx_customers_quickbooks_customer_id',
    'idx_customers_mailchimp_subscriber_id',
    'idx_portfolio_items_social_sync_status'
  ]

  for (const indexName of requiredIndexes) {
    try {
      const { data, error } = await supabase
        .rpc('check_index_exists', { index_name: indexName })

      if (error) {
        console.log(`  ❌ Index '${indexName}' - ERROR: ${error.message}`)
      } else if (data) {
        console.log(`  ✅ Index '${indexName}' - EXISTS`)
      } else {
        console.log(`  ⚠️  Index '${indexName}' - MISSING`)
      }
    } catch (error) {
      console.log(`  ❌ Index '${indexName}' - ERROR: ${error.message}`)
    }
  }
  console.log('')
}

/**
 * Test integration data operations
 */
async function testIntegrationOperations() {
  console.log('🧪 Testing Integration Operations...')

  try {
    // Test integration_settings insert/select
    const testSettings = {
      user_id: '00000000-0000-0000-0000-000000000000', // Test UUID
      provider: 'test_provider',
      settings: { test: true },
      enabled: true
    }

    const { data: insertData, error: insertError } = await supabase
      .from('integration_settings')
      .insert(testSettings)
      .select()

    if (insertError) {
      console.log(`  ❌ Integration settings insert - ERROR: ${insertError.message}`)
    } else {
      console.log(`  ✅ Integration settings insert - SUCCESS`)
      
      // Clean up test data
      await supabase
        .from('integration_settings')
        .delete()
        .eq('provider', 'test_provider')
    }

    // Test integration_logs insert
    const testLog = {
      user_id: '00000000-0000-0000-0000-000000000000',
      provider: 'test_provider',
      action: 'test_action',
      status: 'success',
      details: { test: true }
    }

    const { data: logData, error: logError } = await supabase
      .from('integration_logs')
      .insert(testLog)
      .select()

    if (logError) {
      console.log(`  ❌ Integration logs insert - ERROR: ${logError.message}`)
    } else {
      console.log(`  ✅ Integration logs insert - SUCCESS`)
      
      // Clean up test data
      await supabase
        .from('integration_logs')
        .delete()
        .eq('provider', 'test_provider')
    }

  } catch (error) {
    console.log(`  ❌ Integration operations test - ERROR: ${error.message}`)
  }
  console.log('')
}

/**
 * Generate migration recommendations
 */
function generateRecommendations() {
  console.log('💡 Migration Recommendations')
  console.log('============================')
  console.log('')
  console.log('1. **Apply Integration Columns Migration**:')
  console.log('   Run: db/migrations/phase7_integration_columns.sql')
  console.log('')
  console.log('2. **Verify OAuth Foundation**:')
  console.log('   Ensure: db/migrations/phase7_oauth_foundation.sql was applied')
  console.log('')
  console.log('3. **Test Integration Endpoints**:')
  console.log('   - Test OAuth flows for each provider')
  console.log('   - Verify API endpoints respond correctly')
  console.log('   - Check rate limiting functionality')
  console.log('')
  console.log('4. **Production Deployment**:')
  console.log('   - Apply migrations during maintenance window')
  console.log('   - Monitor integration performance')
  console.log('   - Verify RLS policies in production')
  console.log('')
}

/**
 * Main execution
 */
async function main() {
  try {
    // Test database connection
    const { data, error } = await supabase
      .from('admin_settings')
      .select('setting_key')
      .limit(1)

    if (error) {
      console.error('❌ Database connection failed:', error.message)
      return
    }

    console.log('✅ Database connection successful\n')

    // Run all verification steps
    await verifyIntegrationTables()
    await verifyIntegrationColumns()
    await verifyRLSPolicies()
    await verifyIndexes()
    await testIntegrationOperations()

    // Generate recommendations
    generateRecommendations()

    console.log('🎉 Phase 7 Database Schema Verification Complete!')

  } catch (error) {
    console.error('❌ Verification failed:', error)
    process.exit(1)
  }
}

main()
