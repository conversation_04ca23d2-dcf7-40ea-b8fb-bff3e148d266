/**
 * Predictive Analytics Models for Ocean Soul Sparkles
 * Implements machine learning algorithms for business intelligence
 */

/**
 * Simple Linear Regression for trend analysis
 */
export class LinearRegression {
  constructor() {
    this.slope = 0;
    this.intercept = 0;
    this.trained = false;
  }

  train(xValues, yValues) {
    if (xValues.length !== yValues.length || xValues.length < 2) {
      throw new Error('Invalid training data');
    }

    const n = xValues.length;
    const sumX = xValues.reduce((a, b) => a + b, 0);
    const sumY = yValues.reduce((a, b) => a + b, 0);
    const sumXY = xValues.reduce((sum, x, i) => sum + x * yValues[i], 0);
    const sumXX = xValues.reduce((sum, x) => sum + x * x, 0);

    this.slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    this.intercept = (sumY - this.slope * sumX) / n;
    this.trained = true;

    return this;
  }

  predict(x) {
    if (!this.trained) {
      throw new Error('Model must be trained before prediction');
    }
    return this.slope * x + this.intercept;
  }

  getR2(xValues, yValues) {
    if (!this.trained) return 0;

    const predictions = xValues.map(x => this.predict(x));
    const yMean = yValues.reduce((a, b) => a + b, 0) / yValues.length;
    
    const totalSumSquares = yValues.reduce((sum, y) => sum + Math.pow(y - yMean, 2), 0);
    const residualSumSquares = yValues.reduce((sum, y, i) => sum + Math.pow(y - predictions[i], 2), 0);
    
    return 1 - (residualSumSquares / totalSumSquares);
  }
}

/**
 * Moving Average for smoothing time series data
 */
export class MovingAverage {
  constructor(windowSize = 7) {
    this.windowSize = windowSize;
  }

  calculate(data) {
    if (data.length < this.windowSize) {
      return data;
    }

    const result = [];
    for (let i = this.windowSize - 1; i < data.length; i++) {
      const window = data.slice(i - this.windowSize + 1, i + 1);
      const average = window.reduce((a, b) => a + b, 0) / window.length;
      result.push(average);
    }

    return result;
  }

  exponential(data, alpha = 0.3) {
    if (data.length === 0) return [];
    
    const result = [data[0]];
    for (let i = 1; i < data.length; i++) {
      const ema = alpha * data[i] + (1 - alpha) * result[i - 1];
      result.push(ema);
    }

    return result;
  }
}

/**
 * Seasonal Decomposition for identifying patterns
 */
export class SeasonalAnalysis {
  constructor(seasonLength = 7) {
    this.seasonLength = seasonLength;
  }

  decompose(data) {
    if (data.length < this.seasonLength * 2) {
      return { trend: data, seasonal: new Array(data.length).fill(0), residual: new Array(data.length).fill(0) };
    }

    // Calculate trend using moving average
    const ma = new MovingAverage(this.seasonLength);
    const trend = ma.calculate(data);

    // Pad trend array to match original data length
    const paddedTrend = new Array(Math.floor(this.seasonLength / 2)).fill(trend[0])
      .concat(trend)
      .concat(new Array(Math.ceil(this.seasonLength / 2)).fill(trend[trend.length - 1]));

    // Calculate seasonal component
    const detrended = data.map((value, i) => value - paddedTrend[i]);
    const seasonal = this.calculateSeasonalComponent(detrended);

    // Calculate residual
    const residual = data.map((value, i) => value - paddedTrend[i] - seasonal[i]);

    return {
      trend: paddedTrend,
      seasonal,
      residual
    };
  }

  calculateSeasonalComponent(detrended) {
    const seasonalAverages = new Array(this.seasonLength).fill(0);
    const counts = new Array(this.seasonLength).fill(0);

    // Calculate average for each seasonal position
    detrended.forEach((value, i) => {
      const seasonIndex = i % this.seasonLength;
      seasonalAverages[seasonIndex] += value;
      counts[seasonIndex]++;
    });

    // Normalize by count
    for (let i = 0; i < this.seasonLength; i++) {
      if (counts[i] > 0) {
        seasonalAverages[i] /= counts[i];
      }
    }

    // Repeat seasonal pattern for full data length
    return detrended.map((_, i) => seasonalAverages[i % this.seasonLength]);
  }

  detectSeasonality(data) {
    const decomposition = this.decompose(data);
    const seasonalVariance = this.calculateVariance(decomposition.seasonal);
    const residualVariance = this.calculateVariance(decomposition.residual);
    
    return {
      hasSeasonality: seasonalVariance > residualVariance,
      seasonalStrength: seasonalVariance / (seasonalVariance + residualVariance),
      seasonalPattern: decomposition.seasonal.slice(0, this.seasonLength)
    };
  }

  calculateVariance(data) {
    const mean = data.reduce((a, b) => a + b, 0) / data.length;
    return data.reduce((sum, value) => sum + Math.pow(value - mean, 2), 0) / data.length;
  }
}

/**
 * Demand Forecasting Model
 */
export class DemandForecaster {
  constructor() {
    this.model = new LinearRegression();
    this.seasonalAnalysis = new SeasonalAnalysis();
    this.movingAverage = new MovingAverage();
  }

  forecast(historicalData, periodsAhead = 7) {
    if (historicalData.length < 14) {
      // Not enough data for complex forecasting, use simple average
      const average = historicalData.reduce((a, b) => a + b, 0) / historicalData.length;
      return new Array(periodsAhead).fill(average);
    }

    // Decompose the data
    const decomposition = this.seasonalAnalysis.decompose(historicalData);
    
    // Forecast trend using linear regression
    const timePoints = historicalData.map((_, i) => i);
    this.model.train(timePoints, decomposition.trend);

    const forecasts = [];
    for (let i = 0; i < periodsAhead; i++) {
      const futureTime = historicalData.length + i;
      const trendForecast = this.model.predict(futureTime);
      const seasonalComponent = decomposition.seasonal[i % this.seasonalAnalysis.seasonLength];
      
      forecasts.push(Math.max(0, trendForecast + seasonalComponent));
    }

    return forecasts;
  }

  calculateConfidenceInterval(historicalData, forecasts, confidence = 0.95) {
    const residuals = this.calculateResiduals(historicalData);
    const standardError = Math.sqrt(residuals.reduce((sum, r) => sum + r * r, 0) / residuals.length);
    
    const zScore = confidence === 0.95 ? 1.96 : confidence === 0.99 ? 2.58 : 1.64;
    
    return forecasts.map(forecast => ({
      forecast,
      lower: forecast - zScore * standardError,
      upper: forecast + zScore * standardError
    }));
  }

  calculateResiduals(data) {
    const decomposition = this.seasonalAnalysis.decompose(data);
    return decomposition.residual;
  }
}

/**
 * Customer Behavior Analysis
 */
export class CustomerBehaviorAnalyzer {
  constructor() {
    this.segments = [];
  }

  analyzeBookingPatterns(customerData) {
    return customerData.map(customer => {
      const bookings = customer.bookings || [];
      
      return {
        customerId: customer.id,
        frequency: this.calculateBookingFrequency(bookings),
        averageValue: this.calculateAverageBookingValue(bookings),
        seasonality: this.detectCustomerSeasonality(bookings),
        loyalty: this.calculateLoyaltyScore(bookings),
        churnRisk: this.calculateChurnRisk(bookings),
        preferredServices: this.getPreferredServices(bookings),
        preferredTimes: this.getPreferredTimes(bookings)
      };
    });
  }

  calculateBookingFrequency(bookings) {
    if (bookings.length < 2) return 0;
    
    const sortedBookings = bookings.sort((a, b) => new Date(a.date) - new Date(b.date));
    const daysBetween = [];
    
    for (let i = 1; i < sortedBookings.length; i++) {
      const days = (new Date(sortedBookings[i].date) - new Date(sortedBookings[i-1].date)) / (1000 * 60 * 60 * 24);
      daysBetween.push(days);
    }
    
    return daysBetween.reduce((a, b) => a + b, 0) / daysBetween.length;
  }

  calculateAverageBookingValue(bookings) {
    if (bookings.length === 0) return 0;
    const totalValue = bookings.reduce((sum, booking) => sum + (booking.amount || 0), 0);
    return totalValue / bookings.length;
  }

  detectCustomerSeasonality(bookings) {
    const monthlyBookings = new Array(12).fill(0);
    bookings.forEach(booking => {
      const month = new Date(booking.date).getMonth();
      monthlyBookings[month]++;
    });
    
    const maxMonth = monthlyBookings.indexOf(Math.max(...monthlyBookings));
    const minMonth = monthlyBookings.indexOf(Math.min(...monthlyBookings));
    
    return {
      peakMonth: maxMonth,
      lowMonth: minMonth,
      seasonalVariation: Math.max(...monthlyBookings) - Math.min(...monthlyBookings)
    };
  }

  calculateLoyaltyScore(bookings) {
    const factors = {
      recency: this.calculateRecencyScore(bookings),
      frequency: Math.min(bookings.length / 12, 1), // Normalize to yearly frequency
      monetary: this.calculateMonetaryScore(bookings)
    };
    
    return (factors.recency * 0.3 + factors.frequency * 0.4 + factors.monetary * 0.3);
  }

  calculateRecencyScore(bookings) {
    if (bookings.length === 0) return 0;
    
    const lastBooking = Math.max(...bookings.map(b => new Date(b.date)));
    const daysSinceLastBooking = (Date.now() - lastBooking) / (1000 * 60 * 60 * 24);
    
    return Math.max(0, 1 - daysSinceLastBooking / 365); // Score decreases over a year
  }

  calculateMonetaryScore(bookings) {
    const totalValue = bookings.reduce((sum, booking) => sum + (booking.amount || 0), 0);
    return Math.min(totalValue / 1000, 1); // Normalize to $1000 max
  }

  calculateChurnRisk(bookings) {
    const recencyScore = this.calculateRecencyScore(bookings);
    const frequencyTrend = this.calculateFrequencyTrend(bookings);
    
    return 1 - (recencyScore * 0.6 + frequencyTrend * 0.4);
  }

  calculateFrequencyTrend(bookings) {
    if (bookings.length < 4) return 0.5; // Neutral for insufficient data
    
    const sortedBookings = bookings.sort((a, b) => new Date(a.date) - new Date(b.date));
    const midpoint = Math.floor(sortedBookings.length / 2);
    
    const firstHalf = sortedBookings.slice(0, midpoint);
    const secondHalf = sortedBookings.slice(midpoint);
    
    const firstHalfFreq = firstHalf.length / this.getDateRangeInDays(firstHalf);
    const secondHalfFreq = secondHalf.length / this.getDateRangeInDays(secondHalf);
    
    return secondHalfFreq > firstHalfFreq ? 1 : 0; // 1 if increasing, 0 if decreasing
  }

  getDateRangeInDays(bookings) {
    if (bookings.length < 2) return 1;
    const dates = bookings.map(b => new Date(b.date));
    return (Math.max(...dates) - Math.min(...dates)) / (1000 * 60 * 60 * 24) || 1;
  }

  getPreferredServices(bookings) {
    const serviceCounts = {};
    bookings.forEach(booking => {
      const service = booking.service || 'Unknown';
      serviceCounts[service] = (serviceCounts[service] || 0) + 1;
    });
    
    return Object.entries(serviceCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([service, count]) => ({ service, count }));
  }

  getPreferredTimes(bookings) {
    const hourCounts = new Array(24).fill(0);
    bookings.forEach(booking => {
      const hour = new Date(booking.date).getHours();
      hourCounts[hour]++;
    });
    
    const maxHour = hourCounts.indexOf(Math.max(...hourCounts));
    return {
      preferredHour: maxHour,
      distribution: hourCounts
    };
  }
}

/**
 * Revenue Prediction Model
 */
export class RevenuePredictionModel {
  constructor() {
    this.demandForecaster = new DemandForecaster();
    this.seasonalAnalysis = new SeasonalAnalysis();
  }

  predictRevenue(historicalRevenue, bookingData, periodsAhead = 30) {
    // Forecast booking demand
    const bookingCounts = bookingData.map(day => day.bookingCount || 0);
    const bookingForecast = this.demandForecaster.forecast(bookingCounts, periodsAhead);
    
    // Calculate average revenue per booking
    const revenuePerBooking = this.calculateAverageRevenuePerBooking(historicalRevenue, bookingData);
    
    // Predict revenue based on booking forecast
    const revenueForecast = bookingForecast.map(bookings => bookings * revenuePerBooking);
    
    // Apply seasonal adjustments
    const seasonalAdjustment = this.calculateSeasonalRevenueAdjustment(historicalRevenue);
    const adjustedForecast = revenueForecast.map((revenue, i) => 
      revenue * seasonalAdjustment[i % seasonalAdjustment.length]
    );
    
    return {
      forecast: adjustedForecast,
      bookingForecast,
      averageRevenuePerBooking: revenuePerBooking,
      confidence: this.calculateForecastConfidence(historicalRevenue)
    };
  }

  calculateAverageRevenuePerBooking(historicalRevenue, bookingData) {
    const totalRevenue = historicalRevenue.reduce((a, b) => a + b, 0);
    const totalBookings = bookingData.reduce((sum, day) => sum + (day.bookingCount || 0), 0);
    
    return totalBookings > 0 ? totalRevenue / totalBookings : 0;
  }

  calculateSeasonalRevenueAdjustment(historicalRevenue) {
    const decomposition = this.seasonalAnalysis.decompose(historicalRevenue);
    const seasonalPattern = decomposition.seasonal.slice(0, this.seasonalAnalysis.seasonLength);
    
    // Normalize seasonal pattern to multipliers around 1.0
    const average = seasonalPattern.reduce((a, b) => a + b, 0) / seasonalPattern.length;
    return seasonalPattern.map(value => 1 + (value - average) / Math.abs(average || 1));
  }

  calculateForecastConfidence(historicalData) {
    if (historicalData.length < 7) return 0.5;
    
    const variance = this.calculateVariance(historicalData);
    const mean = historicalData.reduce((a, b) => a + b, 0) / historicalData.length;
    const coefficientOfVariation = Math.sqrt(variance) / Math.abs(mean || 1);
    
    // Higher coefficient of variation = lower confidence
    return Math.max(0.1, Math.min(0.9, 1 - coefficientOfVariation));
  }

  calculateVariance(data) {
    const mean = data.reduce((a, b) => a + b, 0) / data.length;
    return data.reduce((sum, value) => sum + Math.pow(value - mean, 2), 0) / data.length;
  }
}
