import { supabase } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';
import { 
  sendNewBookingAlert, 
  sendBookingChangeAlert, 
  sendRevenueMilestoneAlert 
} from '@/lib/artist-notification-service';

/**
 * Artist Alerts API Endpoint
 * Handles artist-specific notification triggers and alerts
 */
export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed' 
    });
  }

  try {
    // Authenticate request
    const authResult = await authenticateAdminRequest(req);
    if (!authResult.success) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const {
      alert_type,
      artist_id,
      booking_id,
      customer_id,
      service_id,
      change_type,
      changes,
      milestone_data
    } = req.body;

    // Validate required fields
    if (!alert_type || !artist_id) {
      return res.status(400).json({
        success: false,
        error: 'Alert type and artist ID are required'
      });
    }

    // Validate alert type
    const validAlertTypes = ['new_booking', 'booking_change', 'revenue_milestone'];
    if (!validAlertTypes.includes(alert_type)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid alert type'
      });
    }

    let result;

    switch (alert_type) {
      case 'new_booking':
        result = await handleNewBookingAlert(artist_id, booking_id, customer_id, service_id);
        break;
      
      case 'booking_change':
        result = await handleBookingChangeAlert(artist_id, booking_id, customer_id, service_id, change_type, changes);
        break;
      
      case 'revenue_milestone':
        result = await handleRevenueMilestoneAlert(artist_id, milestone_data);
        break;
      
      default:
        return res.status(400).json({
          success: false,
          error: 'Unsupported alert type'
        });
    }

    return res.status(200).json(result);

  } catch (error) {
    console.error('Artist alert error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to send artist alert: ' + error.message
    });
  }
}

/**
 * Handle new booking alert
 */
async function handleNewBookingAlert(artistId, bookingId, customerId, serviceId) {
  try {
    // Get booking details
    const { data: booking, error: bookingError } = await supabase
      .from('bookings')
      .select('*')
      .eq('id', bookingId)
      .single();

    if (bookingError) throw bookingError;
    if (!booking) throw new Error('Booking not found');

    // Get artist details
    const { data: artist, error: artistError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', artistId)
      .single();

    if (artistError) throw artistError;
    if (!artist) throw new Error('Artist not found');

    // Get customer details
    const { data: customer, error: customerError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', customerId)
      .single();

    if (customerError) throw customerError;
    if (!customer) throw new Error('Customer not found');

    // Get service details
    const { data: service, error: serviceError } = await supabase
      .from('services')
      .select('*')
      .eq('id', serviceId)
      .single();

    if (serviceError) throw serviceError;
    if (!service) throw new Error('Service not found');

    // Send the alert
    const alertResult = await sendNewBookingAlert(booking, artist, customer, service);

    return {
      success: true,
      alert_type: 'new_booking',
      artist_id: artistId,
      booking_id: bookingId,
      notification_result: alertResult
    };

  } catch (error) {
    console.error('New booking alert error:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Handle booking change alert
 */
async function handleBookingChangeAlert(artistId, bookingId, customerId, serviceId, changeType, changes) {
  try {
    // Validate change type
    const validChangeTypes = ['rescheduled', 'modified', 'cancelled'];
    if (!validChangeTypes.includes(changeType)) {
      throw new Error('Invalid change type');
    }

    // Get booking details
    const { data: booking, error: bookingError } = await supabase
      .from('bookings')
      .select('*')
      .eq('id', bookingId)
      .single();

    if (bookingError) throw bookingError;
    if (!booking) throw new Error('Booking not found');

    // Get artist details
    const { data: artist, error: artistError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', artistId)
      .single();

    if (artistError) throw artistError;
    if (!artist) throw new Error('Artist not found');

    // Get customer details
    const { data: customer, error: customerError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', customerId)
      .single();

    if (customerError) throw customerError;
    if (!customer) throw new Error('Customer not found');

    // Get service details
    const { data: service, error: serviceError } = await supabase
      .from('services')
      .select('*')
      .eq('id', serviceId)
      .single();

    if (serviceError) throw serviceError;
    if (!service) throw new Error('Service not found');

    // Send the alert
    const alertResult = await sendBookingChangeAlert(booking, artist, customer, service, changeType, changes);

    return {
      success: true,
      alert_type: 'booking_change',
      change_type: changeType,
      artist_id: artistId,
      booking_id: bookingId,
      notification_result: alertResult
    };

  } catch (error) {
    console.error('Booking change alert error:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Handle revenue milestone alert
 */
async function handleRevenueMilestoneAlert(artistId, milestoneData) {
  try {
    // Validate milestone data
    if (!milestoneData || !milestoneData.milestone || !milestoneData.current_revenue || !milestoneData.period) {
      throw new Error('Invalid milestone data');
    }

    const { milestone, current_revenue, period } = milestoneData;

    // Get artist details
    const { data: artist, error: artistError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', artistId)
      .single();

    if (artistError) throw artistError;
    if (!artist) throw new Error('Artist not found');

    // Send the alert
    const alertResult = await sendRevenueMilestoneAlert(artist, milestone, current_revenue, period);

    return {
      success: true,
      alert_type: 'revenue_milestone',
      artist_id: artistId,
      milestone_data: milestoneData,
      notification_result: alertResult
    };

  } catch (error) {
    console.error('Revenue milestone alert error:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Trigger artist alerts based on booking events
 * This function can be called from other parts of the system
 */
export async function triggerArtistAlert(alertType, data) {
  try {
    const response = await fetch('/api/notifications/artist-alerts', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        alert_type: alertType,
        ...data
      })
    });

    const result = await response.json();
    return result;

  } catch (error) {
    console.error('Error triggering artist alert:', error);
    return {
      success: false,
      error: error.message
    };
  }
}
