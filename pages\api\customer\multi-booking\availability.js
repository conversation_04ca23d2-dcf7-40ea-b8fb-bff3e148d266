/**
 * Multi-Service Booking Availability API - Phase 8: Advanced Customer Experience
 * Ocean Soul Sparkles - Advanced Booking Availability
 */

import { getAdminClient } from '@/lib/supabase'
import { getCurrentUser } from '@/lib/auth'

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET'])
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Get authenticated user
    const user = await getCurrentUser(req)
    if (!user) {
      return res.status(401).json({ error: 'Authentication required' })
    }

    const { services, date, duration_buffer = 15 } = req.query

    // Validate required parameters
    if (!services || !date) {
      return res.status(400).json({ 
        error: 'Missing required parameters: services, date' 
      })
    }

    // Parse service IDs
    const serviceIds = services.split(',').filter(id => id.trim())
    if (serviceIds.length === 0) {
      return res.status(400).json({ error: 'No valid service IDs provided' })
    }

    // Validate date format
    const bookingDate = new Date(date)
    if (isNaN(bookingDate.getTime())) {
      return res.status(400).json({ error: 'Invalid date format' })
    }

    // Check if date is in the past
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    if (bookingDate < today) {
      return res.status(400).json({ error: 'Cannot book for past dates' })
    }

    const adminClient = getAdminClient()
    if (!adminClient) {
      return res.status(500).json({ error: 'Database connection failed' })
    }

    // Get service details
    const { data: serviceDetails, error: serviceError } = await adminClient
      .from('services')
      .select('id, name, duration, category')
      .in('id', serviceIds)

    if (serviceError) {
      console.error('Error fetching service details:', serviceError)
      return res.status(500).json({ error: 'Failed to fetch service details' })
    }

    if (serviceDetails.length !== serviceIds.length) {
      return res.status(400).json({ error: 'Some service IDs are invalid' })
    }

    // Get available artists for these services
    const { data: artists, error: artistError } = await adminClient
      .from('artist_profiles')
      .select(`
        id,
        artist_name,
        display_name,
        specializations,
        is_active,
        is_available_today
      `)
      .eq('is_active', true)
      .eq('is_available_today', true)

    if (artistError) {
      console.error('Error fetching artists:', artistError)
      return res.status(500).json({ error: 'Failed to fetch artists' })
    }

    // Filter artists by service specializations
    const availableArtists = artists.filter(artist => {
      const specializations = artist.specializations || []
      return serviceDetails.some(service => 
        specializations.includes(service.category) || specializations.length === 0
      )
    })

    if (availableArtists.length === 0) {
      return res.status(200).json({
        success: true,
        slots: [],
        message: 'No artists available for the selected services on this date'
      })
    }

    // Get existing bookings for the date
    const startOfDay = new Date(bookingDate)
    startOfDay.setHours(0, 0, 0, 0)
    const endOfDay = new Date(bookingDate)
    endOfDay.setHours(23, 59, 59, 999)

    const { data: existingBookings, error: bookingError } = await adminClient
      .from('bookings')
      .select('artist_id, start_time, end_time, status')
      .gte('start_time', startOfDay.toISOString())
      .lte('start_time', endOfDay.toISOString())
      .in('status', ['confirmed', 'in_progress'])
      .in('artist_id', availableArtists.map(a => a.id))

    if (bookingError) {
      console.error('Error fetching existing bookings:', bookingError)
      return res.status(500).json({ error: 'Failed to fetch existing bookings' })
    }

    // Generate time slots for each service and artist combination
    const availableSlots = []
    const bufferMinutes = parseInt(duration_buffer)

    // Business hours (can be made configurable)
    const businessStart = 9 // 9 AM
    const businessEnd = 18 // 6 PM
    const slotInterval = 30 // 30-minute intervals

    for (const service of serviceDetails) {
      const serviceDuration = service.duration || 60 // Default 60 minutes

      for (const artist of availableArtists) {
        // Check if artist specializes in this service category
        const specializations = artist.specializations || []
        if (specializations.length > 0 && !specializations.includes(service.category)) {
          continue // Skip if artist doesn't specialize in this service
        }

        // Get artist's existing bookings for the day
        const artistBookings = existingBookings.filter(booking => 
          booking.artist_id === artist.id
        )

        // Generate potential time slots
        for (let hour = businessStart; hour < businessEnd; hour++) {
          for (let minute = 0; minute < 60; minute += slotInterval) {
            const slotStart = new Date(bookingDate)
            slotStart.setHours(hour, minute, 0, 0)
            
            const slotEnd = new Date(slotStart)
            slotEnd.setMinutes(slotEnd.getMinutes() + serviceDuration)

            // Check if slot end time is within business hours
            if (slotEnd.getHours() >= businessEnd) {
              continue
            }

            // Check if slot conflicts with existing bookings
            const hasConflict = artistBookings.some(booking => {
              const bookingStart = new Date(booking.start_time)
              const bookingEnd = new Date(booking.end_time)
              
              // Add buffer time
              const bufferedBookingStart = new Date(bookingStart)
              bufferedBookingStart.setMinutes(bufferedBookingStart.getMinutes() - bufferMinutes)
              
              const bufferedBookingEnd = new Date(bookingEnd)
              bufferedBookingEnd.setMinutes(bufferedBookingEnd.getMinutes() + bufferMinutes)

              return (
                (slotStart >= bufferedBookingStart && slotStart < bufferedBookingEnd) ||
                (slotEnd > bufferedBookingStart && slotEnd <= bufferedBookingEnd) ||
                (slotStart <= bufferedBookingStart && slotEnd >= bufferedBookingEnd)
              )
            })

            if (!hasConflict) {
              availableSlots.push({
                service_id: service.id,
                service_name: service.name,
                service_duration: serviceDuration,
                artist_id: artist.id,
                artist_name: artist.display_name || artist.artist_name,
                start: slotStart.toISOString(),
                end: slotEnd.toISOString(),
                date: date,
                available: true
              })
            }
          }
        }
      }
    }

    // Sort slots by service and time
    availableSlots.sort((a, b) => {
      if (a.service_id !== b.service_id) {
        return a.service_id.localeCompare(b.service_id)
      }
      return new Date(a.start) - new Date(b.start)
    })

    // Group slots by service for easier frontend handling
    const slotsByService = {}
    availableSlots.forEach(slot => {
      if (!slotsByService[slot.service_id]) {
        slotsByService[slot.service_id] = []
      }
      slotsByService[slot.service_id].push(slot)
    })

    return res.status(200).json({
      success: true,
      slots: availableSlots,
      slots_by_service: slotsByService,
      services: serviceDetails,
      artists: availableArtists,
      date: date,
      total_slots: availableSlots.length,
      metadata: {
        business_hours: {
          start: businessStart,
          end: businessEnd
        },
        slot_interval: slotInterval,
        duration_buffer: bufferMinutes,
        generated_at: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('Error in multi-booking availability:', error)
    return res.status(500).json({ 
      error: 'Internal server error',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}
