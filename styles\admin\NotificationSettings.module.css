/* Notification Settings Component Styles */

.notificationSettings {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header {
  margin-bottom: 30px;
  text-align: center;
}

.header h2 {
  color: #2c3e50;
  margin-bottom: 8px;
  font-size: 28px;
  font-weight: 600;
}

.header p {
  color: #6b7280;
  font-size: 16px;
  margin: 0;
}

/* Loading State */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Tabs */
.tabs {
  display: flex;
  border-bottom: 2px solid #e5e7eb;
  margin-bottom: 30px;
  overflow-x: auto;
}

.tab {
  padding: 12px 24px;
  background: none;
  border: none;
  color: #6b7280;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  border-bottom: 3px solid transparent;
}

.tab:hover {
  color: #3b82f6;
  background-color: #f8fafc;
}

.tab.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
  background-color: #f8fafc;
}

/* Tab Content */
.tabContent {
  min-height: 400px;
}

.section {
  padding: 20px 0;
}

.section h3 {
  color: #1f2937;
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 20px;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 10px;
}

.section h4 {
  color: #374151;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
  margin-top: 24px;
}

/* Setting Groups */
.settingGroup {
  margin-bottom: 24px;
  padding: 20px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.settingGroup:hover {
  background: #f3f4f6;
  transition: background-color 0.2s ease;
}

/* Toggle Switch */
.toggleLabel {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 8px;
}

.toggleLabel input[type="checkbox"] {
  display: none;
}

.toggle {
  position: relative;
  width: 50px;
  height: 24px;
  background: #d1d5db;
  border-radius: 12px;
  margin-right: 12px;
  transition: background-color 0.3s ease;
}

.toggle::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  transition: transform 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggleLabel input[type="checkbox"]:checked + .toggle {
  background: #3b82f6;
}

.toggleLabel input[type="checkbox"]:checked + .toggle::after {
  transform: translateX(26px);
}

.description {
  color: #6b7280;
  font-size: 14px;
  margin: 0;
  line-height: 1.5;
}

/* Input Fields */
.phoneInput,
.reminderTime {
  margin-top: 12px;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #d1d5db;
}

.phoneInput label,
.reminderTime label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.phoneInput input,
.reminderTime select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
  background: white;
}

.phoneInput input:focus,
.reminderTime select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Quiet Hours Configuration */
.quietHoursConfig {
  margin-top: 12px;
  padding: 16px;
  background: white;
  border-radius: 6px;
  border: 1px solid #d1d5db;
}

.timeInputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.timeInputs > div {
  display: flex;
  flex-direction: column;
}

.timeInputs label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.timeInputs input[type="time"] {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
  background: white;
}

.timeInputs input[type="time"]:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Channel Configuration */
.channelConfig {
  margin-top: 16px;
}

.priorityGroup {
  margin-bottom: 20px;
  padding: 16px;
  background: white;
  border-radius: 6px;
  border: 1px solid #d1d5db;
}

.priorityGroup > label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
}

.channelOptions {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
}

.checkboxLabel input[type="checkbox"] {
  margin-right: 8px;
  width: 16px;
  height: 16px;
  accent-color: #3b82f6;
}

/* Actions */
.actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
  gap: 16px;
}

.testButton {
  padding: 12px 24px;
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.testButton:hover:not(:disabled) {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.testButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.saveButton {
  padding: 12px 32px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.saveButton:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.saveButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .notificationSettings {
    margin: 0;
    padding: 16px;
    border-radius: 0;
  }

  .tabs {
    margin-bottom: 20px;
  }

  .tab {
    padding: 10px 16px;
    font-size: 14px;
  }

  .section h3 {
    font-size: 20px;
  }

  .timeInputs {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .channelOptions {
    flex-direction: column;
    gap: 8px;
  }

  .actions {
    flex-direction: column;
    gap: 12px;
  }

  .testButton,
  .saveButton {
    width: 100%;
    text-align: center;
  }
}
