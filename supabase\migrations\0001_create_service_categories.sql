-- Migration: Create service_categories table
-- This script creates the service_categories table to store categories for services.

BEGIN;

-- Create the service_categories table
CREATE TABLE IF NOT EXISTS public.service_categories (
  -- Columns
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(), -- Unique identifier for the category
  name TEXT NOT NULL UNIQUE,                       -- Name of the category, must be unique
  description TEXT,                                -- Optional description for the category
  parent_id UUID REFERENCES public.service_categories(id) ON DELETE SET NULL, -- Optional parent category for hierarchies
  created_at TIMESTAMPTZ DEFAULT NOW(),             -- Timestamp of when the category was created
  updated_at TIMESTAMPTZ DEFAULT NOW()              -- Timestamp of when the category was last updated
);

-- Add comments to the table and columns
COMMENT ON TABLE public.service_categories IS 'Stores categories for services, allowing for hierarchical organization.';
COMMENT ON COLUMN public.service_categories.id IS 'Primary key, unique identifier for the service category.';
COMMENT ON COLUMN public.service_categories.name IS 'The name of the service category (e.g., "Hair Styling", "Makeup"). Must be unique.';
COMMENT ON COLUMN public.service_categories.description IS 'A more detailed description of the service category.';
COMMENT ON COLUMN public.service_categories.parent_id IS 'Foreign key referencing another service category, allowing for sub-categories. If null, it is a top-level category.';
COMMENT ON COLUMN public.service_categories.created_at IS 'Timestamp indicating when the category record was created.';
COMMENT ON COLUMN public.service_categories.updated_at IS 'Timestamp indicating when the category record was last updated.';

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION public.trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically update updated_at on row modification
CREATE TRIGGER set_service_categories_updated_at
BEFORE UPDATE ON public.service_categories
FOR EACH ROW
EXECUTE FUNCTION public.trigger_set_timestamp();

-- Add RLS (Row Level Security) policies
-- Enable RLS for the table
ALTER TABLE public.service_categories ENABLE ROW LEVEL SECURITY;

-- Policy: Allow public read access to service categories
CREATE POLICY "Public can read service categories"
  ON public.service_categories FOR SELECT
  TO anon, authenticated
  USING (true);

-- Policy: Allow admin users to manage (insert, update, delete) service categories
-- Assumes a user_roles table and a get_user_role() function exist, or similar auth check.
-- If not, this policy might need adjustment based on the actual auth setup.
CREATE POLICY "Admins can manage service categories"
  ON public.service_categories FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.user_roles
      WHERE user_roles.id = auth.uid() AND user_roles.role IN ('admin', 'dev')
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.user_roles
      WHERE user_roles.id = auth.uid() AND user_roles.role IN ('admin', 'dev')
    )
  );

COMMIT;

-- Verification comment:
-- After running this migration, you should be able to:
-- 1. See the 'service_categories' table in your database schema.
-- 2. Insert data into the table.
-- 3. See the 'updated_at' column automatically update when a row is changed.
-- 4. See RLS policies applied to the table.
