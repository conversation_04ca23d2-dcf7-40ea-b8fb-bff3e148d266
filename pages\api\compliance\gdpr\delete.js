/**
 * GDPR Data Deletion API Endpoint for Ocean Soul Sparkles
 * Handles data erasure requests (Article 17 - Right to be forgotten)
 * 
 * Phase 9.2: Data Protection & Privacy Compliance
 */

import { withAdminAuth } from '@/lib/admin-auth'
import { gdprManager } from '@/lib/compliance/gdpr-manager'

async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { 
      requestType = 'erasure',
      requesterEmail,
      requesterName,
      userId,
      customerId,
      requestDetails = {}
    } = req.body

    if (!requesterEmail) {
      return res.status(400).json({ 
        error: 'Missing required fields',
        message: 'requesterEmail is required'
      })
    }

    if (!userId && !customerId) {
      return res.status(400).json({ 
        error: 'Missing required fields',
        message: 'Either userId or customerId is required'
      })
    }

    // Create GDPR deletion request
    const request = await gdprManager.createGDPRRequest({
      requestType,
      requesterEmail,
      requesterName,
      userId,
      customerId,
      requestDetails
    })

    res.status(200).json({
      success: true,
      requestId: request.requestId,
      verificationRequired: request.verificationRequired,
      deadline: request.deadline,
      message: 'Data deletion request created successfully. Please check your email for verification instructions.',
      warning: 'Some business records may be retained for legal compliance purposes.'
    })

  } catch (error) {
    console.error('GDPR deletion request error:', error)
    res.status(500).json({
      error: 'Failed to create data deletion request',
      message: error.message
    })
  }
}

export default withAdminAuth(handler)
