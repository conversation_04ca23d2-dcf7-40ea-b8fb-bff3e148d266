-- =============================================
-- PUSH NOTIFICATION SYSTEM ENHANCEMENT
-- =============================================
-- This migration enhances the notification system with emergency notifications,
-- advanced preferences, and improved notification tracking.

-- =============================================
-- EMERGENCY NOTIFICATIONS TABLE
-- =============================================

CREATE TABLE IF NOT EXISTS public.emergency_notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  priority TEXT NOT NULL CHECK (priority IN ('high', 'critical')),
  target_audience TEXT NOT NULL CHECK (target_audience IN ('all', 'artists', 'customers', 'admins')),
  channels TEXT[] NOT NULL DEFAULT ARRAY['push', 'email'],
  expires_at TIMESTAMPTZ,
  requires_acknowledgment BOOLEAN DEFAULT FALSE,
  sent_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  sent_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  recipients_count INTEGER DEFAULT 0,
  successful_deliveries INTEGER DEFAULT 0,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'sending', 'sent', 'failed')),
  delivery_results JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for emergency notifications
CREATE INDEX IF NOT EXISTS idx_emergency_notifications_sent_at ON public.emergency_notifications(sent_at DESC);
CREATE INDEX IF NOT EXISTS idx_emergency_notifications_priority ON public.emergency_notifications(priority);
CREATE INDEX IF NOT EXISTS idx_emergency_notifications_target_audience ON public.emergency_notifications(target_audience);
CREATE INDEX IF NOT EXISTS idx_emergency_notifications_status ON public.emergency_notifications(status);

-- =============================================
-- ENHANCED NOTIFICATION PREFERENCES
-- =============================================

-- Add new columns to notification_preferences table
ALTER TABLE public.notification_preferences 
ADD COLUMN IF NOT EXISTS booking_alerts JSONB DEFAULT '{
  "new_booking": true,
  "booking_changes": true,
  "cancellations": true,
  "customer_inquiries": true,
  "schedule_conflicts": true
}'::jsonb;

ALTER TABLE public.notification_preferences 
ADD COLUMN IF NOT EXISTS revenue_alerts JSONB DEFAULT '{
  "daily_summary": false,
  "weekly_summary": true,
  "milestone_notifications": true,
  "payment_received": true
}'::jsonb;

ALTER TABLE public.notification_preferences 
ADD COLUMN IF NOT EXISTS system_alerts JSONB DEFAULT '{
  "maintenance_notifications": true,
  "security_alerts": true,
  "feature_updates": false,
  "emergency_broadcasts": true
}'::jsonb;

ALTER TABLE public.notification_preferences 
ADD COLUMN IF NOT EXISTS quiet_hours JSONB DEFAULT '{
  "enabled": false,
  "start_time": "22:00",
  "end_time": "08:00",
  "timezone": "Australia/Sydney"
}'::jsonb;

ALTER TABLE public.notification_preferences 
ADD COLUMN IF NOT EXISTS notification_channels JSONB DEFAULT '{
  "urgent": ["push", "email"],
  "normal": ["push"],
  "marketing": ["email"]
}'::jsonb;

-- =============================================
-- NOTIFICATION ACKNOWLEDGMENTS TABLE
-- =============================================

CREATE TABLE IF NOT EXISTS public.notification_acknowledgments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  notification_id UUID REFERENCES public.notifications(id) ON DELETE CASCADE,
  emergency_notification_id UUID REFERENCES public.emergency_notifications(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  acknowledged_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  acknowledgment_method TEXT CHECK (acknowledgment_method IN ('click', 'button', 'auto')),
  device_info JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for acknowledgments
CREATE INDEX IF NOT EXISTS idx_notification_acknowledgments_notification_id ON public.notification_acknowledgments(notification_id);
CREATE INDEX IF NOT EXISTS idx_notification_acknowledgments_emergency_id ON public.notification_acknowledgments(emergency_notification_id);
CREATE INDEX IF NOT EXISTS idx_notification_acknowledgments_user_id ON public.notification_acknowledgments(user_id);

-- =============================================
-- NOTIFICATION DELIVERY TRACKING
-- =============================================

CREATE TABLE IF NOT EXISTS public.notification_delivery_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  notification_id UUID REFERENCES public.notifications(id) ON DELETE CASCADE,
  emergency_notification_id UUID REFERENCES public.emergency_notifications(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  channel TEXT NOT NULL CHECK (channel IN ('push', 'email', 'sms')),
  status TEXT NOT NULL CHECK (status IN ('pending', 'sent', 'delivered', 'failed', 'bounced')),
  external_id TEXT, -- OneSignal notification ID or email service ID
  error_message TEXT,
  delivery_attempts INTEGER DEFAULT 1,
  sent_at TIMESTAMPTZ,
  delivered_at TIMESTAMPTZ,
  opened_at TIMESTAMPTZ,
  clicked_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for delivery tracking
CREATE INDEX IF NOT EXISTS idx_notification_delivery_logs_notification_id ON public.notification_delivery_logs(notification_id);
CREATE INDEX IF NOT EXISTS idx_notification_delivery_logs_user_id ON public.notification_delivery_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_delivery_logs_channel ON public.notification_delivery_logs(channel);
CREATE INDEX IF NOT EXISTS idx_notification_delivery_logs_status ON public.notification_delivery_logs(status);
CREATE INDEX IF NOT EXISTS idx_notification_delivery_logs_sent_at ON public.notification_delivery_logs(sent_at DESC);

-- =============================================
-- NOTIFICATION TEMPLATES TABLE
-- =============================================

CREATE TABLE IF NOT EXISTS public.notification_templates (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  category TEXT NOT NULL CHECK (category IN ('booking', 'payment', 'emergency', 'marketing', 'system')),
  title_template TEXT NOT NULL,
  message_template TEXT NOT NULL,
  html_template TEXT,
  variables JSONB DEFAULT '[]'::jsonb,
  is_active BOOLEAN DEFAULT TRUE,
  created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for templates
CREATE INDEX IF NOT EXISTS idx_notification_templates_category ON public.notification_templates(category);
CREATE INDEX IF NOT EXISTS idx_notification_templates_is_active ON public.notification_templates(is_active);

-- =============================================
-- ENHANCED NOTIFICATIONS TABLE
-- =============================================

-- Add new columns to existing notifications table
ALTER TABLE public.notifications 
ADD COLUMN IF NOT EXISTS priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'critical'));

ALTER TABLE public.notifications 
ADD COLUMN IF NOT EXISTS expires_at TIMESTAMPTZ;

ALTER TABLE public.notifications 
ADD COLUMN IF NOT EXISTS requires_acknowledgment BOOLEAN DEFAULT FALSE;

ALTER TABLE public.notifications 
ADD COLUMN IF NOT EXISTS acknowledged_at TIMESTAMPTZ;

ALTER TABLE public.notifications 
ADD COLUMN IF NOT EXISTS channels TEXT[] DEFAULT ARRAY['push'];

ALTER TABLE public.notifications 
ADD COLUMN IF NOT EXISTS template_id UUID REFERENCES public.notification_templates(id) ON DELETE SET NULL;

ALTER TABLE public.notifications 
ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}'::jsonb;

-- Add indexes for enhanced notifications
CREATE INDEX IF NOT EXISTS idx_notifications_priority ON public.notifications(priority);
CREATE INDEX IF NOT EXISTS idx_notifications_expires_at ON public.notifications(expires_at);
CREATE INDEX IF NOT EXISTS idx_notifications_requires_acknowledgment ON public.notifications(requires_acknowledgment);

-- =============================================
-- NOTIFICATION STATISTICS VIEW
-- =============================================

CREATE OR REPLACE VIEW public.notification_statistics AS
SELECT 
  DATE_TRUNC('day', created_at) as date,
  notification_type,
  priority,
  COUNT(*) as total_notifications,
  COUNT(CASE WHEN is_read = true THEN 1 END) as read_notifications,
  COUNT(CASE WHEN acknowledged_at IS NOT NULL THEN 1 END) as acknowledged_notifications,
  ROUND(
    (COUNT(CASE WHEN is_read = true THEN 1 END)::float / COUNT(*)::float) * 100, 
    2
  ) as read_rate,
  ROUND(
    (COUNT(CASE WHEN acknowledged_at IS NOT NULL THEN 1 END)::float / COUNT(*)::float) * 100, 
    2
  ) as acknowledgment_rate
FROM public.notifications
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE_TRUNC('day', created_at), notification_type, priority
ORDER BY date DESC, notification_type, priority;

-- =============================================
-- EMERGENCY NOTIFICATION STATISTICS VIEW
-- =============================================

CREATE OR REPLACE VIEW public.emergency_notification_statistics AS
SELECT 
  DATE_TRUNC('day', sent_at) as date,
  priority,
  target_audience,
  COUNT(*) as total_emergencies,
  SUM(recipients_count) as total_recipients,
  SUM(successful_deliveries) as total_successful,
  ROUND(
    (SUM(successful_deliveries)::float / NULLIF(SUM(recipients_count)::float, 0)) * 100, 
    2
  ) as delivery_rate,
  AVG(EXTRACT(EPOCH FROM (completed_at - sent_at))) as avg_delivery_time_seconds
FROM public.emergency_notifications
WHERE sent_at >= NOW() - INTERVAL '90 days'
GROUP BY DATE_TRUNC('day', sent_at), priority, target_audience
ORDER BY date DESC, priority, target_audience;

-- =============================================
-- FUNCTIONS FOR NOTIFICATION MANAGEMENT
-- =============================================

-- Function to clean up expired notifications
CREATE OR REPLACE FUNCTION cleanup_expired_notifications()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM public.notifications 
  WHERE expires_at IS NOT NULL 
    AND expires_at < NOW() 
    AND is_read = true;
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to get user notification summary
CREATE OR REPLACE FUNCTION get_user_notification_summary(user_uuid UUID)
RETURNS TABLE (
  total_notifications BIGINT,
  unread_notifications BIGINT,
  urgent_notifications BIGINT,
  recent_notifications BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*) as total_notifications,
    COUNT(CASE WHEN is_read = false THEN 1 END) as unread_notifications,
    COUNT(CASE WHEN priority IN ('high', 'critical') AND is_read = false THEN 1 END) as urgent_notifications,
    COUNT(CASE WHEN created_at >= NOW() - INTERVAL '24 hours' THEN 1 END) as recent_notifications
  FROM public.notifications
  WHERE user_id = user_uuid
    AND (expires_at IS NULL OR expires_at > NOW());
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- ROW LEVEL SECURITY POLICIES
-- =============================================

-- Enable RLS on new tables
ALTER TABLE public.emergency_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notification_acknowledgments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notification_delivery_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notification_templates ENABLE ROW LEVEL SECURITY;

-- Emergency notifications policies (admin/dev only)
CREATE POLICY "Emergency notifications viewable by admins" ON public.emergency_notifications
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles 
      WHERE id = auth.uid() 
      AND role IN ('admin', 'dev')
    )
  );

CREATE POLICY "Emergency notifications manageable by admins" ON public.emergency_notifications
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles 
      WHERE id = auth.uid() 
      AND role IN ('admin', 'dev')
    )
  );

-- Notification acknowledgments policies
CREATE POLICY "Users can manage their own acknowledgments" ON public.notification_acknowledgments
  FOR ALL USING (user_id = auth.uid());

-- Delivery logs policies (admin/dev only)
CREATE POLICY "Delivery logs viewable by admins" ON public.notification_delivery_logs
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles 
      WHERE id = auth.uid() 
      AND role IN ('admin', 'dev')
    )
  );

-- Templates policies (admin/dev only)
CREATE POLICY "Templates manageable by admins" ON public.notification_templates
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles 
      WHERE id = auth.uid() 
      AND role IN ('admin', 'dev')
    )
  );

-- =============================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =============================================

-- Update timestamp trigger for emergency notifications
CREATE OR REPLACE FUNCTION update_emergency_notification_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER emergency_notifications_updated_at
  BEFORE UPDATE ON public.emergency_notifications
  FOR EACH ROW
  EXECUTE FUNCTION update_emergency_notification_timestamp();

-- Update timestamp trigger for delivery logs
CREATE OR REPLACE FUNCTION update_delivery_log_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER delivery_logs_updated_at
  BEFORE UPDATE ON public.notification_delivery_logs
  FOR EACH ROW
  EXECUTE FUNCTION update_delivery_log_timestamp();

-- =============================================
-- SAMPLE NOTIFICATION TEMPLATES
-- =============================================

INSERT INTO public.notification_templates (name, category, title_template, message_template, variables) VALUES
('new_booking', 'booking', '🎉 New Booking Received!', 'You have a new booking from {{customer_name}} for {{service_name}} on {{booking_date}} at {{booking_time}}.', '["customer_name", "service_name", "booking_date", "booking_time"]'::jsonb),
('booking_reminder', 'booking', '⏰ Upcoming Appointment', 'Reminder: Your appointment for {{service_name}} is in {{minutes}} minutes at {{location}}.', '["service_name", "minutes", "location"]'::jsonb),
('payment_received', 'payment', '💰 Payment Received', 'Payment of ${{amount}} has been received for {{service_name}}.', '["amount", "service_name"]'::jsonb),
('emergency_weather', 'emergency', '🌧️ Weather Alert', 'Due to severe weather conditions, all outdoor events scheduled for today have been cancelled. Please check your bookings for updates.', '[]'::jsonb),
('system_maintenance', 'system', '🔧 System Maintenance', 'We will be performing system maintenance on {{date}} from {{start_time}} to {{end_time}}. Some features may be temporarily unavailable.', '["date", "start_time", "end_time"]'::jsonb)
ON CONFLICT (name) DO NOTHING;

-- =============================================
-- COMPLETION MESSAGE
-- =============================================

DO $$
BEGIN
  RAISE NOTICE 'Push Notification System Enhancement migration completed successfully!';
  RAISE NOTICE 'New features added:';
  RAISE NOTICE '- Emergency notification system';
  RAISE NOTICE '- Enhanced notification preferences';
  RAISE NOTICE '- Notification acknowledgments';
  RAISE NOTICE '- Delivery tracking';
  RAISE NOTICE '- Notification templates';
  RAISE NOTICE '- Statistics and analytics views';
END $$;
