/* Business Dashboard Component Styles */
/* Ocean Soul Sparkles - Phase 7.4: Business Management Integrations */

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  box-sizing: border-box;
}

.container.mobile {
  padding: 15px;
  max-width: 100%;
}

/* Header */
.header {
  margin-bottom: 30px;
  text-align: center;
}

.header h2 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 2rem;
  font-weight: 600;
}

.header p {
  color: #7f8c8d;
  font-size: 1.1rem;
  margin: 0;
}

/* Loading State */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #27ae60;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Navigation Tabs */
.tabs {
  display: flex;
  border-bottom: 2px solid #ecf0f1;
  margin-bottom: 30px;
  overflow-x: auto;
}

.tab {
  background: none;
  border: none;
  padding: 15px 25px;
  font-size: 1rem;
  font-weight: 500;
  color: #7f8c8d;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  white-space: nowrap;
}

.tab:hover {
  color: #27ae60;
  background-color: rgba(39, 174, 96, 0.1);
}

.tab.active {
  color: #27ae60;
  border-bottom: 3px solid #27ae60;
}

/* Tab Content */
.tabContent {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Integrations Section */
.integrations {
  margin-bottom: 30px;
}

.integrations h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.3rem;
  font-weight: 600;
}

.noIntegrations {
  text-align: center;
  padding: 40px 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 2px dashed #dee2e6;
}

.noIntegrations p {
  color: #6c757d;
  margin-bottom: 20px;
  font-size: 1.1rem;
}

.connectButtons {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.connectButton {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.connectButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
}

/* Integration List */
.integrationList {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.integrationCard {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.integrationCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.integrationInfo h4 {
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.status {
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.9rem;
  display: inline-block;
  margin-bottom: 10px;
}

.status.connected {
  background: #d4edda;
  color: #155724;
}

.status.error {
  background: #f8d7da;
  color: #721c24;
}

.companyInfo {
  margin-top: 10px;
}

.companyInfo p {
  margin: 5px 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.integrationActions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.settingsButton,
.disconnectButton {
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.settingsButton {
  background: #f8f9fa;
  color: #495057;
  border: 1px solid #dee2e6;
}

.settingsButton:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.disconnectButton {
  background: #fff5f5;
  color: #e53e3e;
  border: 1px solid #fed7d7;
}

.disconnectButton:hover {
  background: #fed7d7;
  border-color: #feb2b2;
}

/* Quick Actions */
.quickActions {
  margin-bottom: 30px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 25px;
}

.quickActions h3 {
  color: #2c3e50;
  margin: 0 0 20px 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.actionButtons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.actionButton {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
  border: none;
  padding: 15px 20px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.actionButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
}

.actionButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Summary Cards */
.summaryCards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.summaryCard {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
}

.summaryCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.summaryCard h4 {
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.summaryCard p {
  color: #27ae60;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
}

/* Section Styles */
.accountingSection,
.marketingSection,
.reportsSection {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 25px;
}

.accountingSection h3,
.marketingSection h3,
.reportsSection h3 {
  color: #2c3e50;
  margin: 0 0 20px 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.noConnection,
.noReports {
  text-align: center;
  padding: 40px 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #27ae60;
}

.noConnection p,
.noReports p {
  color: #6c757d;
  margin-bottom: 20px;
}

.accountingContent,
.marketingContent,
.reportsContent {
  margin-top: 20px;
}

.accountingActions,
.marketingActions {
  margin-bottom: 20px;
}

.financialSummary,
.marketingSummary {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #27ae60;
}

.financialSummary h4,
.marketingSummary h4 {
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.financialSummary p,
.marketingSummary p {
  color: #495057;
  margin: 10px 0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }

  .header h2 {
    font-size: 1.6rem;
  }

  .header p {
    font-size: 1rem;
  }

  .tabs {
    margin-bottom: 20px;
  }

  .tab {
    padding: 12px 20px;
    font-size: 0.9rem;
  }

  .connectButtons {
    flex-direction: column;
    align-items: center;
  }

  .integrationList {
    grid-template-columns: 1fr;
  }

  .integrationActions {
    flex-direction: column;
  }

  .actionButtons {
    grid-template-columns: 1fr;
  }

  .summaryCards {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 10px;
  }

  .integrationCard,
  .quickActions,
  .summaryCard,
  .accountingSection,
  .marketingSection,
  .reportsSection {
    padding: 15px;
  }
}
