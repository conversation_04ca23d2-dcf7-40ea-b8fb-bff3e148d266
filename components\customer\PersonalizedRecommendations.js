/**
 * Personalized Recommendations Component - Phase 8: Advanced Customer Experience
 * Ocean Soul Sparkles - AI-Powered Customer Recommendations
 */

import { useState } from 'react'
import Link from 'next/link'
import { useMobileOptimization } from '@/lib/hooks/useMobileOptimization'
import { toast } from 'react-toastify'
import styles from '@/styles/customer/PersonalizedRecommendations.module.css'

export default function PersonalizedRecommendations({ recommendations, customer, onRecommendationAction }) {
  const { isMobile, viewport } = useMobileOptimization()
  
  const [loading, setLoading] = useState({})

  const handleRecommendationAction = async (action, recommendation) => {
    const actionKey = `${action}_${recommendation.id}`
    setLoading(prev => ({ ...prev, [actionKey]: true }))

    try {
      switch (action) {
        case 'book':
          // Navigate to booking with pre-selected service
          const bookingParams = new URLSearchParams({
            service: recommendation.service_id || '',
            artist: recommendation.artist_id || ''
          })
          window.location.href = `/book-online?${bookingParams.toString()}`
          break

        case 'save':
          const response = await fetch('/api/customer/recommendations/save', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ recommendation_id: recommendation.id })
          })
          
          const data = await response.json()
          
          if (data.success) {
            toast.success('Recommendation saved!')
            if (onRecommendationAction) {
              onRecommendationAction(action, recommendation)
            }
          } else {
            throw new Error(data.error || 'Failed to save recommendation')
          }
          break

        case 'dismiss':
          const dismissResponse = await fetch('/api/customer/recommendations/dismiss', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ recommendation_id: recommendation.id })
          })
          
          const dismissData = await dismissResponse.json()
          
          if (dismissData.success) {
            toast.success('Recommendation dismissed')
            if (onRecommendationAction) {
              onRecommendationAction(action, recommendation)
            }
          } else {
            throw new Error(dismissData.error || 'Failed to dismiss recommendation')
          }
          break

        default:
          console.warn('Unknown recommendation action:', action)
      }
    } catch (error) {
      console.error('Error handling recommendation action:', error)
      toast.error('Action failed. Please try again.')
    } finally {
      setLoading(prev => ({ ...prev, [actionKey]: false }))
    }
  }

  const getRecommendationIcon = (type) => {
    switch (type) {
      case 'service':
        return '✨'
      case 'artist':
        return '👤'
      case 'time':
        return '⏰'
      case 'package':
        return '📦'
      case 'seasonal':
        return '🌟'
      default:
        return '💡'
    }
  }

  const getRecommendationReason = (recommendation) => {
    if (recommendation.reason) {
      return recommendation.reason
    }

    switch (recommendation.type) {
      case 'service':
        return 'Based on your previous bookings'
      case 'artist':
        return 'Highly rated by similar customers'
      case 'time':
        return 'Popular time slot for this service'
      case 'package':
        return 'Great value for multiple services'
      case 'seasonal':
        return 'Perfect for this time of year'
      default:
        return 'Recommended for you'
    }
  }

  if (!recommendations || recommendations.length === 0) {
    return (
      <div className={styles.noRecommendations}>
        <div className={styles.noRecommendationsIcon}>🎯</div>
        <h3 className={styles.noRecommendationsTitle}>No Recommendations Yet</h3>
        <p className={styles.noRecommendationsDescription}>
          Book a few services to get personalized recommendations!
        </p>
      </div>
    )
  }

  return (
    <div className={styles.personalizedRecommendations}>
      <div className={styles.header}>
        <h2 className={styles.title}>Recommended for You</h2>
        <p className={styles.subtitle}>
          Personalized suggestions based on your preferences and history
        </p>
      </div>

      <div className={styles.recommendationsGrid}>
        {recommendations.map(recommendation => (
          <div key={recommendation.id} className={styles.recommendationCard}>
            {/* Recommendation Header */}
            <div className={styles.recommendationHeader}>
              <div className={styles.recommendationIcon}>
                {getRecommendationIcon(recommendation.type)}
              </div>
              <div className={styles.recommendationMeta}>
                <span className={styles.recommendationType}>
                  {recommendation.type.toUpperCase()}
                </span>
                {recommendation.confidence_score && (
                  <span className={styles.confidenceScore}>
                    {Math.round(recommendation.confidence_score * 100)}% match
                  </span>
                )}
              </div>
            </div>

            {/* Recommendation Content */}
            <div className={styles.recommendationContent}>
              <h3 className={styles.recommendationTitle}>
                {recommendation.title || recommendation.service_name || 'Recommended Service'}
              </h3>
              
              <p className={styles.recommendationDescription}>
                {recommendation.description || 'A great choice based on your preferences.'}
              </p>

              <div className={styles.recommendationReason}>
                <span className={styles.reasonIcon}>💡</span>
                <span className={styles.reasonText}>
                  {getRecommendationReason(recommendation)}
                </span>
              </div>

              {/* Recommendation Details */}
              <div className={styles.recommendationDetails}>
                {recommendation.service_name && (
                  <div className={styles.detailItem}>
                    <span className={styles.detailIcon}>✨</span>
                    <span className={styles.detailLabel}>Service:</span>
                    <span className={styles.detailValue}>{recommendation.service_name}</span>
                  </div>
                )}

                {recommendation.artist_name && (
                  <div className={styles.detailItem}>
                    <span className={styles.detailIcon}>👤</span>
                    <span className={styles.detailLabel}>Artist:</span>
                    <span className={styles.detailValue}>{recommendation.artist_name}</span>
                  </div>
                )}

                {recommendation.estimated_duration && (
                  <div className={styles.detailItem}>
                    <span className={styles.detailIcon}>⏱️</span>
                    <span className={styles.detailLabel}>Duration:</span>
                    <span className={styles.detailValue}>{recommendation.estimated_duration} min</span>
                  </div>
                )}

                {recommendation.estimated_price && (
                  <div className={styles.detailItem}>
                    <span className={styles.detailIcon}>💰</span>
                    <span className={styles.detailLabel}>Price:</span>
                    <span className={styles.detailValue}>
                      ${parseFloat(recommendation.estimated_price).toFixed(2)}
                    </span>
                  </div>
                )}
              </div>

              {/* Special Offers */}
              {recommendation.special_offer && (
                <div className={styles.specialOffer}>
                  <span className={styles.offerIcon}>🎁</span>
                  <span className={styles.offerText}>{recommendation.special_offer}</span>
                </div>
              )}
            </div>

            {/* Recommendation Actions */}
            <div className={styles.recommendationActions}>
              <button
                onClick={() => handleRecommendationAction('book', recommendation)}
                disabled={loading[`book_${recommendation.id}`]}
                className={styles.bookButton}
              >
                {loading[`book_${recommendation.id}`] ? 'Loading...' : 'Book Now'}
              </button>

              <button
                onClick={() => handleRecommendationAction('save', recommendation)}
                disabled={loading[`save_${recommendation.id}`]}
                className={styles.saveButton}
              >
                {loading[`save_${recommendation.id}`] ? 'Saving...' : 'Save'}
              </button>

              <button
                onClick={() => handleRecommendationAction('dismiss', recommendation)}
                disabled={loading[`dismiss_${recommendation.id}`]}
                className={styles.dismissButton}
              >
                {loading[`dismiss_${recommendation.id}`] ? 'Dismissing...' : 'Not Interested'}
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* View More */}
      <div className={styles.viewMore}>
        <Link href="/customer/recommendations" className={styles.viewMoreButton}>
          View All Recommendations
        </Link>
      </div>
    </div>
  )
}
