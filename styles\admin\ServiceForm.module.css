.serviceForm {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.formHeader {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem 2rem;
  margin: -1rem -1rem 2rem -1rem;
}

.formHeader h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.error {
  background: #fee;
  border: 1px solid #fcc;
  color: #c33;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.success {
  background: #efe;
  border: 1px solid #cfc;
  color: #3c3;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.form {
  padding: 0 2rem 2rem 2rem;
}

.section {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #eee;
}

.section:last-of-type {
  border-bottom: none;
  margin-bottom: 0;
}

.section h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
}

.formRow {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.formGroup label {
  font-weight: 500;
  color: #555;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.input,
.select,
.textarea {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
}

.input:focus,
.select:focus,
.textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.textarea {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.colorInput {
  width: 60px;
  height: 40px;
  padding: 0;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
}

.checkboxLabel {
  flex-direction: row !important;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
}

.checkboxLabel input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.formActions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #eee;
}

.leftActions {
  display: flex;
  gap: 1rem;
}

.rightActions {
  display: flex;
  gap: 1rem;
}

.cancelButton,
.saveButton {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelButton {
  background: #f5f5f5;
  color: #666;
}

.cancelButton:hover {
  background: #e5e5e5;
}

.saveButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.saveButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.deleteButton {
  background: #dc3545;
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.deleteButton:hover {
  background: #c82333;
  transform: translateY(-1px);
}

.saveButton:disabled,
.cancelButton:disabled,
.deleteButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Image upload section */
.imageUpload {
  border: 2px dashed #ddd;
  border-radius: 4px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.imageUpload:hover {
  border-color: #667eea;
}

.imageUpload.dragOver {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.uploadText {
  color: #666;
  font-size: 0.9rem;
}

.imagePreview {
  margin-top: 1rem;
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.previewImage {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #ddd;
}

/* Responsive design */
@media (max-width: 768px) {
  .serviceForm {
    margin: 0;
    border-radius: 0;
  }

  .formHeader {
    margin: -1rem -1rem 1rem -1rem;
    padding: 1rem;
  }

  .form {
    padding: 0 1rem 1rem 1rem;
  }

  .formRow {
    grid-template-columns: 1fr;
  }

  .formActions {
    flex-direction: column;
  }

  .leftActions,
  .rightActions {
    justify-content: center;
  }

  .cancelButton,
  .saveButton,
  .deleteButton {
    width: 100%;
  }
}

/* Additional styling for better UX */
.requiredField {
  color: #e74c3c;
}

.helpText {
  font-size: 0.8rem;
  color: #666;
  margin-top: 0.25rem;
}

.fieldError {
  border-color: #e74c3c !important;
}

.fieldError:focus {
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1) !important;
}

/* Image picker styles */
.imagePreviewContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.imagePreview {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f8f9fa;
}

.previewImage {
  border-radius: 6px;
  border: 1px solid #ddd;
}

.imageActions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.changeImageButton,
.removeImageButton,
.selectImageButton {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.changeImageButton,
.selectImageButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.changeImageButton:hover,
.selectImageButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.removeImageButton {
  background: #dc3545;
  color: white;
}

.removeImageButton:hover {
  background: #c82333;
  transform: translateY(-1px);
}

.imagePath {
  font-size: 0.85rem;
  color: #666;
  word-break: break-all;
  padding: 0.75rem;
  background: #f1f3f4;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.imagePath strong {
  color: #333;
}

.noImageContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
  border: 2px dashed #ddd;
  border-radius: 8px;
  background: #f8f9fa;
}

.noImagePlaceholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: #666;
}

.noImagePlaceholder svg {
  opacity: 0.5;
}

.noImagePlaceholder p {
  margin: 0;
  font-size: 0.9rem;
}

.manualInputOption {
  margin-top: 1rem;
}

.manualInputOption details {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 0.5rem;
}

.manualInputOption summary {
  cursor: pointer;
  font-size: 0.9rem;
  color: #666;
  padding: 0.25rem;
}

.manualInputOption summary:hover {
  color: #333;
}

.manualInputOption input {
  margin-top: 0.5rem;
  width: 100%;
}

/* Image upload options */
.imageUploadOptions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

.uploadOption {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.fileInput {
  display: none;
}

.uploadButton {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  min-width: 140px;
  justify-content: center;
}

.uploadButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.uploadButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.loadingSpinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s ease-in-out infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Pricing Tiers Styles */
.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.sectionHeader h3 {
  margin: 0;
}

.addTierButton {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.addTierButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.pricingTiersContainer {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.pricingTier {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 1.5rem;
  background: #f8f9fa;
  transition: all 0.2s ease;
}

.pricingTier:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.tierHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #dee2e6;
}

.tierHeader h4 {
  margin: 0;
  color: #495057;
  font-size: 1rem;
  font-weight: 600;
}

.tierActions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.defaultLabel {
  display: flex !important;
  flex-direction: row !important;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #495057;
  margin: 0 !important;
  cursor: pointer;
}

.defaultLabel input[type="radio"] {
  margin: 0;
  cursor: pointer;
}

.removeTierButton {
  background: #dc3545;
  color: white;
  border: none;
  padding: 0.4rem 0.8rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.removeTierButton:hover {
  background: #c82333;
  transform: translateY(-1px);
}

.tierFields {
  background: white;
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.tierFields .formRow {
  margin-bottom: 1rem;
}

.tierFields .formGroup:last-child {
  margin-bottom: 0;
}

/* Responsive adjustments for pricing tiers */
@media (max-width: 768px) {
  .sectionHeader {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .tierHeader {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .tierActions {
    justify-content: space-between;
  }

  .pricingTier {
    padding: 1rem;
  }

  .tierFields {
    padding: 0.75rem;
  }
}


/* Styles for dynamic category management */
.categoryInputWrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.categoryInputWrapper .select {
  flex-grow: 1;
}

.addNewCategoryButton {
  padding: 0.75rem 1rem; /* Match select padding */
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  white-space: nowrap; /* Prevent text wrapping */
}

.addNewCategoryButton:hover {
  background-color: #5a6268;
}

.addNewCategoryButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Modal styles for Add New Category */
.modalBackdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000; /* Ensure it's above other content */
}

.modalContent {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  width: 100%;
  max-width: 400px; /* Adjust as needed */
}

.modalContent h3 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: #333;
  font-size: 1.25rem;
}

.errorTextModal {
  color: #dc3545;
  font-size: 0.85rem;
  margin-bottom: 1rem;
}

.modalActions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 1.5rem;
}

.cancelButtonModal,
.saveButtonModal {
  padding: 0.6rem 1.2rem;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelButtonModal {
  background-color: #6c757d;
  color: white;
}
.cancelButtonModal:hover {
  background-color: #5a6268;
}

.saveButtonModal {
  background-color: #007bff;
  color: white;
}
.saveButtonModal:hover {
  background-color: #0056b3;
}

.saveButtonModal:disabled,
.cancelButtonModal:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Delete confirmation modal */
.deleteModal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.deleteModalContent {
  background-color: white;
  padding: 2rem;
  border-radius: 8px;
  max-width: 500px;
  width: 90%;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.deleteModalContent h3 {
  margin: 0 0 1rem 0;
  color: #dc3545;
}

.deleteModalContent p {
  margin: 0 0 1rem 0;
  color: #666;
  line-height: 1.5;
}

.warningText {
  color: #856404 !important;
  font-weight: 500;
}

.impactList {
  margin: 0 0 1.5rem 1rem;
  color: #666;
}

.impactList li {
  margin-bottom: 0.25rem;
}

.deleteModalActions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.confirmDeleteButton {
  background-color: #dc3545;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
}

.confirmDeleteButton:hover {
  background-color: #c82333;
}

/* Styles for Inline "Add New Category" Button and Modal */
.inlineButton {
  margin-top: 0.5rem;
  padding: 0.5rem 0.75rem;
  font-size: 0.8rem;
  background-color: #e9ecef;
  color: #495057;
  border: 1px solid #ced4da;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.inlineButton:hover {
  background-color: #dee2e6;
}

.inlineModalBackdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1050; /* Ensure it's above other content but potentially below highest-level modals from AdminLayout */
}

.inlineModal {
  background: white;
  padding: 1.5rem 2rem;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.3);
  min-width: 400px;
  max-width: 90%;
}

.inlineModal h3 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.25rem;
  color: #333;
}

.modalForm .formGroup { /* Specificity for modal form groups if needed */
  margin-bottom: 1rem;
}

.modalForm .formActions { /* Specificity for modal form actions */
  margin-top: 1.5rem;
  border-top: none; /* Assuming main formActions has a border-top */
  padding-top: 0;
}

.errorTextModal {
  color: #dc3545;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  padding: 0.75rem 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.categoryActionsContainer {
  display: flex;
  gap: 0.5rem; /* Space between buttons */
  margin-top: 0.5rem; /* Space above the button container */
}

