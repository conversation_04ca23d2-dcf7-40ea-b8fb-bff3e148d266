/* Loyalty Dashboard Styles - Phase 8: Advanced Customer Experience */

.loyaltyDashboard {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

/* Tab Navigation */
.tabNavigation {
  display: flex;
  border-bottom: 2px solid #f0f0f0;
  margin-bottom: 2rem;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.tabNavigation::-webkit-scrollbar {
  display: none;
}

.tab {
  padding: 1rem 2rem;
  border: none;
  background: transparent;
  color: #666;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  border-bottom: 3px solid transparent;
}

.tab:hover {
  color: #4ECDC4;
  background: rgba(78, 205, 196, 0.05);
}

.tab.active {
  color: #4ECDC4;
  border-bottom-color: #4ECDC4;
  background: rgba(78, 205, 196, 0.1);
}

/* Tab Content */
.tabContent {
  min-height: 400px;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  gap: 1rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4ECDC4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Overview Section */
.overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.noLoyalty {
  text-align: center;
  padding: 3rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.noLoyalty h3 {
  color: #333;
  margin-bottom: 1rem;
}

.noLoyalty p {
  color: #666;
  margin-bottom: 2rem;
}

.joinButton {
  background: #4ECDC4;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.joinButton:hover {
  background: #44A08D;
  transform: translateY(-1px);
}

/* Points Card */
.pointsCard {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
}

.pointsHeader h3 {
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
  opacity: 0.9;
}

.pointsBalance {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.pointsSubtext {
  opacity: 0.8;
  font-size: 0.9rem;
}

/* Tier Card */
.tierCard {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.tierHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.tierHeader h3 {
  margin: 0;
  color: #333;
}

.tierBadge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tierProgress {
  margin-top: 1rem;
}

.progressBar {
  width: 100%;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, #4ECDC4 0%, #44A08D 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progressText {
  font-size: 0.9rem;
  color: #666;
  text-align: center;
}

/* Stats Grid */
.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  grid-column: 1 / -1;
}

.statCard {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.statValue {
  font-size: 2rem;
  font-weight: 700;
  color: #4ECDC4;
  margin-bottom: 0.5rem;
}

.statLabel {
  font-size: 0.9rem;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Rewards Section */
.rewards {
  padding: 1rem 0;
}

.rewards h3 {
  margin-bottom: 2rem;
  color: #333;
}

.noRewards {
  text-align: center;
  padding: 3rem;
  color: #666;
}

.rewardsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.rewardCard {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.rewardCard.available {
  border-color: #4ECDC4;
  box-shadow: 0 4px 16px rgba(78, 205, 196, 0.2);
}

.rewardCard.unavailable {
  opacity: 0.6;
  background: #f8f9fa;
}

.rewardHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.rewardName {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin: 0;
  flex: 1;
}

.rewardPoints {
  background: #4ECDC4;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  white-space: nowrap;
}

.rewardDescription {
  color: #666;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.rewardMeta {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.rewardType {
  background: #e9ecef;
  color: #495057;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 0.7rem;
  font-weight: 600;
}

.tierRequirement {
  background: #ffc107;
  color: #212529;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 0.7rem;
  font-weight: 600;
}

.redeemButton {
  width: 100%;
  padding: 0.75rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.redeemButton:enabled {
  background: #4ECDC4;
  color: white;
}

.redeemButton:enabled:hover {
  background: #44A08D;
  transform: translateY(-1px);
}

.redeemButton:disabled {
  background: #e9ecef;
  color: #6c757d;
  cursor: not-allowed;
}

/* Transactions Section */
.transactions {
  padding: 1rem 0;
}

.transactions h3 {
  margin-bottom: 2rem;
  color: #333;
}

.noTransactions {
  text-align: center;
  padding: 3rem;
  color: #666;
}

.transactionsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.transactionItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.transactionInfo {
  flex: 1;
}

.transactionDescription {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
}

.transactionDate {
  font-size: 0.9rem;
  color: #666;
}

.transactionAmount {
  text-align: right;
}

.pointsChange {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.pointsChange.positive {
  color: #28a745;
}

.pointsChange.negative {
  color: #dc3545;
}

.transactionType {
  font-size: 0.8rem;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .loyaltyDashboard {
    padding: 0.5rem;
  }

  .tab {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .overview {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .pointsBalance {
    font-size: 2.5rem;
  }

  .rewardsGrid {
    grid-template-columns: 1fr;
  }

  .transactionItem {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .transactionAmount {
    text-align: left;
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .tierHeader {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .rewardHeader {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }

  .rewardPoints {
    align-self: flex-start;
  }

  .statsGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}
