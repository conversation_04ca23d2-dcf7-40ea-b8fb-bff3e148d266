-- Phase 7: Integration Columns Migration
-- Ocean Soul Sparkles - Add integration-specific columns to existing tables
-- Created: January 2025

BEGIN;

-- =====================================================
-- BOOKINGS TABLE INTEGRATION COLUMNS
-- Add columns for QuickBooks and calendar sync data
-- =====================================================

-- Add QuickBooks integration columns to bookings table
ALTER TABLE public.bookings 
ADD COLUMN IF NOT EXISTS quickbooks_customer_id TEXT,
ADD COLUMN IF NOT EXISTS quickbooks_invoice_id TEXT,
ADD COLUMN IF NOT EXISTS quickbooks_item_id TEXT,
ADD COLUMN IF NOT EXISTS accounting_sync_data JSONB DEFAULT NULL,
ADD COLUMN IF NOT EXISTS accounting_sync_status TEXT DEFAULT NULL CHECK (accounting_sync_status IN ('pending', 'synced', 'failed', 'manual')),
ADD COLUMN IF NOT EXISTS last_accounting_sync TIMESTAMP WITH TIME ZONE DEFAULT NULL;

-- Add calendar integration columns to bookings table
ALTER TABLE public.bookings 
ADD COLUMN IF NOT EXISTS calendar_event_id TEXT,
ADD COLUMN IF NOT EXISTS calendar_provider TEXT,
ADD COLUMN IF NOT EXISTS calendar_sync_data JSONB DEFAULT NULL,
ADD COLUMN IF NOT EXISTS calendar_sync_status TEXT DEFAULT NULL CHECK (calendar_sync_status IN ('pending', 'synced', 'failed', 'manual')),
ADD COLUMN IF NOT EXISTS last_calendar_sync TIMESTAMP WITH TIME ZONE DEFAULT NULL;

-- =====================================================
-- CUSTOMERS TABLE INTEGRATION COLUMNS  
-- Add columns for QuickBooks and Mailchimp sync data
-- =====================================================

-- Add QuickBooks integration columns to customers table
ALTER TABLE public.customers 
ADD COLUMN IF NOT EXISTS quickbooks_customer_id TEXT,
ADD COLUMN IF NOT EXISTS accounting_sync_data JSONB DEFAULT NULL,
ADD COLUMN IF NOT EXISTS accounting_sync_status TEXT DEFAULT NULL CHECK (accounting_sync_status IN ('pending', 'synced', 'failed', 'manual')),
ADD COLUMN IF NOT EXISTS last_accounting_sync TIMESTAMP WITH TIME ZONE DEFAULT NULL;

-- Add Mailchimp integration columns to customers table  
ALTER TABLE public.customers 
ADD COLUMN IF NOT EXISTS mailchimp_subscriber_id TEXT,
ADD COLUMN IF NOT EXISTS mailchimp_list_id TEXT,
ADD COLUMN IF NOT EXISTS marketing_sync_data JSONB DEFAULT NULL,
ADD COLUMN IF NOT EXISTS marketing_sync_status TEXT DEFAULT NULL CHECK (marketing_sync_status IN ('pending', 'synced', 'failed', 'manual')),
ADD COLUMN IF NOT EXISTS last_marketing_sync TIMESTAMP WITH TIME ZONE DEFAULT NULL;

-- Add customer segmentation data for marketing
ALTER TABLE public.customers 
ADD COLUMN IF NOT EXISTS customer_segment TEXT DEFAULT 'regular',
ADD COLUMN IF NOT EXISTS customer_tags TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS email_subscription_status TEXT DEFAULT 'subscribed' CHECK (email_subscription_status IN ('subscribed', 'unsubscribed', 'pending', 'cleaned'));

-- =====================================================
-- PORTFOLIO_ITEMS TABLE INTEGRATION COLUMNS
-- Add columns for social media sync data
-- =====================================================

-- Check if portfolio_items table exists, create if not
CREATE TABLE IF NOT EXISTS public.portfolio_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    image_url TEXT NOT NULL,
    category TEXT,
    tags TEXT[] DEFAULT '{}',
    is_public BOOLEAN DEFAULT true,
    featured BOOLEAN DEFAULT false,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add social media integration columns to portfolio_items table
ALTER TABLE public.portfolio_items 
ADD COLUMN IF NOT EXISTS social_sync_data JSONB DEFAULT NULL,
ADD COLUMN IF NOT EXISTS social_sync_status TEXT DEFAULT NULL CHECK (social_sync_status IN ('pending', 'synced', 'failed', 'manual')),
ADD COLUMN IF NOT EXISTS last_social_sync TIMESTAMP WITH TIME ZONE DEFAULT NULL,
ADD COLUMN IF NOT EXISTS instagram_post_id TEXT,
ADD COLUMN IF NOT EXISTS facebook_post_id TEXT,
ADD COLUMN IF NOT EXISTS social_media_urls JSONB DEFAULT '{}';

-- =====================================================
-- EVENTS TABLE INTEGRATION COLUMNS
-- Add columns for calendar and social media sync
-- =====================================================

-- Add integration columns to events table (if it exists)
DO $$ 
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'events' AND table_schema = 'public') THEN
        -- Add calendar integration columns
        ALTER TABLE public.events 
        ADD COLUMN IF NOT EXISTS calendar_event_id TEXT,
        ADD COLUMN IF NOT EXISTS calendar_provider TEXT,
        ADD COLUMN IF NOT EXISTS calendar_sync_data JSONB DEFAULT NULL,
        ADD COLUMN IF NOT EXISTS calendar_sync_status TEXT DEFAULT NULL CHECK (calendar_sync_status IN ('pending', 'synced', 'failed', 'manual')),
        ADD COLUMN IF NOT EXISTS last_calendar_sync TIMESTAMP WITH TIME ZONE DEFAULT NULL;
        
        -- Add social media integration columns
        ALTER TABLE public.events 
        ADD COLUMN IF NOT EXISTS facebook_event_id TEXT,
        ADD COLUMN IF NOT EXISTS social_sync_data JSONB DEFAULT NULL,
        ADD COLUMN IF NOT EXISTS social_sync_status TEXT DEFAULT NULL CHECK (social_sync_status IN ('pending', 'synced', 'failed', 'manual')),
        ADD COLUMN IF NOT EXISTS last_social_sync TIMESTAMP WITH TIME ZONE DEFAULT NULL;
    END IF;
END $$;

-- =====================================================
-- SERVICES TABLE INTEGRATION COLUMNS
-- Add columns for QuickBooks item sync
-- =====================================================

-- Add QuickBooks integration columns to services table
ALTER TABLE public.services 
ADD COLUMN IF NOT EXISTS quickbooks_item_id TEXT,
ADD COLUMN IF NOT EXISTS accounting_sync_data JSONB DEFAULT NULL,
ADD COLUMN IF NOT EXISTS accounting_sync_status TEXT DEFAULT NULL CHECK (accounting_sync_status IN ('pending', 'synced', 'failed', 'manual')),
ADD COLUMN IF NOT EXISTS last_accounting_sync TIMESTAMP WITH TIME ZONE DEFAULT NULL;

-- =====================================================
-- ARTIST_PROFILES TABLE INTEGRATION COLUMNS
-- Add columns for calendar and social media sync
-- =====================================================

-- Add integration columns to artist_profiles table (if it exists)
DO $$ 
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'artist_profiles' AND table_schema = 'public') THEN
        -- Add calendar integration columns
        ALTER TABLE public.artist_profiles 
        ADD COLUMN IF NOT EXISTS calendar_integration_enabled BOOLEAN DEFAULT false,
        ADD COLUMN IF NOT EXISTS calendar_sync_data JSONB DEFAULT NULL,
        ADD COLUMN IF NOT EXISTS last_calendar_sync TIMESTAMP WITH TIME ZONE DEFAULT NULL;
        
        -- Add social media integration columns
        ALTER TABLE public.artist_profiles 
        ADD COLUMN IF NOT EXISTS social_media_enabled BOOLEAN DEFAULT false,
        ADD COLUMN IF NOT EXISTS social_sync_data JSONB DEFAULT NULL,
        ADD COLUMN IF NOT EXISTS last_social_sync TIMESTAMP WITH TIME ZONE DEFAULT NULL;
    END IF;
END $$;

-- =====================================================
-- CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Indexes for integration sync columns
CREATE INDEX IF NOT EXISTS idx_bookings_quickbooks_customer_id ON public.bookings(quickbooks_customer_id);
CREATE INDEX IF NOT EXISTS idx_bookings_quickbooks_invoice_id ON public.bookings(quickbooks_invoice_id);
CREATE INDEX IF NOT EXISTS idx_bookings_calendar_event_id ON public.bookings(calendar_event_id);
CREATE INDEX IF NOT EXISTS idx_bookings_accounting_sync_status ON public.bookings(accounting_sync_status);
CREATE INDEX IF NOT EXISTS idx_bookings_calendar_sync_status ON public.bookings(calendar_sync_status);

CREATE INDEX IF NOT EXISTS idx_customers_quickbooks_customer_id ON public.customers(quickbooks_customer_id);
CREATE INDEX IF NOT EXISTS idx_customers_mailchimp_subscriber_id ON public.customers(mailchimp_subscriber_id);
CREATE INDEX IF NOT EXISTS idx_customers_accounting_sync_status ON public.customers(accounting_sync_status);
CREATE INDEX IF NOT EXISTS idx_customers_marketing_sync_status ON public.customers(marketing_sync_status);
CREATE INDEX IF NOT EXISTS idx_customers_customer_segment ON public.customers(customer_segment);

CREATE INDEX IF NOT EXISTS idx_portfolio_items_social_sync_status ON public.portfolio_items(social_sync_status);
CREATE INDEX IF NOT EXISTS idx_portfolio_items_instagram_post_id ON public.portfolio_items(instagram_post_id);
CREATE INDEX IF NOT EXISTS idx_portfolio_items_facebook_post_id ON public.portfolio_items(facebook_post_id);

CREATE INDEX IF NOT EXISTS idx_services_quickbooks_item_id ON public.services(quickbooks_item_id);
CREATE INDEX IF NOT EXISTS idx_services_accounting_sync_status ON public.services(accounting_sync_status);

-- =====================================================
-- UPDATE EXISTING RLS POLICIES
-- Ensure integration columns are covered by existing policies
-- =====================================================

-- The existing RLS policies should already cover these new columns
-- since they're added to existing tables with existing policies

-- =====================================================
-- ADD COMMENTS FOR DOCUMENTATION
-- =====================================================

-- Bookings table integration comments
COMMENT ON COLUMN public.bookings.quickbooks_customer_id IS 'QuickBooks customer ID for invoice generation';
COMMENT ON COLUMN public.bookings.quickbooks_invoice_id IS 'QuickBooks invoice ID for this booking';
COMMENT ON COLUMN public.bookings.accounting_sync_data IS 'JSON data for accounting system synchronization';
COMMENT ON COLUMN public.bookings.calendar_event_id IS 'External calendar event ID for calendar sync';
COMMENT ON COLUMN public.bookings.calendar_sync_data IS 'JSON data for calendar system synchronization';

-- Customers table integration comments
COMMENT ON COLUMN public.customers.quickbooks_customer_id IS 'QuickBooks customer ID for invoice generation';
COMMENT ON COLUMN public.customers.mailchimp_subscriber_id IS 'Mailchimp subscriber ID for email marketing';
COMMENT ON COLUMN public.customers.marketing_sync_data IS 'JSON data for marketing platform synchronization';
COMMENT ON COLUMN public.customers.customer_segment IS 'Customer segmentation for marketing campaigns';
COMMENT ON COLUMN public.customers.customer_tags IS 'Array of tags for customer categorization';

-- Portfolio items integration comments
COMMENT ON COLUMN public.portfolio_items.social_sync_data IS 'JSON data for social media platform synchronization';
COMMENT ON COLUMN public.portfolio_items.instagram_post_id IS 'Instagram post ID for portfolio sync';
COMMENT ON COLUMN public.portfolio_items.facebook_post_id IS 'Facebook post ID for portfolio sync';
COMMENT ON COLUMN public.portfolio_items.social_media_urls IS 'JSON object containing social media post URLs';

-- Services table integration comments
COMMENT ON COLUMN public.services.quickbooks_item_id IS 'QuickBooks service item ID for invoice line items';
COMMENT ON COLUMN public.services.accounting_sync_data IS 'JSON data for accounting system synchronization';

COMMIT;
