import { useState, useEffect, useMemo } from 'react';
import { <PERSON>, Bar, Scatter } from 'react-chartjs-2';
import { authenticatedFetch } from '@/lib/auth-utils';
import { 
  DemandForecaster, 
  RevenuePredictionModel, 
  CustomerBehaviorAnalyzer 
} from '@/lib/analytics/predictive-models';
import styles from '@/styles/admin/analytics/PredictiveAnalytics.module.css';

/**
 * Predictive Analytics Component
 * Provides ML-powered forecasting and predictive insights
 */
export default function PredictiveAnalytics({ timeframe = 'monthly' }) {
  const [forecastData, setForecastData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedModel, setSelectedModel] = useState('demand');
  const [forecastPeriods, setForecastPeriods] = useState(7);
  const [confidenceLevel, setConfidenceLevel] = useState(0.95);

  const demandForecaster = useMemo(() => new DemandForecaster(), []);
  const revenuePredictor = useMemo(() => new RevenuePredictionModel(), []);
  const behaviorAnalyzer = useMemo(() => new CustomerBehaviorAnalyzer(), []);

  useEffect(() => {
    loadPredictiveData();
  }, [timeframe, selectedModel, forecastPeriods]);

  const loadPredictiveData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await authenticatedFetch(`/api/analytics/predictive-forecasting?timeframe=${timeframe}&model=${selectedModel}&periods=${forecastPeriods}`);
      
      if (response.success) {
        setForecastData(response.data);
      } else {
        throw new Error(response.error || 'Failed to load predictive data');
      }
    } catch (err) {
      console.error('Error loading predictive data:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const generateDemandForecastChart = () => {
    if (!forecastData?.demandForecast) return null;

    const historical = forecastData.historical.demand || [];
    const forecast = forecastData.demandForecast.forecast || [];
    const confidence = forecastData.demandForecast.confidence || [];

    // Create labels for historical + forecast periods
    const historicalLabels = historical.map((_, i) => `Period ${i + 1}`);
    const forecastLabels = forecast.map((_, i) => `Forecast ${i + 1}`);
    const allLabels = [...historicalLabels, ...forecastLabels];

    return {
      labels: allLabels,
      datasets: [
        {
          label: 'Historical Demand',
          data: [...historical, ...new Array(forecast.length).fill(null)],
          borderColor: 'rgb(59, 130, 246)',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          fill: false,
          tension: 0.4
        },
        {
          label: 'Forecasted Demand',
          data: [...new Array(historical.length).fill(null), ...forecast],
          borderColor: 'rgb(16, 185, 129)',
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          borderDash: [5, 5],
          fill: false,
          tension: 0.4
        },
        {
          label: 'Confidence Interval (Upper)',
          data: [...new Array(historical.length).fill(null), ...confidence.map(c => c.upper)],
          borderColor: 'rgba(16, 185, 129, 0.3)',
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          fill: '+1',
          pointRadius: 0,
          borderWidth: 1
        },
        {
          label: 'Confidence Interval (Lower)',
          data: [...new Array(historical.length).fill(null), ...confidence.map(c => c.lower)],
          borderColor: 'rgba(16, 185, 129, 0.3)',
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          fill: false,
          pointRadius: 0,
          borderWidth: 1
        }
      ]
    };
  };

  const generateRevenueForecastChart = () => {
    if (!forecastData?.revenueForecast) return null;

    const historical = forecastData.historical.revenue || [];
    const forecast = forecastData.revenueForecast.forecast || [];

    const historicalLabels = historical.map((_, i) => `Period ${i + 1}`);
    const forecastLabels = forecast.map((_, i) => `Forecast ${i + 1}`);
    const allLabels = [...historicalLabels, ...forecastLabels];

    return {
      labels: allLabels,
      datasets: [
        {
          label: 'Historical Revenue',
          data: [...historical, ...new Array(forecast.length).fill(null)],
          borderColor: 'rgb(139, 92, 246)',
          backgroundColor: 'rgba(139, 92, 246, 0.1)',
          fill: false,
          tension: 0.4
        },
        {
          label: 'Forecasted Revenue',
          data: [...new Array(historical.length).fill(null), ...forecast],
          borderColor: 'rgb(245, 158, 11)',
          backgroundColor: 'rgba(245, 158, 11, 0.1)',
          borderDash: [5, 5],
          fill: false,
          tension: 0.4
        }
      ]
    };
  };

  const generateSeasonalityChart = () => {
    if (!forecastData?.seasonalAnalysis) return null;

    const seasonalPattern = forecastData.seasonalAnalysis.seasonalPattern || [];
    const labels = seasonalPattern.map((_, i) => `Day ${i + 1}`);

    return {
      labels,
      datasets: [
        {
          label: 'Seasonal Pattern',
          data: seasonalPattern,
          backgroundColor: 'rgba(236, 72, 153, 0.8)',
          borderColor: 'rgb(236, 72, 153)',
          borderWidth: 1
        }
      ]
    };
  };

  const generateCustomerBehaviorChart = () => {
    if (!forecastData?.customerBehavior) return null;

    const behaviorData = forecastData.customerBehavior || [];
    
    return {
      datasets: [
        {
          label: 'Customer Segments',
          data: behaviorData.map(customer => ({
            x: customer.frequency,
            y: customer.averageValue,
            r: customer.loyalty * 10 // Scale for visibility
          })),
          backgroundColor: 'rgba(59, 130, 246, 0.6)',
          borderColor: 'rgb(59, 130, 246)',
          borderWidth: 1
        }
      ]
    };
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Predictive Analytics'
      },
      tooltip: {
        mode: 'index',
        intersect: false,
      }
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: 'Time Period'
        }
      },
      y: {
        display: true,
        title: {
          display: true,
          text: 'Value'
        }
      }
    },
    interaction: {
      mode: 'nearest',
      axis: 'x',
      intersect: false
    }
  };

  const scatterOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Customer Behavior Analysis'
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            return `Frequency: ${context.parsed.x}, Value: $${context.parsed.y}, Loyalty: ${(context.raw.r / 10).toFixed(2)}`;
          }
        }
      }
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: 'Booking Frequency (days between bookings)'
        }
      },
      y: {
        display: true,
        title: {
          display: true,
          text: 'Average Booking Value ($)'
        }
      }
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(amount);
  };

  const formatPercentage = (value) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.spinner}></div>
        <p>Generating predictive analytics...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.errorContainer}>
        <h3>Error Loading Predictive Analytics</h3>
        <p>{error}</p>
        <button onClick={loadPredictiveData} className={styles.retryButton}>
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className={styles.predictiveAnalytics}>
      <div className={styles.header}>
        <h2>Predictive Analytics & Forecasting</h2>
        <div className={styles.controls}>
          <select
            value={selectedModel}
            onChange={(e) => setSelectedModel(e.target.value)}
            className={styles.modelSelect}
          >
            <option value="demand">Demand Forecasting</option>
            <option value="revenue">Revenue Prediction</option>
            <option value="customer">Customer Behavior</option>
            <option value="seasonal">Seasonal Analysis</option>
          </select>
          
          <select
            value={forecastPeriods}
            onChange={(e) => setForecastPeriods(parseInt(e.target.value))}
            className={styles.periodSelect}
          >
            <option value={7}>7 Periods</option>
            <option value={14}>14 Periods</option>
            <option value={30}>30 Periods</option>
            <option value={90}>90 Periods</option>
          </select>

          <select
            value={confidenceLevel}
            onChange={(e) => setConfidenceLevel(parseFloat(e.target.value))}
            className={styles.confidenceSelect}
          >
            <option value={0.90}>90% Confidence</option>
            <option value={0.95}>95% Confidence</option>
            <option value={0.99}>99% Confidence</option>
          </select>
        </div>
      </div>

      {/* Forecast Summary Cards */}
      <div className={styles.summaryGrid}>
        <div className={styles.summaryCard}>
          <h3>Demand Forecast</h3>
          <div className={styles.summaryValue}>
            {forecastData?.demandForecast?.forecast?.[0]?.toFixed(0) || 'N/A'}
          </div>
          <div className={styles.summarySubtext}>
            Next period prediction
          </div>
          <div className={styles.confidence}>
            Confidence: {formatPercentage(forecastData?.demandForecast?.confidence || 0)}
          </div>
        </div>

        <div className={styles.summaryCard}>
          <h3>Revenue Forecast</h3>
          <div className={styles.summaryValue}>
            {forecastData?.revenueForecast?.forecast?.[0] ? 
              formatCurrency(forecastData.revenueForecast.forecast[0]) : 'N/A'}
          </div>
          <div className={styles.summarySubtext}>
            Next period prediction
          </div>
          <div className={styles.confidence}>
            Confidence: {formatPercentage(forecastData?.revenueForecast?.confidence || 0)}
          </div>
        </div>

        <div className={styles.summaryCard}>
          <h3>Seasonal Strength</h3>
          <div className={styles.summaryValue}>
            {formatPercentage(forecastData?.seasonalAnalysis?.seasonalStrength || 0)}
          </div>
          <div className={styles.summarySubtext}>
            Pattern consistency
          </div>
          <div className={styles.confidence}>
            {forecastData?.seasonalAnalysis?.hasSeasonality ? 'Seasonal' : 'Non-seasonal'}
          </div>
        </div>

        <div className={styles.summaryCard}>
          <h3>Model Accuracy</h3>
          <div className={styles.summaryValue}>
            {formatPercentage(forecastData?.modelAccuracy?.overall || 0)}
          </div>
          <div className={styles.summarySubtext}>
            Prediction accuracy
          </div>
          <div className={styles.confidence}>
            Based on historical data
          </div>
        </div>
      </div>

      {/* Charts Grid */}
      <div className={styles.chartsGrid}>
        {selectedModel === 'demand' && (
          <div className={styles.chartCard}>
            <h3>Demand Forecasting</h3>
            <div className={styles.chartContainer}>
              {generateDemandForecastChart() && (
                <Line data={generateDemandForecastChart()} options={chartOptions} />
              )}
            </div>
          </div>
        )}

        {selectedModel === 'revenue' && (
          <div className={styles.chartCard}>
            <h3>Revenue Prediction</h3>
            <div className={styles.chartContainer}>
              {generateRevenueForecastChart() && (
                <Line data={generateRevenueForecastChart()} options={chartOptions} />
              )}
            </div>
          </div>
        )}

        {selectedModel === 'seasonal' && (
          <div className={styles.chartCard}>
            <h3>Seasonal Pattern Analysis</h3>
            <div className={styles.chartContainer}>
              {generateSeasonalityChart() && (
                <Bar data={generateSeasonalityChart()} options={chartOptions} />
              )}
            </div>
          </div>
        )}

        {selectedModel === 'customer' && (
          <div className={styles.chartCard}>
            <h3>Customer Behavior Segments</h3>
            <div className={styles.chartContainer}>
              {generateCustomerBehaviorChart() && (
                <Scatter data={generateCustomerBehaviorChart()} options={scatterOptions} />
              )}
            </div>
          </div>
        )}
      </div>

      {/* Insights and Recommendations */}
      <div className={styles.insightsSection}>
        <h3>Predictive Insights & Recommendations</h3>
        <div className={styles.insightsGrid}>
          {forecastData?.insights?.map((insight, index) => (
            <div key={index} className={`${styles.insightCard} ${styles[insight.type]}`}>
              <h4>{insight.title}</h4>
              <p>{insight.description}</p>
              {insight.recommendation && (
                <div className={styles.recommendation}>
                  <strong>Recommendation:</strong> {insight.recommendation}
                </div>
              )}
              {insight.impact && (
                <div className={styles.impact}>
                  <strong>Expected Impact:</strong> {insight.impact}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Model Performance Metrics */}
      <div className={styles.modelMetrics}>
        <h3>Model Performance</h3>
        <div className={styles.metricsGrid}>
          <div className={styles.metricCard}>
            <h4>Mean Absolute Error</h4>
            <div className={styles.metricValue}>
              {(forecastData?.modelMetrics?.mae || 0).toFixed(2)}
            </div>
          </div>
          
          <div className={styles.metricCard}>
            <h4>Root Mean Square Error</h4>
            <div className={styles.metricValue}>
              {(forecastData?.modelMetrics?.rmse || 0).toFixed(2)}
            </div>
          </div>
          
          <div className={styles.metricCard}>
            <h4>R-Squared</h4>
            <div className={styles.metricValue}>
              {formatPercentage(forecastData?.modelMetrics?.r2 || 0)}
            </div>
          </div>
          
          <div className={styles.metricCard}>
            <h4>Training Data Points</h4>
            <div className={styles.metricValue}>
              {forecastData?.modelMetrics?.dataPoints || 0}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
