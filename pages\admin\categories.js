import React, { useState, useEffect, useCallback } from 'react';
import AdminLayout from '../../components/admin/AdminLayout';
import ProtectedRoute from '../../components/admin/ProtectedRoute';
import styles from '../../styles/admin/CategoriesPage.module.css';

const CategoriesPage = () => {
  const [categories, setCategories] = useState([]);
  const [isLoading, setIsLoading] = useState(false); // For initial list loading
  const [isSubmitting, setIsSubmitting] = useState(false); // For form submissions (add/edit/delete)
  const [error, setError] = useState(null); // For general errors displayed at the top
  const [feedbackMessage, setFeedbackMessage] = useState({ type: '', message: '' }); // For success/error messages after actions

  const [showFormModal, setShowFormModal] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentCategory, setCurrentCategory] = useState(null); // For editing

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    parent_id: '',
  });

  const clearFeedback = () => {
    setFeedbackMessage({ type: '', message: '' });
  };

  const fetchCategories = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    // clearFeedback(); // Clear previous feedback when fetching
    try {
      const response = await fetch('/api/admin/service-categories');
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to fetch categories: ${response.status}`);
      }
      const data = await response.json();
      setCategories(data.categories || []);
    } catch (err) {
      setError(err.message); // Set general error
      console.error("Fetch categories error:", err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const resetForm = () => {
    setFormData({ name: '', description: '', parent_id: '' });
    setCurrentCategory(null);
    setIsEditing(false);
  };

  const handleAddNewClick = () => {
    clearFeedback();
    resetForm();
    setShowFormModal(true);
    setIsEditing(false);
  };

  const handleEditClick = (category) => {
    clearFeedback();
    resetForm();
    setCurrentCategory(category);
    setFormData({
      name: category.name || '',
      description: category.description || '',
      parent_id: category.parent_id || '',
    });
    setShowFormModal(true);
    setIsEditing(true);
  };

  const handleDeleteClick = async (categoryId) => {
    clearFeedback();
    if (window.confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
      setIsSubmitting(true);
      setFeedbackMessage({ type: 'info', message: 'Deleting category...' });
      try {
        const response = await fetch(`/api/admin/service-categories?id=${categoryId}`, { method: 'DELETE' });
        const responseData = await response.json();

        if (!response.ok) {
          throw new Error(responseData.error || `Failed to delete category: ${response.status}`);
        }
        setFeedbackMessage({ type: 'success', message: 'Category deleted successfully!' });
        fetchCategories(); // Refresh list
      } catch (err) {
        setFeedbackMessage({ type: 'error', message: err.message });
        console.error("Delete category error:", err);
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    clearFeedback();
    setIsSubmitting(true);
    setFeedbackMessage({ type: 'info', message: isEditing ? 'Updating category...' : 'Adding category...' });

    const method = isEditing ? 'PUT' : 'POST';
    const url = isEditing
      ? `/api/admin/service-categories?id=${currentCategory.id}`
      : '/api/admin/service-categories';

    // Ensure parent_id is null if empty string, otherwise API might treat it as a value
    const payload = {
      ...formData,
      parent_id: formData.parent_id === '' ? null : formData.parent_id,
    };

    try {
      const response = await fetch(url, {
        method: method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
      const responseData = await response.json();

      if (!response.ok) {
        throw new Error(responseData.error || `Failed to ${isEditing ? 'update' : 'save'} category: ${response.status}`);
      }

      setFeedbackMessage({ type: 'success', message: `Category ${isEditing ? 'updated' : 'added'} successfully!` });
      fetchCategories(); // Refresh list
      setShowFormModal(false);
      resetForm();
    } catch (err) {
      setFeedbackMessage({ type: 'error', message: err.message });
      console.error("Submit category error:", err);
      // Keep form open on error so user can see and correct
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancelForm = () => {
    setShowFormModal(false);
    resetForm();
    clearFeedback();
  };

  const getParentCategoryName = (parentId) => {
    if (!parentId) return 'N/A';
    const parent = categories.find(cat => cat.id === parentId);
    return parent ? parent.name : 'Unknown (ID: ' + parentId + ')';
  };

  return (
    <ProtectedRoute>
      <AdminLayout>
        <div className={styles.container}>
          <div className={styles.header}>
            <h1 className={styles.title}>Service Categories</h1>
            <button onClick={handleAddNewClick} className={styles.addButton} disabled={isSubmitting}>
              Add New Category
            </button>
          </div>

          {isLoading && <p className={styles.loading}>Loading categories...</p>}
          {error && <p className={`${styles.error} ${styles.pageError}`}>Error: {error}</p>}

          {feedbackMessage.message && (
            <div
              className={`${styles.feedbackMessage} ${feedbackMessage.type === 'error' ? styles.errorFeedback : feedbackMessage.type === 'success' ? styles.successFeedback : styles.infoFeedback}`}
              role="alert"
            >
              {feedbackMessage.message}
            </div>
          )}


          {showFormModal && (
            <div className={styles.formModal}>
              <h3>{isEditing ? 'Edit Category' : 'Add New Category'}</h3>
              <form onSubmit={handleSubmit}>
                <div className={styles.formGroup}>
                  <label htmlFor="name">Name</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    disabled={isSubmitting}
                  />
                </div>
                <div className={styles.formGroup}>
                  <label htmlFor="description">Description</label>
                  <textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    disabled={isSubmitting}
                  />
                </div>
                <div className={styles.formGroup}>
                  <label htmlFor="parent_id">Parent Category</label>
                  <select
                    id="parent_id"
                    name="parent_id"
                    value={formData.parent_id}
                    onChange={handleInputChange}
                    disabled={isSubmitting}
                  >
                    <option value="">None (Top Level)</option>
                    {categories
                      .filter(cat => !currentCategory || cat.id !== currentCategory.id) // Prevent self-parenting
                      .map((cat) => (
                        <option key={cat.id} value={cat.id}>
                          {cat.name}
                        </option>
                      ))}
                  </select>
                </div>
                <div className={styles.formActions}>
                  <button type="button" onClick={handleCancelForm} className={styles.cancelButton} disabled={isSubmitting}>
                    Cancel
                  </button>
                  <button type="submit" className={styles.saveButton} disabled={isSubmitting}>
                    {isSubmitting ? (isEditing ? 'Saving...' : 'Adding...') : (isEditing ? 'Save Changes' : 'Add Category')}
                  </button>
                </div>
              </form>
            </div>
          )}

          {!isLoading && !error && !categories.length && !showFormModal && (
            <p>No categories found. Add one to get started!</p>
          )}

          <ul className={styles.categoryList}>
            {categories.map((category) => (
              <li key={category.id} className={styles.categoryItem}>
                <div className={styles.categoryDetails}>
                  <p className={styles.categoryName}>{category.name}</p>
                  {category.description && <p className={styles.categoryDescription}>{category.description}</p>}
                  <p className={styles.categoryParent}>
                    Parent: {getParentCategoryName(category.parent_id)}
                  </p>
                   <small>ID: {category.id}</small><br/>
                   <small>Created: {category.created_at ? new Date(category.created_at).toLocaleString() : 'N/A'}</small><br/>
                   <small>Updated: {category.updated_at ? new Date(category.updated_at).toLocaleString() : 'N/A'}</small>
                </div>
                <div className={styles.categoryActions}>
                  <button onClick={() => handleEditClick(category)} className={styles.editButton} disabled={isSubmitting || showFormModal}>
                    Edit
                  </button>
                  <button onClick={() => handleDeleteClick(category.id)} className={styles.deleteButton} disabled={isSubmitting || showFormModal}>
                    Delete
                  </button>
                </div>
              </li>
            ))}
          </ul>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
};

export default CategoriesPage;
