/* Camera Capture Component Styles */

.container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #000;
  z-index: 10000;
  display: flex;
  flex-direction: column;
}

.error {
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  background: rgba(239, 68, 68, 0.9);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  z-index: 10001;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error p {
  margin: 0;
  font-size: 14px;
}

.error button {
  background: none;
  border: none;
  color: white;
  font-size: 14px;
  cursor: pointer;
  text-decoration: underline;
}

/* Fallback for unsupported devices */
.fallback {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 40px;
  text-align: center;
  color: white;
}

.fallback h3 {
  font-size: 24px;
  margin-bottom: 16px;
  color: white;
}

.fallback p {
  font-size: 16px;
  margin-bottom: 32px;
  color: #d1d5db;
  line-height: 1.5;
}

.uploadButton {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  margin-bottom: 16px;
  transition: all 0.2s ease;
}

.uploadButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(79, 70, 229, 0.3);
}

.cancelButton {
  background: transparent;
  color: #9ca3af;
  border: 1px solid #4b5563;
  border-radius: 12px;
  padding: 12px 24px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelButton:hover {
  background: rgba(156, 163, 175, 0.1);
  color: white;
}

/* Camera view */
.camera {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background: #000;
}

/* Camera controls */
.controls {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.topControls {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px;
  pointer-events: auto;
}

.bottomControls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40px 20px;
  pointer-events: auto;
}

.controlButton {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.controlButton:hover {
  background: rgba(0, 0, 0, 0.7);
  border-color: rgba(255, 255, 255, 0.5);
}

.controlButton.active {
  background: rgba(251, 191, 36, 0.8);
  border-color: #fbbf24;
}

.closeButton {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.closeButton:hover {
  background: rgba(239, 68, 68, 0.8);
  border-color: #ef4444;
}

.switchButton {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-size: 24px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.switchButton:hover {
  background: rgba(0, 0, 0, 0.7);
  border-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.05);
}

.captureButton {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  border: 4px solid rgba(255, 255, 255, 0.5);
  color: #1f2937;
  font-size: 32px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.captureButton:hover:not(:disabled) {
  transform: scale(1.05);
  background: white;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
}

.captureButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.spacer {
  width: 56px;
  height: 56px;
}

.typeIndicator {
  position: absolute;
  top: 50%;
  left: 20px;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Photo preview */
.preview {
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.capturedImage {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: #000;
}

.previewActions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 40px 20px 20px;
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.retakeButton {
  flex: 1;
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.5);
  border-radius: 12px;
  padding: 16px 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retakeButton:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: white;
}

.saveButton {
  flex: 2;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 16px 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.saveButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(16, 185, 129, 0.3);
}

.saveButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .topControls {
    padding: 16px;
  }
  
  .bottomControls {
    padding: 32px 16px;
  }
  
  .captureButton {
    width: 72px;
    height: 72px;
    font-size: 28px;
  }
  
  .switchButton {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }
  
  .controlButton {
    width: 44px;
    height: 44px;
    font-size: 18px;
  }
  
  .closeButton {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }
  
  .typeIndicator {
    left: 16px;
    font-size: 12px;
    padding: 6px 12px;
  }
  
  .previewActions {
    padding: 32px 16px 16px;
    gap: 12px;
  }
  
  .retakeButton,
  .saveButton {
    padding: 14px 20px;
    font-size: 15px;
  }
  
  .fallback {
    padding: 20px;
  }
  
  .fallback h3 {
    font-size: 20px;
  }
  
  .fallback p {
    font-size: 14px;
  }
  
  .uploadButton {
    padding: 14px 28px;
    font-size: 15px;
  }
  
  .cancelButton {
    padding: 10px 20px;
    font-size: 13px;
  }
}

/* Landscape orientation adjustments */
@media (orientation: landscape) and (max-height: 500px) {
  .topControls {
    padding: 12px 16px;
  }
  
  .bottomControls {
    padding: 20px 16px;
  }
  
  .captureButton {
    width: 64px;
    height: 64px;
    font-size: 24px;
  }
  
  .typeIndicator {
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .captureButton {
    border-width: 3px;
  }
  
  .controlButton {
    border-width: 1.5px;
  }
}
