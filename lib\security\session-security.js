/**
 * Enhanced Session Security Manager for Ocean Soul Sparkles
 * Provides secure session management with device fingerprinting and suspicious activity detection
 * 
 * Phase 9.1: Enhanced Authentication System
 */

import crypto from 'crypto'
import { createClient } from '@supabase/supabase-js'
import { DeviceFingerprintManager } from './device-fingerprinting'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

/**
 * Session Security Manager Class
 * Handles enhanced session management with security features
 */
export class SessionSecurityManager {
  constructor() {
    this.sessionTimeout = 24 * 60 * 60 * 1000 // 24 hours
    this.inactivityTimeout = 2 * 60 * 60 * 1000 // 2 hours
    this.maxConcurrentSessions = 5
    this.deviceFingerprintManager = new DeviceFingerprintManager()
  }

  /**
   * Create a new secure session
   * @param {string} userId - User ID
   * @param {Object} request - Request object with headers
   * @returns {Promise<Object>} - Session tokens
   */
  async createSession(userId, request) {
    try {
      const sessionToken = this.generateSecureToken()
      const refreshToken = this.generateSecureToken()
      const deviceFingerprint = await this.deviceFingerprintManager.generateFingerprint(request)
      
      const sessionData = {
        user_id: userId,
        session_token: sessionToken,
        refresh_token: refreshToken,
        device_fingerprint: deviceFingerprint.hash,
        ip_address: this.getClientIP(request),
        user_agent: request.headers['user-agent'] || 'Unknown',
        location_country: await this.getLocationFromIP(this.getClientIP(request)),
        expires_at: new Date(Date.now() + this.sessionTimeout).toISOString(),
        last_activity: new Date().toISOString()
      }
      
      // Check for concurrent sessions limit
      await this.enforceSessionLimit(userId)
      
      // Create session
      const { data: session, error } = await supabase
        .from('user_sessions')
        .insert(sessionData)
        .select()
        .single()
      
      if (error) {
        throw new Error(`Failed to create session: ${error.message}`)
      }
      
      // Update device fingerprint
      await this.deviceFingerprintManager.updateDeviceInfo(userId, deviceFingerprint)
      
      // Log session creation
      await this.logSecurityEvent(userId, 'session_created', 'New session created', 'low', {
        sessionId: session.id,
        deviceFingerprint: deviceFingerprint.hash,
        ipAddress: sessionData.ip_address
      })
      
      return {
        sessionToken,
        refreshToken,
        expiresAt: sessionData.expires_at,
        sessionId: session.id
      }
    } catch (error) {
      console.error('Error creating session:', error)
      throw error
    }
  }

  /**
   * Validate and refresh session
   * @param {string} sessionToken - Session token
   * @param {Object} request - Request object
   * @returns {Promise<Object>} - Validation result
   */
  async validateSession(sessionToken, request) {
    try {
      // Get session from database
      const { data: session, error } = await supabase
        .from('user_sessions')
        .select('*')
        .eq('session_token', sessionToken)
        .eq('is_active', true)
        .single()
      
      if (error || !session) {
        return { valid: false, reason: 'Session not found' }
      }
      
      // Check if session is expired
      if (new Date(session.expires_at) < new Date()) {
        await this.invalidateSession(session.id)
        return { valid: false, reason: 'Session expired' }
      }
      
      // Check for inactivity timeout
      const lastActivity = new Date(session.last_activity)
      if (Date.now() - lastActivity.getTime() > this.inactivityTimeout) {
        await this.invalidateSession(session.id)
        await this.logSecurityEvent(session.user_id, 'session_timeout', 'Session expired due to inactivity', 'low')
        return { valid: false, reason: 'Session timed out due to inactivity' }
      }
      
      // Check device fingerprint for suspicious activity
      const currentFingerprint = await this.deviceFingerprintManager.generateFingerprint(request)
      const suspiciousActivity = await this.detectSuspiciousActivity(session, request, currentFingerprint)
      
      if (suspiciousActivity.isSuspicious) {
        await this.handleSuspiciousActivity(session, suspiciousActivity)
        return { valid: false, reason: 'Suspicious activity detected' }
      }
      
      // Update last activity
      await supabase
        .from('user_sessions')
        .update({ last_activity: new Date().toISOString() })
        .eq('id', session.id)
      
      return {
        valid: true,
        userId: session.user_id,
        sessionId: session.id,
        session
      }
    } catch (error) {
      console.error('Error validating session:', error)
      return { valid: false, reason: 'Validation error' }
    }
  }

  /**
   * Refresh session token
   * @param {string} refreshToken - Refresh token
   * @param {Object} request - Request object
   * @returns {Promise<Object>} - New session tokens
   */
  async refreshSession(refreshToken, request) {
    try {
      // Get session by refresh token
      const { data: session, error } = await supabase
        .from('user_sessions')
        .select('*')
        .eq('refresh_token', refreshToken)
        .eq('is_active', true)
        .single()
      
      if (error || !session) {
        return { success: false, reason: 'Invalid refresh token' }
      }
      
      // Check if session is expired
      if (new Date(session.expires_at) < new Date()) {
        await this.invalidateSession(session.id)
        return { success: false, reason: 'Refresh token expired' }
      }
      
      // Generate new tokens
      const newSessionToken = this.generateSecureToken()
      const newRefreshToken = this.generateSecureToken()
      const newExpiresAt = new Date(Date.now() + this.sessionTimeout).toISOString()
      
      // Update session
      const { error: updateError } = await supabase
        .from('user_sessions')
        .update({
          session_token: newSessionToken,
          refresh_token: newRefreshToken,
          expires_at: newExpiresAt,
          last_activity: new Date().toISOString()
        })
        .eq('id', session.id)
      
      if (updateError) {
        throw new Error(`Failed to refresh session: ${updateError.message}`)
      }
      
      // Log token refresh
      await this.logSecurityEvent(session.user_id, 'session_refreshed', 'Session tokens refreshed', 'low')
      
      return {
        success: true,
        sessionToken: newSessionToken,
        refreshToken: newRefreshToken,
        expiresAt: newExpiresAt
      }
    } catch (error) {
      console.error('Error refreshing session:', error)
      return { success: false, reason: 'Refresh error' }
    }
  }

  /**
   * Invalidate a session
   * @param {string} sessionId - Session ID
   * @returns {Promise<boolean>} - Success status
   */
  async invalidateSession(sessionId) {
    try {
      const { error } = await supabase
        .from('user_sessions')
        .update({ is_active: false })
        .eq('id', sessionId)
      
      if (error) {
        throw error
      }
      
      return true
    } catch (error) {
      console.error('Error invalidating session:', error)
      return false
    }
  }

  /**
   * Invalidate all sessions for a user
   * @param {string} userId - User ID
   * @param {string} excludeSessionId - Session ID to exclude (current session)
   * @returns {Promise<number>} - Number of invalidated sessions
   */
  async invalidateAllUserSessions(userId, excludeSessionId = null) {
    try {
      let query = supabase
        .from('user_sessions')
        .update({ is_active: false })
        .eq('user_id', userId)
        .eq('is_active', true)
      
      if (excludeSessionId) {
        query = query.neq('id', excludeSessionId)
      }
      
      const { data, error } = await query.select()
      
      if (error) {
        throw error
      }
      
      // Log security event
      await this.logSecurityEvent(userId, 'all_sessions_invalidated', 
        `All sessions invalidated (${data?.length || 0} sessions)`, 'medium')
      
      return data?.length || 0
    } catch (error) {
      console.error('Error invalidating all user sessions:', error)
      return 0
    }
  }

  /**
   * Get active sessions for a user
   * @param {string} userId - User ID
   * @returns {Promise<Array>} - Active sessions
   */
  async getUserSessions(userId) {
    try {
      const { data: sessions, error } = await supabase
        .from('user_sessions')
        .select('id, ip_address, user_agent, location_country, location_city, created_at, last_activity')
        .eq('user_id', userId)
        .eq('is_active', true)
        .gt('expires_at', new Date().toISOString())
        .order('last_activity', { ascending: false })
      
      if (error) {
        throw error
      }
      
      return sessions || []
    } catch (error) {
      console.error('Error getting user sessions:', error)
      return []
    }
  }

  /**
   * Enforce session limit for a user
   * @param {string} userId - User ID
   */
  async enforceSessionLimit(userId) {
    try {
      const { data: sessions, error } = await supabase
        .from('user_sessions')
        .select('id, last_activity')
        .eq('user_id', userId)
        .eq('is_active', true)
        .gt('expires_at', new Date().toISOString())
        .order('last_activity', { ascending: true })
      
      if (error) {
        console.error('Error checking session limit:', error)
        return
      }
      
      if (sessions && sessions.length >= this.maxConcurrentSessions) {
        // Remove oldest sessions
        const sessionsToRemove = sessions.slice(0, sessions.length - this.maxConcurrentSessions + 1)
        
        for (const session of sessionsToRemove) {
          await this.invalidateSession(session.id)
        }
        
        await this.logSecurityEvent(userId, 'session_limit_enforced', 
          `Removed ${sessionsToRemove.length} old sessions due to limit`, 'low')
      }
    } catch (error) {
      console.error('Error enforcing session limit:', error)
    }
  }

  /**
   * Detect suspicious activity
   * @param {Object} session - Current session
   * @param {Object} request - Request object
   * @param {Object} currentFingerprint - Current device fingerprint
   * @returns {Promise<Object>} - Suspicious activity result
   */
  async detectSuspiciousActivity(session, request, currentFingerprint) {
    const suspiciousFactors = []
    
    // Check IP address change
    const currentIP = this.getClientIP(request)
    if (session.ip_address && session.ip_address !== currentIP) {
      suspiciousFactors.push('ip_change')
    }
    
    // Check device fingerprint change
    if (session.device_fingerprint !== currentFingerprint.hash) {
      const similarity = await this.deviceFingerprintManager.calculateSimilarity(
        session.device_fingerprint, 
        currentFingerprint.hash
      )
      
      if (similarity < 0.8) { // Less than 80% similarity
        suspiciousFactors.push('device_change')
      }
    }
    
    // Check for rapid location changes (if geolocation available)
    const currentLocation = await this.getLocationFromIP(currentIP)
    if (session.location_country && currentLocation && 
        session.location_country !== currentLocation && 
        this.isRapidLocationChange(session.last_activity)) {
      suspiciousFactors.push('rapid_location_change')
    }
    
    return {
      isSuspicious: suspiciousFactors.length > 0,
      factors: suspiciousFactors,
      riskScore: suspiciousFactors.length * 0.3 // Simple risk scoring
    }
  }

  /**
   * Handle suspicious activity
   * @param {Object} session - Session object
   * @param {Object} suspiciousActivity - Suspicious activity details
   */
  async handleSuspiciousActivity(session, suspiciousActivity) {
    try {
      // Log security event
      await this.logSecurityEvent(session.user_id, 'suspicious_activity', 
        'Suspicious session activity detected', 'high', {
          sessionId: session.id,
          factors: suspiciousActivity.factors,
          riskScore: suspiciousActivity.riskScore
        })
      
      // Invalidate session for high-risk activities
      if (suspiciousActivity.riskScore > 0.5) {
        await this.invalidateSession(session.id)
      }
      
      // TODO: Send notification to user about suspicious activity
      
    } catch (error) {
      console.error('Error handling suspicious activity:', error)
    }
  }

  /**
   * Generate secure token
   * @returns {string} - Secure token
   */
  generateSecureToken() {
    return crypto.randomBytes(32).toString('hex')
  }

  /**
   * Get client IP address
   * @param {Object} request - Request object
   * @returns {string} - IP address
   */
  getClientIP(request) {
    return request.headers['x-forwarded-for']?.split(',')[0] ||
           request.headers['x-real-ip'] ||
           request.connection?.remoteAddress ||
           request.socket?.remoteAddress ||
           'unknown'
  }

  /**
   * Get location from IP address (mock implementation)
   * @param {string} ipAddress - IP address
   * @returns {Promise<string>} - Country code
   */
  async getLocationFromIP(ipAddress) {
    // In production, use a geolocation service like MaxMind or ipapi
    // For now, return null
    return null
  }

  /**
   * Check if location change is rapid (impossible travel)
   * @param {string} lastActivity - Last activity timestamp
   * @returns {boolean} - Is rapid change
   */
  isRapidLocationChange(lastActivity) {
    const timeDiff = Date.now() - new Date(lastActivity).getTime()
    return timeDiff < 60 * 60 * 1000 // Less than 1 hour
  }

  /**
   * Log security event
   * @param {string} userId - User ID
   * @param {string} eventType - Event type
   * @param {string} description - Event description
   * @param {string} severity - Event severity
   * @param {Object} additionalData - Additional event data
   */
  async logSecurityEvent(userId, eventType, description, severity, additionalData = {}) {
    try {
      await supabase
        .from('security_events')
        .insert({
          user_id: userId,
          event_type: eventType,
          event_description: description,
          severity,
          additional_data: additionalData
        })
    } catch (error) {
      console.error('Error logging security event:', error)
    }
  }

  /**
   * Clean up expired sessions (should be run periodically)
   * @returns {Promise<number>} - Number of cleaned sessions
   */
  async cleanupExpiredSessions() {
    try {
      const { data, error } = await supabase
        .from('user_sessions')
        .update({ is_active: false })
        .lt('expires_at', new Date().toISOString())
        .eq('is_active', true)
        .select()
      
      if (error) {
        throw error
      }
      
      return data?.length || 0
    } catch (error) {
      console.error('Error cleaning up expired sessions:', error)
      return 0
    }
  }
}

// Export singleton instance
export const sessionSecurityManager = new SessionSecurityManager()
export default sessionSecurityManager
