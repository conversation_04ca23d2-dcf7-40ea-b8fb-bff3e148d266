# Structured Data Implementation Guide
## OceanSoulSparkles Website

This guide provides detailed instructions for implementing structured data (schema.org markup) on the OceanSoulSparkles website to enhance search visibility and enable rich snippets in Google search results.

## Table of Contents

1. [Introduction to Structured Data](#introduction-to-structured-data)
2. [Organization Schema](#organization-schema)
3. [LocalBusiness Schema](#localbusiness-schema)
4. [Service Schema](#service-schema)
5. [Product Schema](#product-schema)
6. [BreadcrumbList Schema](#breadcrumblist-schema)
7. [FAQ Schema](#faq-schema)
8. [Review Schema](#review-schema)
9. [Event Schema](#event-schema)
10. [Implementation Methods](#implementation-methods)
11. [Testing and Validation](#testing-and-validation)

## Introduction to Structured Data

Structured data is a standardized format for providing information about a page and classifying its content. Search engines use structured data to understand the content of a page and to create rich snippets in search results.

Benefits of implementing structured data:
- Enhanced search result appearance with rich snippets
- Improved click-through rates from search results
- Better understanding of your content by search engines
- Potential for featured snippets and knowledge graph inclusion
- Voice search optimization

## Organization Schema

### Purpose
The Organization schema helps search engines understand your business entity and can contribute to your Knowledge Graph entry in Google.

### Implementation Location
Add to the Layout component so it appears on all pages.

### Code Example

```jsx
// components/StructuredData/OrganizationSchema.js
const OrganizationSchema = () => {
  const organizationData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "OceanSoulSparkles",
    "url": "https://www.oceansoulsparkles.com.au",
    "logo": "https://www.oceansoulsparkles.com.auimages\bannerlogo.PNG",
    "sameAs": [
      "https://www.instagram.com/oceansoulsparkles",
      "https://www.facebook.com/OceanSoulSparkles/"
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+61-XXX-XXX-XXX",
      "contactType": "customer service",
      "email": "<EMAIL>"
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationData) }}
    />
  );
};

export default OrganizationSchema;
```

### Usage in Layout Component

```jsx
// components/Layout.js
import OrganizationSchema from './StructuredData/OrganizationSchema';

const Layout = ({ children }) => {
  return (
    <>
      <Head>
        {/* Other head elements */}
      </Head>
      <OrganizationSchema />
      {/* Rest of the layout */}
    </>
  );
};
```

## LocalBusiness Schema

### Purpose
The LocalBusiness schema helps with local SEO, making your business more visible in local search results and Google Maps.

### Implementation Location
Add to the Layout component so it appears on all pages.

### Code Example

```jsx
// components/StructuredData/LocalBusinessSchema.js
const LocalBusinessSchema = () => {
  const businessData = {
    "@context": "https://schema.org",
    "@type": "EntertainmentBusiness",
    "name": "OceanSoulSparkles",
    "image": "https://www.oceansoulsparkles.com.au/images/hero.jpg",
    "url": "https://www.oceansoulsparkles.com.au",
    "telephone": "+61-XXX-XXX-XXX",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "123 Main Street",
      "addressLocality": "Melbourne",
      "addressRegion": "VIC",
      "postalCode": "3000",
      "addressCountry": "AU"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": -37.8136,
      "longitude": 144.9631
    },
    "openingHoursSpecification": [
      {
        "@type": "OpeningHoursSpecification",
        "dayOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
        "opens": "09:00",
        "closes": "17:00"
      }
    ],
    "priceRange": "$$",
    "areaServed": [
      {
        "@type": "City",
        "name": "Melbourne"
      },
      {
        "@type": "City",
        "name": "St Kilda"
      },
      {
        "@type": "City",
        "name": "Brunswick"
      }
    ]
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(businessData) }}
    />
  );
};

export default LocalBusinessSchema;
```

### Usage in Layout Component

```jsx
// components/Layout.js
import LocalBusinessSchema from './StructuredData/LocalBusinessSchema';

const Layout = ({ children }) => {
  return (
    <>
      <Head>
        {/* Other head elements */}
      </Head>
      <OrganizationSchema />
      <LocalBusinessSchema />
      {/* Rest of the layout */}
    </>
  );
};
```

## Service Schema

### Purpose
The Service schema helps search engines understand the services you offer, potentially improving visibility for service-related searches.

### Implementation Location
Add to individual service sections on the services page.

### Code Example

```jsx
// components/StructuredData/ServiceSchema.js
const ServiceSchema = ({ service }) => {
  if (!service) return null;
  
  const serviceData = {
    "@context": "https://schema.org",
    "@type": "Service",
    "serviceType": service.title,
    "name": `${service.title} - OceanSoulSparkles`,
    "description": service.description,
    "provider": {
      "@type": "EntertainmentBusiness",
      "name": "OceanSoulSparkles",
      "url": "https://www.oceansoulsparkles.com.au"
    },
    "areaServed": {
      "@type": "City",
      "name": "Melbourne"
    },
    "offers": {
      "@type": "Offer",
      "price": service.pricing && service.pricing[0] ? service.pricing[0].price.replace(/[^\d.]/g, '') : "",
      "priceCurrency": "AUD"
    },
    "image": `https://www.oceansoulsparkles.com.au${service.image}`
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(serviceData) }}
    />
  );
};

export default ServiceSchema;
```

### Usage in Services Component

```jsx
// components/ServiceCard.js
import ServiceSchema from './StructuredData/ServiceSchema';

const ServiceCard = ({ service }) => {
  return (
    <div className={styles.serviceCard}>
      <ServiceSchema service={service} />
      {/* Rest of the service card */}
    </div>
  );
};
```

## Product Schema

### Purpose
The Product schema enables rich product snippets in search results, potentially showing price, availability, and ratings.

### Implementation Location
Add to individual product cards on the shop page.

### Code Example

```jsx
// components/StructuredData/ProductSchema.js
const ProductSchema = ({ product }) => {
  if (!product) return null;
  
  const productData = {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": product.title,
    "image": `https://www.oceansoulsparkles.com.au${product.image}`,
    "description": product.description,
    "sku": product.id,
    "brand": {
      "@type": "Brand",
      "name": "OceanSoulSparkles"
    },
    "offers": {
      "@type": "Offer",
      "url": `https://www.oceansoulsparkles.com.au/shop/${product.id}`,
      "price": product.price.toString(),
      "priceCurrency": "AUD",
      "availability": product.inStock ? "https://schema.org/InStock" : "https://schema.org/OutOfStock",
      "seller": {
        "@type": "Organization",
        "name": "OceanSoulSparkles"
      }
    }
  };
  
  // Add reviews if available
  if (product.reviews && product.reviews.length > 0) {
    productData.review = product.reviews.map(review => ({
      "@type": "Review",
      "reviewRating": {
        "@type": "Rating",
        "ratingValue": review.rating,
        "bestRating": "5"
      },
      "author": {
        "@type": "Person",
        "name": review.author
      },
      "reviewBody": review.text
    }));
    
    // Add aggregate rating
    const totalRating = product.reviews.reduce((sum, review) => sum + review.rating, 0);
    const averageRating = totalRating / product.reviews.length;
    
    productData.aggregateRating = {
      "@type": "AggregateRating",
      "ratingValue": averageRating.toFixed(1),
      "reviewCount": product.reviews.length
    };
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(productData) }}
    />
  );
};

export default ProductSchema;
```

### Usage in Product Component

```jsx
// components/ProductCard.js
import ProductSchema from './StructuredData/ProductSchema';

const ProductCard = ({ product }) => {
  return (
    <div className={styles.productCard}>
      <ProductSchema product={product} />
      {/* Rest of the product card */}
    </div>
  );
};
```

## BreadcrumbList Schema

### Purpose
The BreadcrumbList schema helps search engines understand your site structure and can display breadcrumb navigation in search results.

### Implementation Location
Add to all pages except the homepage.

### Code Example

```jsx
// components/StructuredData/BreadcrumbSchema.js
import { useRouter } from 'next/router';

const BreadcrumbSchema = () => {
  const router = useRouter();
  const pathSegments = router.asPath.split('/').filter(segment => segment);
  
  // Don't show breadcrumbs on homepage
  if (pathSegments.length === 0) return null;
  
  // Build breadcrumb items
  const breadcrumbItems = [
    { label: 'Home', path: '/' },
    ...pathSegments.map((segment, index) => {
      const path = `/${pathSegments.slice(0, index + 1).join('/')}`;
      return {
        label: segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, ' '),
        path
      };
    })
  ];
  
  const breadcrumbData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbItems.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.label,
      "item": `https://www.oceansoulsparkles.com.au${item.path}`
    }))
  };
  
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbData) }}
    />
  );
};

export default BreadcrumbSchema;
```

### Usage in Layout Component

```jsx
// components/Layout.js
import BreadcrumbSchema from './StructuredData/BreadcrumbSchema';

const Layout = ({ children }) => {
  return (
    <>
      <Head>
        {/* Other head elements */}
      </Head>
      <OrganizationSchema />
      <LocalBusinessSchema />
      <BreadcrumbSchema />
      {/* Rest of the layout */}
    </>
  );
};
```

## FAQ Schema

### Purpose
The FAQ schema can enable rich snippets for frequently asked questions, potentially showing your FAQs directly in search results.

### Implementation Location
Add to service pages, booking page, and any page with FAQ sections.

### Code Example

```jsx
// components/StructuredData/FAQSchema.js
const FAQSchema = ({ faqs }) => {
  if (!faqs || faqs.length === 0) return null;
  
  const faqData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  };
  
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(faqData) }}
    />
  );
};

export default FAQSchema;
```

### Usage in FAQ Component

```jsx
// components/FAQSection.js
import FAQSchema from './StructuredData/FAQSchema';

const FAQSection = ({ faqs }) => {
  return (
    <section className={styles.faqSection}>
      <FAQSchema faqs={faqs} />
      <h2>Frequently Asked Questions</h2>
      <div className={styles.faqGrid}>
        {faqs.map((faq, index) => (
          <div key={index} className={styles.faqItem}>
            <h3>{faq.question}</h3>
            <p>{faq.answer}</p>
          </div>
        ))}
      </div>
    </section>
  );
};
```

## Review Schema

### Purpose
The Review schema enables rich snippets for reviews and ratings, potentially showing star ratings in search results.

### Implementation Location
Add to testimonials section and product reviews.

### Code Example

```jsx
// components/StructuredData/ReviewSchema.js
const ReviewSchema = ({ reviews, itemReviewed }) => {
  if (!reviews || reviews.length === 0) return null;
  
  const reviewsData = reviews.map(review => ({
    "@context": "https://schema.org",
    "@type": "Review",
    "itemReviewed": {
      "@type": itemReviewed.type,
      "name": itemReviewed.name,
      "image": itemReviewed.image
    },
    "reviewRating": {
      "@type": "Rating",
      "ratingValue": review.rating,
      "bestRating": "5"
    },
    "name": review.title || `Review of ${itemReviewed.name}`,
    "author": {
      "@type": "Person",
      "name": review.author
    },
    "reviewBody": review.text,
    "publisher": {
      "@type": "Organization",
      "name": "OceanSoulSparkles"
    }
  }));
  
  return (
    <>
      {reviewsData.map((reviewData, index) => (
        <script
          key={index}
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(reviewData) }}
        />
      ))}
    </>
  );
};

export default ReviewSchema;
```

### Usage in Testimonials Component

```jsx
// components/Testimonials.js
import ReviewSchema from './StructuredData/ReviewSchema';

const Testimonials = ({ testimonials }) => {
  const itemReviewed = {
    type: "Service",
    name: "Face Painting Services - OceanSoulSparkles",
    image: "https://www.oceansoulsparkles.com.au/images/services/face-painting.jpg"
  };
  
  return (
    <section className={styles.testimonialsSection}>
      <ReviewSchema 
        reviews={testimonials.map(t => ({
          author: t.name,
          rating: t.rating,
          text: t.quote
        }))} 
        itemReviewed={itemReviewed} 
      />
      <h2>What Our Clients Say</h2>
      <div className={styles.testimonialsGrid}>
        {testimonials.map((testimonial, index) => (
          <div key={index} className={styles.testimonialCard}>
            <div className={styles.rating}>
              {[...Array(5)].map((_, i) => (
                <span key={i} className={i < testimonial.rating ? styles.starFilled : styles.starEmpty}>★</span>
              ))}
            </div>
            <blockquote>{testimonial.quote}</blockquote>
            <cite>— {testimonial.name}</cite>
          </div>
        ))}
      </div>
    </section>
  );
};
```

## Event Schema

### Purpose
The Event schema can enable rich snippets for events, potentially showing event details directly in search results.

### Implementation Location
Add to any workshop or event pages.

### Code Example

```jsx
// components/StructuredData/EventSchema.js
const EventSchema = ({ event }) => {
  if (!event) return null;
  
  const eventData = {
    "@context": "https://schema.org",
    "@type": "Event",
    "name": event.title,
    "startDate": event.startDate,
    "endDate": event.endDate,
    "location": {
      "@type": "Place",
      "name": event.venue,
      "address": {
        "@type": "PostalAddress",
        "streetAddress": event.address.street,
        "addressLocality": event.address.city,
        "addressRegion": event.address.state,
        "postalCode": event.address.zip,
        "addressCountry": "AU"
      }
    },
    "image": event.image,
    "description": event.description,
    "offers": {
      "@type": "Offer",
      "url": `https://www.oceansoulsparkles.com.au/events/${event.id}`,
      "price": event.price.toString(),
      "priceCurrency": "AUD",
      "availability": event.soldOut ? "https://schema.org/SoldOut" : "https://schema.org/InStock"
    },
    "organizer": {
      "@type": "Organization",
      "name": "OceanSoulSparkles",
      "url": "https://www.oceansoulsparkles.com.au"
    }
  };
  
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(eventData) }}
    />
  );
};

export default EventSchema;
```

## Implementation Methods

### Method 1: Component-Based Implementation
Create separate components for each schema type and include them in the appropriate pages or components.

### Method 2: Centralized Schema Manager
Create a SchemaManager component that handles all schema types and conditionally renders the appropriate schemas based on the current page.

```jsx
// components/StructuredData/SchemaManager.js
import { useRouter } from 'next/router';

const SchemaManager = ({ pageData }) => {
  const router = useRouter();
  const path = router.pathname;
  
  // Always include Organization schema
  const schemas = [
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "OceanSoulSparkles",
      "url": "https://www.oceansoulsparkles.com.au",
      "logo": "https://www.oceansoulsparkles.com.auimages\bannerlogo.PNG",
      "sameAs": [
        "https://www.instagram.com/oceansoulsparkles",
        "https://www.facebook.com/OceanSoulSparkles/"
      ]
    }
  ];
  
  // Add LocalBusiness schema
  schemas.push({
    "@context": "https://schema.org",
    "@type": "EntertainmentBusiness",
    "name": "OceanSoulSparkles",
    "image": "https://www.oceansoulsparkles.com.au/images/hero.jpg",
    "url": "https://www.oceansoulsparkles.com.au",
    "telephone": "+61-XXX-XXX-XXX",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "123 Main Street",
      "addressLocality": "Melbourne",
      "addressRegion": "VIC",
      "postalCode": "3000",
      "addressCountry": "AU"
    }
  });
  
  // Add page-specific schemas
  if (path !== '/') {
    // Add BreadcrumbList schema for all pages except homepage
    // ...
  }
  
  if (path === '/services' && pageData.service) {
    // Add Service schema
    // ...
  }
  
  if (path === '/shop' && pageData.product) {
    // Add Product schema
    // ...
  }
  
  // Return all schemas
  return (
    <>
      {schemas.map((schema, index) => (
        <script
          key={index}
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
        />
      ))}
    </>
  );
};

export default SchemaManager;
```

## Testing and Validation

### Google Rich Results Test
Use the [Google Rich Results Test](https://search.google.com/test/rich-results) to validate your structured data implementation and preview how your pages might appear in search results.

### Schema.org Validator
Use the [Schema.org Validator](https://validator.schema.org/) to check your structured data for errors and warnings.

### Google Search Console
Monitor the "Enhancements" section in Google Search Console to track the performance of your structured data and identify any issues.

### Implementation Checklist

- [ ] Organization schema on all pages
- [ ] LocalBusiness schema on all pages
- [ ] Service schema on service pages
- [ ] Product schema on product pages
- [ ] BreadcrumbList schema on all pages except homepage
- [ ] FAQ schema on pages with FAQs
- [ ] Review schema on testimonials and product reviews
- [ ] Event schema on event pages
- [ ] Validate all schemas with testing tools
- [ ] Monitor performance in Google Search Console
