/**
 * Privacy Preference Manager for Ocean Soul Sparkles
 * Handles customer privacy preferences and cookie consent management
 * 
 * Phase 9.2: Data Protection & Privacy Compliance
 */

import { createClient } from '@supabase/supabase-js'
import crypto from 'crypto'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

/**
 * Privacy Manager Class
 * Handles privacy preferences and consent management
 */
export class PrivacyManager {
  constructor() {
    this.consentTypes = {
      'marketing': {
        name: 'Marketing Communications',
        description: 'Receive promotional emails, SMS, and marketing materials',
        required: false,
        defaultValue: false
      },
      'analytics': {
        name: 'Analytics & Performance',
        description: 'Help us improve our services through usage analytics',
        required: false,
        defaultValue: false
      },
      'cookies': {
        name: 'Cookie Preferences',
        description: 'Store preferences and enhance your browsing experience',
        required: false,
        defaultValue: false
      },
      'data_processing': {
        name: 'Data Processing',
        description: 'Process your personal data for service delivery',
        required: true,
        defaultValue: true
      }
    }
    
    this.cookieCategories = {
      'essential': {
        name: 'Essential Cookies',
        description: 'Required for basic website functionality',
        required: true,
        examples: ['Session management', 'Security', 'Load balancing']
      },
      'functional': {
        name: 'Functional Cookies',
        description: 'Remember your preferences and settings',
        required: false,
        examples: ['Language preferences', 'Theme settings', 'Form data']
      },
      'analytics': {
        name: 'Analytics Cookies',
        description: 'Help us understand how you use our website',
        required: false,
        examples: ['Google Analytics', 'Performance monitoring', 'Usage statistics']
      },
      'marketing': {
        name: 'Marketing Cookies',
        description: 'Used to deliver relevant advertisements',
        required: false,
        examples: ['Ad targeting', 'Social media integration', 'Conversion tracking']
      }
    }

    this.privacyPreferenceCategories = {
      'communication': {
        name: 'Communication Preferences',
        preferences: {
          'email_notifications': 'Email notifications for bookings',
          'sms_notifications': 'SMS notifications for appointments',
          'marketing_emails': 'Promotional emails and offers',
          'newsletter': 'Monthly newsletter subscription'
        }
      },
      'data_sharing': {
        name: 'Data Sharing Preferences',
        preferences: {
          'third_party_analytics': 'Share data with analytics providers',
          'social_media_integration': 'Connect with social media platforms',
          'partner_offers': 'Share data for partner offers'
        }
      },
      'personalization': {
        name: 'Personalization Settings',
        preferences: {
          'personalized_recommendations': 'Receive personalized service recommendations',
          'location_based_services': 'Use location for nearby services',
          'browsing_history': 'Use browsing history for recommendations'
        }
      }
    }
  }

  /**
   * Record user consent
   * @param {Object} consentData - Consent information
   * @returns {Promise<Object>} - Consent record
   */
  async recordConsent(consentData) {
    try {
      const {
        userId = null,
        customerId = null,
        consentType,
        consentGiven,
        consentVersion = '1.0',
        consentMethod = 'explicit',
        consentSource = 'website',
        ipAddress = null,
        userAgent = null,
        consentData: additionalData = {}
      } = consentData

      // Validate consent type
      if (!this.consentTypes[consentType]) {
        throw new Error(`Invalid consent type: ${consentType}`)
      }

      // Check if consent already exists
      const { data: existingConsent } = await supabase
        .from('user_consents')
        .select('*')
        .eq(userId ? 'user_id' : 'customer_id', userId || customerId)
        .eq('consent_type', consentType)
        .eq('consent_version', consentVersion)
        .single()

      if (existingConsent) {
        // Update existing consent
        const { data: updatedConsent, error } = await supabase
          .from('user_consents')
          .update({
            consent_given: consentGiven,
            consent_method: consentMethod,
            consent_source: consentSource,
            ip_address: ipAddress,
            user_agent: userAgent,
            consent_data: additionalData,
            withdrawn_at: consentGiven ? null : new Date().toISOString(),
            withdrawal_reason: consentGiven ? null : 'User withdrawal',
            updated_at: new Date().toISOString()
          })
          .eq('id', existingConsent.id)
          .select()
          .single()

        if (error) throw error
        return updatedConsent
      } else {
        // Create new consent record
        const { data: newConsent, error } = await supabase
          .from('user_consents')
          .insert({
            user_id: userId,
            customer_id: customerId,
            consent_type: consentType,
            consent_given: consentGiven,
            consent_version: consentVersion,
            consent_method: consentMethod,
            consent_source: consentSource,
            ip_address: ipAddress,
            user_agent: userAgent,
            consent_data: additionalData
          })
          .select()
          .single()

        if (error) throw error
        return newConsent
      }
    } catch (error) {
      console.error('Error recording consent:', error)
      throw error
    }
  }

  /**
   * Record cookie consent
   * @param {Object} cookieConsentData - Cookie consent information
   * @returns {Promise<Object>} - Cookie consent record
   */
  async recordCookieConsent(cookieConsentData) {
    try {
      const {
        sessionId = null,
        userId = null,
        customerId = null,
        essentialCookies = true,
        functionalCookies = false,
        analyticsCookies = false,
        marketingCookies = false,
        consentVersion = '1.0',
        ipAddress = null,
        userAgent = null
      } = cookieConsentData

      const expiresAt = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year

      // Check for existing cookie consent
      let query = supabase.from('cookie_consents').select('*')
      
      if (userId) {
        query = query.eq('user_id', userId)
      } else if (customerId) {
        query = query.eq('customer_id', customerId)
      } else if (sessionId) {
        query = query.eq('session_id', sessionId)
      }

      const { data: existingConsent } = await query.single()

      const consentData = {
        session_id: sessionId,
        user_id: userId,
        customer_id: customerId,
        essential_cookies: essentialCookies,
        functional_cookies: functionalCookies,
        analytics_cookies: analyticsCookies,
        marketing_cookies: marketingCookies,
        consent_version: consentVersion,
        ip_address: ipAddress,
        user_agent: userAgent,
        consent_given_at: new Date().toISOString(),
        last_updated_at: new Date().toISOString(),
        expires_at: expiresAt.toISOString()
      }

      if (existingConsent) {
        // Update existing consent
        const { data: updatedConsent, error } = await supabase
          .from('cookie_consents')
          .update(consentData)
          .eq('id', existingConsent.id)
          .select()
          .single()

        if (error) throw error
        return updatedConsent
      } else {
        // Create new consent record
        const { data: newConsent, error } = await supabase
          .from('cookie_consents')
          .insert(consentData)
          .select()
          .single()

        if (error) throw error
        return newConsent
      }
    } catch (error) {
      console.error('Error recording cookie consent:', error)
      throw error
    }
  }

  /**
   * Set privacy preference
   * @param {Object} preferenceData - Privacy preference data
   * @returns {Promise<Object>} - Privacy preference record
   */
  async setPrivacyPreference(preferenceData) {
    try {
      const {
        userId = null,
        customerId = null,
        preferenceCategory,
        preferenceKey,
        preferenceValue,
        isActive = true
      } = preferenceData

      // Validate preference category and key
      if (!this.privacyPreferenceCategories[preferenceCategory]) {
        throw new Error(`Invalid preference category: ${preferenceCategory}`)
      }

      const categoryPrefs = this.privacyPreferenceCategories[preferenceCategory].preferences
      if (!categoryPrefs[preferenceKey]) {
        throw new Error(`Invalid preference key: ${preferenceKey}`)
      }

      const { data: preference, error } = await supabase
        .from('privacy_preferences')
        .upsert({
          user_id: userId,
          customer_id: customerId,
          preference_category: preferenceCategory,
          preference_key: preferenceKey,
          preference_value: preferenceValue,
          is_active: isActive,
          updated_at: new Date().toISOString()
        })
        .select()
        .single()

      if (error) throw error
      return preference
    } catch (error) {
      console.error('Error setting privacy preference:', error)
      throw error
    }
  }

  /**
   * Get user consents
   * @param {string} userId - User ID
   * @param {string} customerId - Customer ID
   * @returns {Promise<Object>} - User consents
   */
  async getUserConsents(userId = null, customerId = null) {
    try {
      let query = supabase
        .from('user_consents')
        .select('*')
        .order('created_at', { ascending: false })

      if (userId) {
        query = query.eq('user_id', userId)
      } else if (customerId) {
        query = query.eq('customer_id', customerId)
      } else {
        throw new Error('Either userId or customerId must be provided')
      }

      const { data: consents, error } = await query

      if (error) throw error

      // Organize consents by type
      const consentsByType = {}
      consents?.forEach(consent => {
        consentsByType[consent.consent_type] = consent
      })

      // Add default values for missing consents
      Object.keys(this.consentTypes).forEach(type => {
        if (!consentsByType[type]) {
          consentsByType[type] = {
            consent_type: type,
            consent_given: this.consentTypes[type].defaultValue,
            consent_version: '1.0',
            created_at: null
          }
        }
      })

      return consentsByType
    } catch (error) {
      console.error('Error getting user consents:', error)
      throw error
    }
  }

  /**
   * Get cookie consent
   * @param {string} sessionId - Session ID
   * @param {string} userId - User ID
   * @param {string} customerId - Customer ID
   * @returns {Promise<Object>} - Cookie consent
   */
  async getCookieConsent(sessionId = null, userId = null, customerId = null) {
    try {
      let query = supabase.from('cookie_consents').select('*')

      if (userId) {
        query = query.eq('user_id', userId)
      } else if (customerId) {
        query = query.eq('customer_id', customerId)
      } else if (sessionId) {
        query = query.eq('session_id', sessionId)
      } else {
        // Return default consent (essential only)
        return {
          essential_cookies: true,
          functional_cookies: false,
          analytics_cookies: false,
          marketing_cookies: false,
          consent_given_at: null,
          expires_at: null
        }
      }

      const { data: consent, error } = await query
        .order('created_at', { ascending: false })
        .limit(1)
        .single()

      if (error && error.code !== 'PGRST116') { // Not found is OK
        throw error
      }

      if (!consent) {
        // Return default consent
        return {
          essential_cookies: true,
          functional_cookies: false,
          analytics_cookies: false,
          marketing_cookies: false,
          consent_given_at: null,
          expires_at: null
        }
      }

      // Check if consent has expired
      if (consent.expires_at && new Date(consent.expires_at) < new Date()) {
        return {
          essential_cookies: true,
          functional_cookies: false,
          analytics_cookies: false,
          marketing_cookies: false,
          consent_given_at: null,
          expires_at: null,
          expired: true
        }
      }

      return consent
    } catch (error) {
      console.error('Error getting cookie consent:', error)
      throw error
    }
  }

  /**
   * Get privacy preferences
   * @param {string} userId - User ID
   * @param {string} customerId - Customer ID
   * @returns {Promise<Object>} - Privacy preferences organized by category
   */
  async getPrivacyPreferences(userId = null, customerId = null) {
    try {
      let query = supabase
        .from('privacy_preferences')
        .select('*')
        .eq('is_active', true)

      if (userId) {
        query = query.eq('user_id', userId)
      } else if (customerId) {
        query = query.eq('customer_id', customerId)
      } else {
        throw new Error('Either userId or customerId must be provided')
      }

      const { data: preferences, error } = await query

      if (error) throw error

      // Organize preferences by category
      const preferencesByCategory = {}
      
      Object.keys(this.privacyPreferenceCategories).forEach(category => {
        preferencesByCategory[category] = {
          name: this.privacyPreferenceCategories[category].name,
          preferences: {}
        }

        // Add default values
        Object.keys(this.privacyPreferenceCategories[category].preferences).forEach(key => {
          preferencesByCategory[category].preferences[key] = {
            value: false, // Default to false for privacy
            description: this.privacyPreferenceCategories[category].preferences[key],
            updated_at: null
          }
        })
      })

      // Override with actual preferences
      preferences?.forEach(pref => {
        if (preferencesByCategory[pref.preference_category]) {
          preferencesByCategory[pref.preference_category].preferences[pref.preference_key] = {
            value: pref.preference_value,
            description: this.privacyPreferenceCategories[pref.preference_category].preferences[pref.preference_key],
            updated_at: pref.updated_at
          }
        }
      })

      return preferencesByCategory
    } catch (error) {
      console.error('Error getting privacy preferences:', error)
      throw error
    }
  }

  /**
   * Withdraw consent
   * @param {string} userId - User ID
   * @param {string} customerId - Customer ID
   * @param {string} consentType - Consent type to withdraw
   * @param {string} reason - Withdrawal reason
   * @returns {Promise<Object>} - Updated consent record
   */
  async withdrawConsent(userId = null, customerId = null, consentType, reason = 'User request') {
    try {
      let query = supabase
        .from('user_consents')
        .update({
          consent_given: false,
          withdrawn_at: new Date().toISOString(),
          withdrawal_reason: reason,
          updated_at: new Date().toISOString()
        })
        .eq('consent_type', consentType)

      if (userId) {
        query = query.eq('user_id', userId)
      } else if (customerId) {
        query = query.eq('customer_id', customerId)
      } else {
        throw new Error('Either userId or customerId must be provided')
      }

      const { data: updatedConsent, error } = await query.select().single()

      if (error) throw error
      return updatedConsent
    } catch (error) {
      console.error('Error withdrawing consent:', error)
      throw error
    }
  }

  /**
   * Get consent types and their descriptions
   * @returns {Object} - Consent types
   */
  getConsentTypes() {
    return this.consentTypes
  }

  /**
   * Get cookie categories and their descriptions
   * @returns {Object} - Cookie categories
   */
  getCookieCategories() {
    return this.cookieCategories
  }

  /**
   * Get privacy preference categories
   * @returns {Object} - Privacy preference categories
   */
  getPrivacyPreferenceCategories() {
    return this.privacyPreferenceCategories
  }

  /**
   * Check if user has given specific consent
   * @param {string} userId - User ID
   * @param {string} customerId - Customer ID
   * @param {string} consentType - Consent type
   * @returns {Promise<boolean>} - Whether consent is given
   */
  async hasConsent(userId = null, customerId = null, consentType) {
    try {
      const consents = await this.getUserConsents(userId, customerId)
      return consents[consentType]?.consent_given || false
    } catch (error) {
      console.error('Error checking consent:', error)
      return false
    }
  }

  /**
   * Generate privacy dashboard data
   * @param {string} userId - User ID
   * @param {string} customerId - Customer ID
   * @returns {Promise<Object>} - Privacy dashboard data
   */
  async getPrivacyDashboardData(userId = null, customerId = null) {
    try {
      const [consents, cookieConsent, preferences] = await Promise.all([
        this.getUserConsents(userId, customerId),
        this.getCookieConsent(null, userId, customerId),
        this.getPrivacyPreferences(userId, customerId)
      ])

      return {
        consents,
        cookieConsent,
        preferences,
        consentTypes: this.consentTypes,
        cookieCategories: this.cookieCategories,
        preferenceCategories: this.privacyPreferenceCategories
      }
    } catch (error) {
      console.error('Error getting privacy dashboard data:', error)
      throw error
    }
  }
}

// Export singleton instance
export const privacyManager = new PrivacyManager()
export default privacyManager
