/* styles/artists/ServiceSpecializationManager.module.css */
.specializationManager {
  padding: 20px;
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-top: 20px;
}

.specializationManager h3 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #333;
}
.instructions {
    font-size: 0.95rem;
    color: #555;
    margin-bottom: 20px;
}

.loadingText, .errorText, .noServicesText {
  text-align: center;
  padding: 20px;
  font-style: italic;
  color: #777;
}
.errorText {
    color: #c0392b;
}
.retryButton {
    margin-left: 10px;
    padding: 5px 10px;
    font-size: 0.9rem;
    cursor: pointer;
}

.categorySection {
  margin-bottom: 20px;
}

.categoryTitle {
  font-size: 1.2rem;
  color: #444;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid #eee;
}

.serviceList {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.serviceItem {
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 4px;
  background-color: #f9f9f9;
  cursor: pointer;
  transition: background-color 0.2s;
}
.serviceItem:hover {
    background-color: #f0f0f0;
}

.checkbox {
  margin-right: 10px;
  transform: scale(1.1); /* Slightly larger checkbox */
}

.serviceName {
  font-weight: 500;
  color: #333;
}

.serviceMeta {
    font-size: 0.85rem;
    color: #777;
    margin-left: 8px;
}
.statusInactive {
    color: #e74c3c;
    font-weight: bold;
}

.formActions {
  margin-top: 25px;
  text-align: right;
}

.saveButton {
  padding: 10px 20px;
  background: linear-gradient(135deg, #28a745, #218838); /* Green gradient */
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
}

.saveButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.saveButton:hover:not(:disabled) {
  background: linear-gradient(135deg, #218838, #1e7e34);
}
