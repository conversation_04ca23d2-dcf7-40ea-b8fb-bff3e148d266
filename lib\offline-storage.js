/**
 * Offline Storage Manager for Ocean Soul Sparkles
 * Manages IndexedDB for offline data storage and synchronization
 */

import { openDB } from 'idb'

const DB_NAME = 'OceanSoulSparklesDB'
const DB_VERSION = 1

// Database stores
const STORES = {
  BOOKINGS: 'offline_bookings',
  PHOTOS: 'offline_photos',
  CUSTOMERS: 'cached_customers',
  SERVICES: 'cached_services',
  DASHBOARD_DATA: 'cached_dashboard_data',
  SYNC_QUEUE: 'sync_queue'
}

class OfflineStorageManager {
  constructor() {
    this.db = null
    this.isInitialized = false
  }

  // Initialize the database
  async init() {
    if (this.isInitialized) return this.db

    try {
      this.db = await openDB(DB_NAME, DB_VERSION, {
        upgrade(db, oldVersion, newVersion, transaction) {
          console.log('[OfflineStorage] Upgrading database from version', oldVersion, 'to', newVersion)

          // Offline bookings store
          if (!db.objectStoreNames.contains(STORES.BOOKINGS)) {
            const bookingsStore = db.createObjectStore(STORES.BOOKINGS, {
              keyPath: 'id',
              autoIncrement: true
            })
            bookingsStore.createIndex('timestamp', 'timestamp')
            bookingsStore.createIndex('status', 'status')
          }

          // Offline photos store
          if (!db.objectStoreNames.contains(STORES.PHOTOS)) {
            const photosStore = db.createObjectStore(STORES.PHOTOS, {
              keyPath: 'id',
              autoIncrement: true
            })
            photosStore.createIndex('bookingId', 'bookingId')
            photosStore.createIndex('timestamp', 'timestamp')
            photosStore.createIndex('type', 'type') // 'before', 'after', 'portfolio'
          }

          // Cached customers store
          if (!db.objectStoreNames.contains(STORES.CUSTOMERS)) {
            const customersStore = db.createObjectStore(STORES.CUSTOMERS, {
              keyPath: 'id'
            })
            customersStore.createIndex('email', 'email')
            customersStore.createIndex('phone', 'phone')
            customersStore.createIndex('lastUpdated', 'lastUpdated')
          }

          // Cached services store
          if (!db.objectStoreNames.contains(STORES.SERVICES)) {
            const servicesStore = db.createObjectStore(STORES.SERVICES, {
              keyPath: 'id'
            })
            servicesStore.createIndex('category', 'category')
            servicesStore.createIndex('lastUpdated', 'lastUpdated')
          }

          // Cached dashboard data store
          if (!db.objectStoreNames.contains(STORES.DASHBOARD_DATA)) {
            const dashboardStore = db.createObjectStore(STORES.DASHBOARD_DATA, {
              keyPath: 'key'
            })
            dashboardStore.createIndex('lastUpdated', 'lastUpdated')
          }

          // Sync queue store
          if (!db.objectStoreNames.contains(STORES.SYNC_QUEUE)) {
            const syncStore = db.createObjectStore(STORES.SYNC_QUEUE, {
              keyPath: 'id',
              autoIncrement: true
            })
            syncStore.createIndex('type', 'type')
            syncStore.createIndex('priority', 'priority')
            syncStore.createIndex('timestamp', 'timestamp')
          }
        }
      })

      this.isInitialized = true
      console.log('[OfflineStorage] Database initialized successfully')
      return this.db
    } catch (error) {
      console.error('[OfflineStorage] Failed to initialize database:', error)
      throw error
    }
  }

  // Offline Bookings Management
  async saveOfflineBooking(bookingData) {
    await this.init()
    
    const booking = {
      ...bookingData,
      id: `offline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      status: 'pending_sync',
      offline: true
    }

    try {
      await this.db.add(STORES.BOOKINGS, booking)
      console.log('[OfflineStorage] Offline booking saved:', booking.id)
      
      // Add to sync queue
      await this.addToSyncQueue('booking', booking.id, 'high')
      
      return booking.id
    } catch (error) {
      console.error('[OfflineStorage] Failed to save offline booking:', error)
      throw error
    }
  }

  async getOfflineBookings() {
    await this.init()
    
    try {
      const bookings = await this.db.getAll(STORES.BOOKINGS)
      return bookings.filter(booking => booking.status === 'pending_sync')
    } catch (error) {
      console.error('[OfflineStorage] Failed to get offline bookings:', error)
      return []
    }
  }

  async markBookingSynced(bookingId) {
    await this.init()
    
    try {
      const booking = await this.db.get(STORES.BOOKINGS, bookingId)
      if (booking) {
        booking.status = 'synced'
        booking.syncedAt = new Date().toISOString()
        await this.db.put(STORES.BOOKINGS, booking)
        
        // Remove from sync queue
        await this.removeFromSyncQueue('booking', bookingId)
      }
    } catch (error) {
      console.error('[OfflineStorage] Failed to mark booking as synced:', error)
    }
  }

  // Photo Management
  async saveOfflinePhoto(photoData) {
    await this.init()
    
    const photo = {
      ...photoData,
      id: `photo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      status: 'pending_sync'
    }

    try {
      await this.db.add(STORES.PHOTOS, photo)
      console.log('[OfflineStorage] Offline photo saved:', photo.id)
      
      // Add to sync queue
      await this.addToSyncQueue('photo', photo.id, 'medium')
      
      return photo.id
    } catch (error) {
      console.error('[OfflineStorage] Failed to save offline photo:', error)
      throw error
    }
  }

  async getOfflinePhotos(bookingId = null) {
    await this.init()
    
    try {
      if (bookingId) {
        return await this.db.getAllFromIndex(STORES.PHOTOS, 'bookingId', bookingId)
      } else {
        return await this.db.getAll(STORES.PHOTOS)
      }
    } catch (error) {
      console.error('[OfflineStorage] Failed to get offline photos:', error)
      return []
    }
  }

  // Cache Management
  async cacheCustomers(customers) {
    await this.init()
    
    try {
      const tx = this.db.transaction(STORES.CUSTOMERS, 'readwrite')
      const store = tx.objectStore(STORES.CUSTOMERS)
      
      for (const customer of customers) {
        await store.put({
          ...customer,
          lastUpdated: new Date().toISOString()
        })
      }
      
      await tx.done
      console.log(`[OfflineStorage] Cached ${customers.length} customers`)
    } catch (error) {
      console.error('[OfflineStorage] Failed to cache customers:', error)
    }
  }

  async getCachedCustomers() {
    await this.init()
    
    try {
      return await this.db.getAll(STORES.CUSTOMERS)
    } catch (error) {
      console.error('[OfflineStorage] Failed to get cached customers:', error)
      return []
    }
  }

  async cacheServices(services) {
    await this.init()
    
    try {
      const tx = this.db.transaction(STORES.SERVICES, 'readwrite')
      const store = tx.objectStore(STORES.SERVICES)
      
      for (const service of services) {
        await store.put({
          ...service,
          lastUpdated: new Date().toISOString()
        })
      }
      
      await tx.done
      console.log(`[OfflineStorage] Cached ${services.length} services`)
    } catch (error) {
      console.error('[OfflineStorage] Failed to cache services:', error)
    }
  }

  async getCachedServices() {
    await this.init()
    
    try {
      return await this.db.getAll(STORES.SERVICES)
    } catch (error) {
      console.error('[OfflineStorage] Failed to get cached services:', error)
      return []
    }
  }

  async cacheDashboardData(key, data) {
    await this.init()
    
    try {
      await this.db.put(STORES.DASHBOARD_DATA, {
        key,
        data,
        lastUpdated: new Date().toISOString()
      })
      console.log(`[OfflineStorage] Cached dashboard data: ${key}`)
    } catch (error) {
      console.error('[OfflineStorage] Failed to cache dashboard data:', error)
    }
  }

  async getCachedDashboardData(key) {
    await this.init()
    
    try {
      const result = await this.db.get(STORES.DASHBOARD_DATA, key)
      return result?.data || null
    } catch (error) {
      console.error('[OfflineStorage] Failed to get cached dashboard data:', error)
      return null
    }
  }

  // Sync Queue Management
  async addToSyncQueue(type, itemId, priority = 'medium') {
    await this.init()
    
    try {
      await this.db.add(STORES.SYNC_QUEUE, {
        type,
        itemId,
        priority,
        timestamp: new Date().toISOString(),
        retryCount: 0
      })
    } catch (error) {
      console.error('[OfflineStorage] Failed to add to sync queue:', error)
    }
  }

  async getSyncQueue() {
    await this.init()
    
    try {
      const items = await this.db.getAll(STORES.SYNC_QUEUE)
      // Sort by priority (high, medium, low) and timestamp
      return items.sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 }
        const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority]
        if (priorityDiff !== 0) return priorityDiff
        return new Date(a.timestamp) - new Date(b.timestamp)
      })
    } catch (error) {
      console.error('[OfflineStorage] Failed to get sync queue:', error)
      return []
    }
  }

  async removeFromSyncQueue(type, itemId) {
    await this.init()
    
    try {
      const items = await this.db.getAllFromIndex(STORES.SYNC_QUEUE, 'type', type)
      const item = items.find(i => i.itemId === itemId)
      if (item) {
        await this.db.delete(STORES.SYNC_QUEUE, item.id)
      }
    } catch (error) {
      console.error('[OfflineStorage] Failed to remove from sync queue:', error)
    }
  }

  // Cleanup old data
  async cleanup(maxAge = 7 * 24 * 60 * 60 * 1000) { // 7 days default
    await this.init()
    
    const cutoffDate = new Date(Date.now() - maxAge).toISOString()
    
    try {
      // Clean up synced bookings older than maxAge
      const bookings = await this.db.getAll(STORES.BOOKINGS)
      for (const booking of bookings) {
        if (booking.status === 'synced' && booking.syncedAt < cutoffDate) {
          await this.db.delete(STORES.BOOKINGS, booking.id)
        }
      }

      // Clean up old cached data
      const dashboardData = await this.db.getAll(STORES.DASHBOARD_DATA)
      for (const data of dashboardData) {
        if (data.lastUpdated < cutoffDate) {
          await this.db.delete(STORES.DASHBOARD_DATA, data.key)
        }
      }

      console.log('[OfflineStorage] Cleanup completed')
    } catch (error) {
      console.error('[OfflineStorage] Cleanup failed:', error)
    }
  }

  // Get storage statistics
  async getStorageStats() {
    await this.init()
    
    try {
      const stats = {}
      
      for (const [name, store] of Object.entries(STORES)) {
        const count = await this.db.count(store)
        stats[name.toLowerCase()] = count
      }
      
      return stats
    } catch (error) {
      console.error('[OfflineStorage] Failed to get storage stats:', error)
      return {}
    }
  }
}

// Export singleton instance
export const offlineStorage = new OfflineStorageManager()
export default offlineStorage
