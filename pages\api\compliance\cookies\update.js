/**
 * <PERSON><PERSON> Consent Update API Endpoint for Ocean Soul Sparkles
 * Updates cookie consent preferences
 * 
 * Phase 9.2: Data Protection & Privacy Compliance
 */

import { withAdminAuth } from '@/lib/admin-auth'
import { privacyManager } from '@/lib/compliance/privacy-manager'

async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { 
      sessionId,
      userId, 
      customerId, 
      essential_cookies = true,
      functional_cookies = false,
      analytics_cookies = false,
      marketing_cookies = false,
      consentVersion = '1.0'
    } = req.body

    // Get client IP and user agent for audit trail
    const ipAddress = req.headers['x-forwarded-for']?.split(',')[0] || 
                     req.headers['x-real-ip'] || 
                     req.connection?.remoteAddress || 
                     'unknown'
    const userAgent = req.headers['user-agent'] || 'unknown'

    // Record cookie consent
    const cookieConsent = await privacyManager.recordCookieConsent({
      sessionId,
      userId,
      customerId,
      essentialCookies: essential_cookies,
      functionalCookies: functional_cookies,
      analyticsCookies: analytics_cookies,
      marketingCookies: marketing_cookies,
      consentVersion,
      ipAddress,
      userAgent
    })

    res.status(200).json({
      success: true,
      cookieConsent,
      message: 'Cookie preferences updated successfully'
    })

  } catch (error) {
    console.error('Cookie consent update error:', error)
    res.status(500).json({
      error: 'Failed to update cookie preferences',
      message: error.message
    })
  }
}

export default withAdminAuth(handler)
