/**
 * Mobile Optimization Hook
 * Ocean Soul Sparkles - Enhanced Mobile Experience
 */

import { useState, useEffect, useCallback } from 'react'

export function useMobileOptimization() {
  const [deviceInfo, setDeviceInfo] = useState({
    isMobile: false,
    isTablet: false,
    isDesktop: false,
    isIOS: false,
    isAndroid: false,
    isTouchDevice: false,
    screenWidth: 0,
    screenHeight: 0,
    orientation: 'portrait',
    pixelRatio: 1,
    hasNotchSupport: false,
    supportsVibration: false,
    connectionType: 'unknown'
  })

  const [viewportInfo, setViewportInfo] = useState({
    width: 0,
    height: 0,
    availableHeight: 0,
    safeAreaInsets: {
      top: 0,
      bottom: 0,
      left: 0,
      right: 0
    }
  })

  const [performanceInfo, setPerformanceInfo] = useState({
    isLowEndDevice: false,
    memoryInfo: null,
    connectionSpeed: 'unknown',
    reducedMotion: false
  })

  // Detect device capabilities
  const detectDevice = useCallback(() => {
    if (typeof window === 'undefined') return

    const userAgent = navigator.userAgent.toLowerCase()
    const width = window.innerWidth
    const height = window.innerHeight
    
    // Device type detection
    const isMobile = width <= 768
    const isTablet = width > 768 && width <= 1024
    const isDesktop = width > 1024
    
    // OS detection
    const isIOS = /iphone|ipad|ipod/.test(userAgent)
    const isAndroid = /android/.test(userAgent)
    
    // Touch support
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0
    
    // Orientation
    const orientation = width > height ? 'landscape' : 'portrait'
    
    // Pixel ratio
    const pixelRatio = window.devicePixelRatio || 1
    
    // Notch support (iOS devices with safe area)
    const hasNotchSupport = isIOS && CSS.supports('padding: env(safe-area-inset-top)')
    
    // Vibration support
    const supportsVibration = 'vibrate' in navigator
    
    // Connection type
    let connectionType = 'unknown'
    if (navigator.connection) {
      connectionType = navigator.connection.effectiveType || 'unknown'
    }

    setDeviceInfo({
      isMobile,
      isTablet,
      isDesktop,
      isIOS,
      isAndroid,
      isTouchDevice,
      screenWidth: width,
      screenHeight: height,
      orientation,
      pixelRatio,
      hasNotchSupport,
      supportsVibration,
      connectionType
    })
  }, [])

  // Detect viewport information
  const detectViewport = useCallback(() => {
    if (typeof window === 'undefined') return

    const width = window.innerWidth
    const height = window.innerHeight
    const availableHeight = window.screen.availableHeight
    
    // Safe area insets (for devices with notches)
    const safeAreaInsets = {
      top: 0,
      bottom: 0,
      left: 0,
      right: 0
    }

    // Try to get safe area insets from CSS environment variables
    if (CSS.supports('padding: env(safe-area-inset-top)')) {
      const computedStyle = getComputedStyle(document.documentElement)
      safeAreaInsets.top = parseInt(computedStyle.getPropertyValue('env(safe-area-inset-top)')) || 0
      safeAreaInsets.bottom = parseInt(computedStyle.getPropertyValue('env(safe-area-inset-bottom)')) || 0
      safeAreaInsets.left = parseInt(computedStyle.getPropertyValue('env(safe-area-inset-left)')) || 0
      safeAreaInsets.right = parseInt(computedStyle.getPropertyValue('env(safe-area-inset-right)')) || 0
    }

    setViewportInfo({
      width,
      height,
      availableHeight,
      safeAreaInsets
    })
  }, [])

  // Detect performance characteristics
  const detectPerformance = useCallback(() => {
    if (typeof window === 'undefined') return

    // Memory information (Chrome only)
    let memoryInfo = null
    if (navigator.memory) {
      memoryInfo = {
        usedJSHeapSize: navigator.memory.usedJSHeapSize,
        totalJSHeapSize: navigator.memory.totalJSHeapSize,
        jsHeapSizeLimit: navigator.memory.jsHeapSizeLimit
      }
    }

    // Low-end device detection (heuristic)
    const isLowEndDevice = (
      navigator.hardwareConcurrency <= 2 ||
      (memoryInfo && memoryInfo.jsHeapSizeLimit < 1073741824) || // Less than 1GB
      deviceInfo.connectionType === 'slow-2g' ||
      deviceInfo.connectionType === '2g'
    )

    // Connection speed
    let connectionSpeed = 'unknown'
    if (navigator.connection) {
      const effectiveType = navigator.connection.effectiveType
      switch (effectiveType) {
        case 'slow-2g':
        case '2g':
          connectionSpeed = 'slow'
          break
        case '3g':
          connectionSpeed = 'medium'
          break
        case '4g':
          connectionSpeed = 'fast'
          break
        default:
          connectionSpeed = 'unknown'
      }
    }

    // Reduced motion preference
    const reducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches

    setPerformanceInfo({
      isLowEndDevice,
      memoryInfo,
      connectionSpeed,
      reducedMotion
    })
  }, [deviceInfo.connectionType])

  // Vibration function
  const vibrate = useCallback((pattern = 200) => {
    if (deviceInfo.supportsVibration && navigator.vibrate) {
      navigator.vibrate(pattern)
    }
  }, [deviceInfo.supportsVibration])

  // Haptic feedback for iOS
  const hapticFeedback = useCallback((type = 'light') => {
    if (deviceInfo.isIOS && window.DeviceMotionEvent) {
      // iOS haptic feedback through vibration patterns
      switch (type) {
        case 'light':
          vibrate(50)
          break
        case 'medium':
          vibrate(100)
          break
        case 'heavy':
          vibrate([100, 50, 100])
          break
        case 'success':
          vibrate([50, 50, 50])
          break
        case 'warning':
          vibrate([100, 100])
          break
        case 'error':
          vibrate([200, 100, 200])
          break
        default:
          vibrate(50)
      }
    } else {
      vibrate()
    }
  }, [deviceInfo.isIOS, vibrate])

  // Get optimal image quality based on device
  const getOptimalImageQuality = useCallback(() => {
    if (performanceInfo.isLowEndDevice || performanceInfo.connectionSpeed === 'slow') {
      return 'low'
    } else if (deviceInfo.pixelRatio >= 2 && performanceInfo.connectionSpeed === 'fast') {
      return 'high'
    } else {
      return 'medium'
    }
  }, [performanceInfo.isLowEndDevice, performanceInfo.connectionSpeed, deviceInfo.pixelRatio])

  // Get optimal animation settings
  const getOptimalAnimationSettings = useCallback(() => {
    return {
      enableAnimations: !performanceInfo.reducedMotion && !performanceInfo.isLowEndDevice,
      animationDuration: performanceInfo.isLowEndDevice ? 150 : 300,
      enableParallax: !performanceInfo.isLowEndDevice && performanceInfo.connectionSpeed !== 'slow',
      enableTransitions: !performanceInfo.reducedMotion
    }
  }, [performanceInfo.reducedMotion, performanceInfo.isLowEndDevice, performanceInfo.connectionSpeed])

  // Initialize and set up event listeners
  useEffect(() => {
    detectDevice()
    detectViewport()
    detectPerformance()

    const handleResize = () => {
      detectDevice()
      detectViewport()
    }

    const handleOrientationChange = () => {
      // Delay to allow for orientation change to complete
      setTimeout(() => {
        detectDevice()
        detectViewport()
      }, 100)
    }

    const handleConnectionChange = () => {
      detectDevice()
      detectPerformance()
    }

    window.addEventListener('resize', handleResize)
    window.addEventListener('orientationchange', handleOrientationChange)
    
    if (navigator.connection) {
      navigator.connection.addEventListener('change', handleConnectionChange)
    }

    return () => {
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('orientationchange', handleOrientationChange)
      
      if (navigator.connection) {
        navigator.connection.removeEventListener('change', handleConnectionChange)
      }
    }
  }, [detectDevice, detectViewport, detectPerformance])

  return {
    // Device information
    ...deviceInfo,
    
    // Viewport information
    viewport: viewportInfo,
    
    // Performance information
    performance: performanceInfo,
    
    // Utility functions
    vibrate,
    hapticFeedback,
    getOptimalImageQuality,
    getOptimalAnimationSettings,
    
    // Convenience flags
    isMobileDevice: deviceInfo.isMobile || deviceInfo.isTablet,
    isSmallScreen: deviceInfo.screenWidth <= 480,
    isLandscape: deviceInfo.orientation === 'landscape',
    isPortrait: deviceInfo.orientation === 'portrait',
    hasHighDPI: deviceInfo.pixelRatio >= 2,
    isSlowConnection: performanceInfo.connectionSpeed === 'slow',
    shouldReduceAnimations: performanceInfo.reducedMotion || performanceInfo.isLowEndDevice
  }
}
