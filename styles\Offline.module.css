/* Ocean Soul Sparkles - Offline Page Styles */

.container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  padding: 20px;
  box-sizing: border-box;
}

.content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  max-width: 500px;
  width: 100%;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}

.logoContainer {
  margin-bottom: 30px;
}

.logo {
  max-width: 120px;
  height: auto;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.statusIndicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-bottom: 30px;
  padding: 12px 20px;
  border-radius: 25px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.statusIndicator.offline {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 2px solid rgba(239, 68, 68, 0.2);
}

.statusIndicator.online {
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
  border: 2px solid rgba(34, 197, 94, 0.2);
}

.statusDot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.offline .statusDot {
  background: #dc2626;
}

.online .statusDot {
  background: #16a34a;
}

.statusText {
  font-size: 16px;
}

.messageContainer {
  margin-bottom: 40px;
}

.title {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 16px;
  line-height: 1.2;
}

.description {
  font-size: 16px;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 24px;
}

.troubleshooting {
  background: rgba(249, 250, 251, 0.8);
  border-radius: 12px;
  padding: 20px;
  text-align: left;
  margin-top: 20px;
}

.troubleshooting h3 {
  font-size: 18px;
  color: #374151;
  margin-bottom: 12px;
  font-weight: 600;
}

.troubleshooting ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.troubleshooting li {
  padding: 8px 0;
  color: #6b7280;
  position: relative;
  padding-left: 20px;
}

.troubleshooting li::before {
  content: '•';
  color: #4f46e5;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 30px;
}

.button {
  padding: 14px 28px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: 48px;
}

.primaryButton {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.primaryButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(79, 70, 229, 0.4);
}

.primaryButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.secondaryButton {
  background: rgba(107, 114, 128, 0.1);
  color: #374151;
  border: 2px solid rgba(107, 114, 128, 0.2);
}

.secondaryButton:hover {
  background: rgba(107, 114, 128, 0.2);
  transform: translateY(-1px);
}

.cacheNotice {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.cacheNotice p {
  margin: 0;
  font-size: 14px;
  color: #1e40af;
  line-height: 1.5;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  margin-top: 20px;
}

.loadingContainer p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(79, 70, 229, 0.2);
  border-top: 3px solid #4f46e5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Background decoration */
.backgroundDecoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.sparkle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: sparkle 3s infinite;
}

.sparkle:nth-child(1) {
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.sparkle:nth-child(2) {
  top: 60%;
  right: 30%;
  animation-delay: 1s;
}

.sparkle:nth-child(3) {
  bottom: 30%;
  left: 70%;
  animation-delay: 2s;
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes sparkle {
  0%, 100% {
    opacity: 0;
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .content {
    padding: 30px 20px;
  }
  
  .title {
    font-size: 24px;
  }
  
  .description {
    font-size: 15px;
  }
  
  .actions {
    gap: 10px;
  }
  
  .button {
    padding: 12px 24px;
    font-size: 15px;
  }
}

@media (max-width: 480px) {
  .content {
    padding: 24px 16px;
  }
  
  .title {
    font-size: 22px;
  }
  
  .logo {
    max-width: 100px;
  }
  
  .troubleshooting {
    padding: 16px;
  }
}
