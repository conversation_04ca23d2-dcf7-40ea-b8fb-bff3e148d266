/**
 * Business Management Status API Endpoint for Ocean Soul Sparkles
 * Provides business integration status and information
 * 
 * Phase 7.4: Business Management Integrations
 */

import { authenticateAdminRequest } from '@/lib/admin-auth'
import { integrationRateLimit } from '@/lib/integrations/rate-limiter'
import { RequestValida<PERSON>, AuditLogger } from '@/lib/integrations/security-utils'
import BusinessManager from '@/lib/integrations/business-manager'
import oauthManager from '@/lib/integrations/oauth-manager'

/**
 * Business Management Status Handler
 * GET /api/integrations/business/status - Get business integration status
 */
export default async function handler(req, res) {
  // Apply rate limiting
  await new Promise((resolve, reject) => {
    integrationRateLimit(req, res, (error) => {
      if (error) reject(error)
      else resolve()
    })
  })

  const startTime = Date.now()

  try {
    // Validate request method
    RequestValidator.validateEndpointAccess(req, ['GET'])

    // Authenticate user
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.success) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      })
    }

    const { user } = authResult
    const userId = user.id

    // Get integration status
    const integrationStatus = await oauthManager.getIntegrationStatus(userId)
    
    // Filter for business providers
    const businessProviders = ['quickbooks', 'mailchimp', 'xero', 'constant_contact']
    const businessIntegrations = integrationStatus.filter(integration => 
      businessProviders.includes(integration.provider)
    )

    // Get detailed business information
    const businessManager = new BusinessManager(userId)
    const connectedProviders = await businessManager.getConnectedProviders()

    // Test connections
    const connectionTests = await businessManager.testAllConnections()

    // Combine all information
    const integrations = businessIntegrations.map(integration => {
      const providerInfo = connectedProviders.find(p => p.provider === integration.provider)
      const connectionTest = connectionTests.find(t => t.provider === integration.provider)
      
      return {
        provider: integration.provider,
        name: getProviderName(integration.provider),
        type: getProviderType(integration.provider),
        connected: integration.connected,
        status: connectionTest?.success ? 'connected' : 'error',
        lastUpdated: integration.lastUpdated,
        needsRefresh: integration.needsRefresh,
        settings: providerInfo?.settings || {},
        companyInfo: connectionTest?.companyInfo || connectionTest?.account || null,
        error: connectionTest?.error || null,
        lastSync: integration.lastSync || null
      }
    })

    await AuditLogger.logApiAccess(
      userId,
      req.url,
      req.method,
      200,
      Date.now() - startTime,
      { action: 'get_business_status', integrationCount: integrations.length }
    )

    return res.status(200).json({
      success: true,
      integrations,
      availableProviders: getAvailableProviders()
    })

  } catch (error) {
    console.error('Business management status error:', error)

    await AuditLogger.logApiAccess(
      req.user?.id || null,
      req.url,
      req.method,
      500,
      Date.now() - startTime,
      { error: error.message }
    )

    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to get business management status'
    })
  }
}

/**
 * Get provider display name
 */
function getProviderName(provider) {
  const names = {
    quickbooks: 'QuickBooks',
    mailchimp: 'Mailchimp',
    xero: 'Xero',
    constant_contact: 'Constant Contact'
  }
  return names[provider] || provider
}

/**
 * Get provider type
 */
function getProviderType(provider) {
  const types = {
    quickbooks: 'accounting',
    xero: 'accounting',
    mailchimp: 'marketing',
    constant_contact: 'marketing'
  }
  return types[provider] || 'unknown'
}

/**
 * Get available business providers
 */
function getAvailableProviders() {
  return [
    {
      id: 'quickbooks',
      name: 'QuickBooks',
      type: 'accounting',
      description: 'Sync bookings to invoices and track expenses',
      features: ['Invoice generation', 'Expense tracking', 'Financial reports', 'Customer sync'],
      available: true
    },
    {
      id: 'mailchimp',
      name: 'Mailchimp',
      type: 'marketing',
      description: 'Manage customer email campaigns and segmentation',
      features: ['Customer segmentation', 'Email campaigns', 'Automation', 'Analytics'],
      available: true
    },
    {
      id: 'xero',
      name: 'Xero',
      type: 'accounting',
      description: 'Alternative accounting solution with invoicing',
      features: ['Invoice generation', 'Expense tracking', 'Financial reports'],
      available: false,
      comingSoon: true
    },
    {
      id: 'constant_contact',
      name: 'Constant Contact',
      type: 'marketing',
      description: 'Email marketing and customer engagement',
      features: ['Email campaigns', 'Contact management', 'Event marketing'],
      available: false,
      comingSoon: true
    }
  ]
}

/**
 * API Route Configuration
 */
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
}
