/**
 * AI Insights Dashboard Component
 * Displays automated business intelligence and recommendations for Ocean Soul Sparkles
 */

import { useState, useEffect } from 'react'
import { authenticatedFetch } from '@/lib/auth-utils'
import { useMobileOptimization } from '@/lib/hooks/useMobileOptimization'
import { toast } from 'react-toastify'
import styles from './AIInsightsDashboard.module.css'

export default function AIInsightsDashboard({ 
  className = '',
  autoRefresh = false,
  refreshInterval = 300000 // 5 minutes
}) {
  const [insights, setInsights] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0])
  const [insightType, setInsightType] = useState('daily')
  const [lastRefresh, setLastRefresh] = useState(null)
  
  const { isMobile, viewport } = useMobileOptimization()

  useEffect(() => {
    fetchInsights()
  }, [selectedDate, insightType])

  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      const interval = setInterval(fetchInsights, refreshInterval)
      return () => clearInterval(interval)
    }
  }, [autoRefresh, refreshInterval, selectedDate, insightType])

  /**
   * Fetch insights from the API
   */
  const fetchInsights = async () => {
    setLoading(true)
    setError(null)

    try {
      console.log('[AIInsightsDashboard] Fetching insights for:', { selectedDate, insightType })
      
      const result = await authenticatedFetch(
        `/api/ai/generate-insights?date=${selectedDate}&type=${insightType}`
      )
      
      console.log('[AIInsightsDashboard] Insights received:', result)
      setInsights(result.insights)
      setLastRefresh(new Date())
      
      if (result.insights.alerts?.some(alert => alert.severity === 'high')) {
        toast.warning('High priority alerts detected in business insights')
      }
    } catch (error) {
      console.error('[AIInsightsDashboard] Failed to fetch insights:', error)
      setError(error.message)
      toast.error(`Failed to load insights: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  /**
   * Get status indicator for performance
   */
  const getStatusIndicator = (indicator) => {
    const statusConfig = {
      excellent: { icon: '🟢', color: '#28a745' },
      good: { icon: '🔵', color: '#007bff' },
      fair: { icon: '🟡', color: '#ffc107' },
      poor: { icon: '🔴', color: '#dc3545' },
      unknown: { icon: '⚪', color: '#6c757d' }
    }
    
    return statusConfig[indicator?.status] || statusConfig.unknown
  }

  /**
   * Get alert severity styling
   */
  const getAlertSeverityClass = (severity) => {
    switch (severity) {
      case 'high': return styles.severityHigh
      case 'medium': return styles.severityMedium
      case 'low': return styles.severityLow
      default: return styles.severityMedium
    }
  }

  /**
   * Get recommendation priority styling
   */
  const getRecommendationPriorityClass = (priority) => {
    switch (priority) {
      case 'high': return styles.priorityHigh
      case 'medium': return styles.priorityMedium
      case 'low': return styles.priorityLow
      default: return styles.priorityMedium
    }
  }

  /**
   * Format currency for display
   */
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(amount || 0)
  }

  /**
   * Format percentage for display
   */
  const formatPercentage = (value) => {
    return `${(value || 0).toFixed(1)}%`
  }

  if (loading) {
    return (
      <div className={`${styles.loading} ${className}`}>
        <div className={styles.loadingSpinner}></div>
        <h3>🤖 AI is analyzing your data...</h3>
        <p>Generating insights and recommendations</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className={`${styles.error} ${className}`}>
        <div className={styles.errorIcon}>❌</div>
        <h3>Unable to Generate Insights</h3>
        <p>{error}</p>
        <button onClick={fetchInsights} className={styles.retryButton}>
          🔄 Try Again
        </button>
      </div>
    )
  }

  return (
    <div className={`${styles.aiDashboard} ${isMobile ? styles.mobile : ''} ${className}`}>
      {/* Header */}
      <div className={styles.header}>
        <h2 className={styles.title}>🤖 AI Business Insights</h2>
        <div className={styles.controls}>
          <div className={styles.dateSelector}>
            <input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              className={styles.dateInput}
            />
            <select
              value={insightType}
              onChange={(e) => setInsightType(e.target.value)}
              className={styles.typeSelector}
            >
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
            </select>
          </div>
          <button onClick={fetchInsights} className={styles.refreshButton}>
            🔄 Refresh
          </button>
        </div>
      </div>

      {insights && (
        <>
          {/* Executive Summary */}
          <div className={styles.summaryCard}>
            <div className={styles.summaryHeader}>
              <h3>📊 Executive Summary</h3>
              <div className={styles.performanceIndicator}>
                <span className={styles.statusIcon}>
                  {getStatusIndicator(insights.summary.performanceIndicator).icon}
                </span>
                <span className={styles.statusText}>
                  {insights.summary.performanceIndicator?.message || 'Status unknown'}
                </span>
              </div>
            </div>
            
            <div className={styles.summaryGrid}>
              <div className={styles.summaryMetric}>
                <span className={styles.metricValue}>{insights.summary.totalBookings || 0}</span>
                <span className={styles.metricLabel}>Total Bookings</span>
              </div>
              <div className={styles.summaryMetric}>
                <span className={styles.metricValue}>
                  {formatCurrency(insights.summary.totalRevenue)}
                </span>
                <span className={styles.metricLabel}>Revenue</span>
              </div>
              <div className={styles.summaryMetric}>
                <span className={styles.metricValue}>
                  {formatPercentage(insights.summary.completionRate)}
                </span>
                <span className={styles.metricLabel}>Completion Rate</span>
              </div>
              <div className={styles.summaryMetric}>
                <span className={`${styles.metricValue} ${
                  insights.summary.revenueGrowth > 0 ? styles.positive : 
                  insights.summary.revenueGrowth < 0 ? styles.negative : ''
                }`}>
                  {insights.summary.revenueGrowth > 0 ? '+' : ''}
                  {formatPercentage(insights.summary.revenueGrowth)}
                </span>
                <span className={styles.metricLabel}>Growth</span>
              </div>
            </div>
            
            <div className={styles.narrative}>
              <p>{insights.summary.narrative}</p>
            </div>
          </div>

          {/* Alerts */}
          {insights.alerts && insights.alerts.length > 0 && (
            <div className={styles.alertsCard}>
              <h3 className={styles.cardTitle}>🚨 Alerts & Anomalies</h3>
              <div className={styles.alertsList}>
                {insights.alerts.map((alert, index) => (
                  <div 
                    key={index} 
                    className={`${styles.alert} ${getAlertSeverityClass(alert.severity)}`}
                  >
                    <div className={styles.alertHeader}>
                      <span className={styles.alertType}>
                        {alert.type.toUpperCase()}
                      </span>
                      <span className={styles.alertCategory}>
                        {alert.category}
                      </span>
                      <span className={styles.alertSeverity}>
                        {alert.severity.toUpperCase()}
                      </span>
                    </div>
                    <p className={styles.alertMessage}>{alert.message}</p>
                    <p className={styles.alertRecommendation}>
                      💡 {alert.recommendation}
                    </p>
                    {alert.metrics && (
                      <div className={styles.alertMetrics}>
                        {Object.entries(alert.metrics).map(([key, value]) => (
                          <span key={key} className={styles.alertMetric}>
                            {key}: {value}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Recommendations */}
          {insights.recommendations && insights.recommendations.length > 0 && (
            <div className={styles.recommendationsCard}>
              <h3 className={styles.cardTitle}>💡 AI Recommendations</h3>
              <div className={styles.recommendationsList}>
                {insights.recommendations.map((rec, index) => (
                  <div 
                    key={index} 
                    className={`${styles.recommendation} ${getRecommendationPriorityClass(rec.priority)}`}
                  >
                    <div className={styles.recommendationHeader}>
                      <h4 className={styles.recommendationTitle}>{rec.title}</h4>
                      <div className={styles.recommendationMeta}>
                        <span className={styles.recommendationCategory}>
                          {rec.category}
                        </span>
                        <span className={styles.recommendationPriority}>
                          {rec.priority?.toUpperCase()}
                        </span>
                      </div>
                    </div>
                    <p className={styles.recommendationDescription}>
                      {rec.description}
                    </p>
                    <p className={styles.recommendationImpact}>
                      📈 {rec.impact}
                    </p>
                    {rec.actionItems && rec.actionItems.length > 0 && (
                      <div className={styles.actionItems}>
                        <h5>Action Items:</h5>
                        <ul>
                          {rec.actionItems.map((item, i) => (
                            <li key={i}>{item}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                    {rec.metrics && (
                      <div className={styles.recommendationMetrics}>
                        {Object.entries(rec.metrics).map(([key, value]) => (
                          <span key={key} className={styles.recommendationMetric}>
                            {key}: {value}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Booking Insights */}
          {insights.bookingInsights && (
            <div className={styles.bookingInsights}>
              <h3 className={styles.cardTitle}>📅 Booking Pattern Analysis</h3>
              <div className={styles.insightGrid}>
                {/* Peak Hours */}
                {insights.bookingInsights.peakHours && insights.bookingInsights.peakHours.length > 0 && (
                  <div className={styles.insightItem}>
                    <h4>⏰ Peak Hours</h4>
                    <div className={styles.peakHoursList}>
                      {insights.bookingInsights.peakHours.map((peak, index) => (
                        <div key={index} className={styles.peakHour}>
                          <span className={styles.peakTime}>{peak.time}</span>
                          <span className={styles.peakBookings}>
                            {peak.bookings} bookings ({peak.percentage}%)
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Popular Services */}
                {insights.bookingInsights.popularServices && insights.bookingInsights.popularServices.length > 0 && (
                  <div className={styles.insightItem}>
                    <h4>🎨 Popular Services</h4>
                    <div className={styles.servicesList}>
                      {insights.bookingInsights.popularServices.map((service, index) => (
                        <div key={index} className={styles.serviceItem}>
                          <div className={styles.serviceName}>{service.service}</div>
                          <div className={styles.serviceStats}>
                            <span>{service.count} bookings</span>
                            <span>{service.percentage}%</span>
                            {service.revenue && (
                              <span>{formatCurrency(service.revenue)}</span>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Customer Insights */}
                {insights.bookingInsights.customerInsights && (
                  <div className={styles.insightItem}>
                    <h4>👥 Customer Analysis</h4>
                    <div className={styles.customerStats}>
                      <div className={styles.customerStat}>
                        <span className={styles.statValue}>
                          {insights.bookingInsights.customerInsights.newCustomers || 0}
                        </span>
                        <span className={styles.statLabel}>New Customers</span>
                      </div>
                      <div className={styles.customerStat}>
                        <span className={styles.statValue}>
                          {insights.bookingInsights.customerInsights.returningCustomers || 0}
                        </span>
                        <span className={styles.statLabel}>Returning</span>
                      </div>
                      <div className={styles.customerStat}>
                        <span className={styles.statValue}>
                          {formatPercentage(insights.bookingInsights.customerInsights.newCustomerRate)}
                        </span>
                        <span className={styles.statLabel}>New Rate</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </>
      )}

      {/* Footer */}
      <div className={styles.footer}>
        <div className={styles.footerInfo}>
          <span>Generated by Ocean Soul Sparkles AI</span>
          {insights?.generatedAt && (
            <span>
              • {new Date(insights.generatedAt).toLocaleString('en-AU')}
            </span>
          )}
          {lastRefresh && (
            <span>
              • Last refresh: {lastRefresh.toLocaleTimeString('en-AU')}
            </span>
          )}
        </div>
        {insights?.metadata?.confidenceLevel && (
          <div className={styles.confidenceLevel}>
            <span>Confidence: {formatPercentage(insights.metadata.confidenceLevel * 100)}</span>
          </div>
        )}
      </div>
    </div>
  )
}

/**
 * Component Props:
 * 
 * @param {string} className - Additional CSS classes
 * @param {boolean} autoRefresh - Whether to automatically refresh insights (default: false)
 * @param {number} refreshInterval - Auto-refresh interval in milliseconds (default: 300000 = 5 minutes)
 * 
 * Usage Examples:
 * 
 * // Basic usage
 * <AIInsightsDashboard />
 * 
 * // With auto-refresh every 5 minutes
 * <AIInsightsDashboard autoRefresh={true} refreshInterval={300000} />
 * 
 * // Custom styling
 * <AIInsightsDashboard className="custom-insights-dashboard" />
 * 
 * Features:
 * - Real-time business intelligence generation
 * - Executive summary with key metrics
 * - Automated alerts and anomaly detection
 * - Actionable recommendations with priority levels
 * - Booking pattern analysis
 * - Mobile-responsive design
 * - Auto-refresh capability
 * - Multiple insight types (daily, weekly, monthly)
 * - Confidence level indicators
 * - Error handling and retry functionality
 */
