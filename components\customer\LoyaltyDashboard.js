/**
 * Loyalty Dashboard Component - Phase 8: Advanced Customer Experience
 * Ocean Soul Sparkles - Customer Loyalty Program
 */

import { useState, useEffect } from 'react'
import { useCustomer } from '@/contexts/CustomerContext'
import { useMobileOptimization } from '@/lib/hooks/useMobileOptimization'
import { toast } from 'react-toastify'
import styles from '@/styles/customer/LoyaltyDashboard.module.css'

export default function LoyaltyDashboard({ loyaltyInfo, customer, onLoyaltyUpdate }) {
  const { isMobile, viewport } = useMobileOptimization()
  
  const [rewards, setRewards] = useState([])
  const [transactions, setTransactions] = useState([])
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    if (loyaltyInfo) {
      loadRewardsAndTransactions()
    }
  }, [loyaltyInfo])

  const loadRewardsAndTransactions = async () => {
    try {
      setLoading(true)
      
      const [rewardsRes, transactionsRes] = await Promise.all([
        fetch('/api/customer/loyalty/rewards'),
        fetch('/api/customer/loyalty/transactions?limit=10')
      ])

      const [rewardsData, transactionsData] = await Promise.all([
        rewardsRes.json(),
        transactionsRes.json()
      ])

      if (rewardsData.success) {
        setRewards(rewardsData.data || [])
      }

      if (transactionsData.success) {
        setTransactions(transactionsData.data || [])
      }

    } catch (error) {
      console.error('Error loading loyalty data:', error)
      toast.error('Failed to load loyalty information')
    } finally {
      setLoading(false)
    }
  }

  const handleRedeemReward = async (rewardId) => {
    try {
      const response = await fetch('/api/customer/loyalty/redeem', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ reward_id: rewardId })
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Reward redeemed successfully!')
        loadRewardsAndTransactions()
        if (onLoyaltyUpdate) {
          onLoyaltyUpdate()
        }
      } else {
        throw new Error(data.error || 'Failed to redeem reward')
      }
    } catch (error) {
      console.error('Error redeeming reward:', error)
      toast.error('Failed to redeem reward')
    }
  }

  const getTierInfo = (tier) => {
    const tiers = {
      bronze: { name: 'Bronze', color: '#CD7F32', nextTier: 'Silver', pointsNeeded: 500 },
      silver: { name: 'Silver', color: '#C0C0C0', nextTier: 'Gold', pointsNeeded: 1000 },
      gold: { name: 'Gold', color: '#FFD700', nextTier: 'Platinum', pointsNeeded: 2500 },
      platinum: { name: 'Platinum', color: '#E5E4E2', nextTier: 'Diamond', pointsNeeded: 5000 },
      diamond: { name: 'Diamond', color: '#B9F2FF', nextTier: null, pointsNeeded: null }
    }
    return tiers[tier] || tiers.bronze
  }

  const calculateProgress = () => {
    if (!loyaltyInfo) return 0
    
    const tierInfo = getTierInfo(loyaltyInfo.tier_level)
    if (!tierInfo.pointsNeeded) return 100 // Diamond tier
    
    const progress = (loyaltyInfo.lifetime_points / tierInfo.pointsNeeded) * 100
    return Math.min(progress, 100)
  }

  const renderOverview = () => {
    if (!loyaltyInfo) {
      return (
        <div className={styles.noLoyalty}>
          <h3>Join Our Loyalty Program</h3>
          <p>Start earning points with your first booking!</p>
          <button className={styles.joinButton}>
            Learn More
          </button>
        </div>
      )
    }

    const tierInfo = getTierInfo(loyaltyInfo.tier_level)
    const progress = calculateProgress()

    return (
      <div className={styles.overview}>
        {/* Points Balance */}
        <div className={styles.pointsCard}>
          <div className={styles.pointsHeader}>
            <h3>Your Points</h3>
            <div className={styles.pointsBalance}>
              {loyaltyInfo.points_balance.toLocaleString()}
            </div>
          </div>
          <p className={styles.pointsSubtext}>
            Available to redeem
          </p>
        </div>

        {/* Tier Status */}
        <div className={styles.tierCard}>
          <div className={styles.tierHeader}>
            <h3>Tier Status</h3>
            <div 
              className={styles.tierBadge}
              style={{ backgroundColor: tierInfo.color }}
            >
              {tierInfo.name}
            </div>
          </div>
          
          {tierInfo.nextTier && (
            <div className={styles.tierProgress}>
              <div className={styles.progressBar}>
                <div 
                  className={styles.progressFill}
                  style={{ width: `${progress}%` }}
                />
              </div>
              <p className={styles.progressText}>
                {loyaltyInfo.next_tier_points || (tierInfo.pointsNeeded - loyaltyInfo.lifetime_points)} points to {tierInfo.nextTier}
              </p>
            </div>
          )}
        </div>

        {/* Quick Stats */}
        <div className={styles.statsGrid}>
          <div className={styles.statCard}>
            <div className={styles.statValue}>
              {loyaltyInfo.lifetime_points.toLocaleString()}
            </div>
            <div className={styles.statLabel}>Lifetime Points</div>
          </div>
          
          <div className={styles.statCard}>
            <div className={styles.statValue}>
              {loyaltyInfo.referral_count || 0}
            </div>
            <div className={styles.statLabel}>Referrals</div>
          </div>
          
          <div className={styles.statCard}>
            <div className={styles.statValue}>
              {new Date(loyaltyInfo.anniversary_date).getFullYear()}
            </div>
            <div className={styles.statLabel}>Member Since</div>
          </div>
        </div>
      </div>
    )
  }

  const renderRewards = () => (
    <div className={styles.rewards}>
      <h3>Available Rewards</h3>
      
      {rewards.length === 0 ? (
        <div className={styles.noRewards}>
          <p>No rewards available at the moment.</p>
        </div>
      ) : (
        <div className={styles.rewardsGrid}>
          {rewards.map(reward => {
            const canRedeem = loyaltyInfo && loyaltyInfo.points_balance >= reward.points_required
            const tierRequirementMet = !reward.tier_requirement || 
              (loyaltyInfo && getTierInfo(loyaltyInfo.tier_level).name.toLowerCase() >= reward.tier_requirement)

            return (
              <div 
                key={reward.id} 
                className={`${styles.rewardCard} ${canRedeem && tierRequirementMet ? styles.available : styles.unavailable}`}
              >
                <div className={styles.rewardHeader}>
                  <h4 className={styles.rewardName}>{reward.reward_name}</h4>
                  <div className={styles.rewardPoints}>
                    {reward.points_required} pts
                  </div>
                </div>
                
                <p className={styles.rewardDescription}>
                  {reward.reward_description}
                </p>
                
                <div className={styles.rewardMeta}>
                  <span className={styles.rewardType}>
                    {reward.reward_type.replace('_', ' ').toUpperCase()}
                  </span>
                  {reward.tier_requirement && (
                    <span className={styles.tierRequirement}>
                      {reward.tier_requirement.toUpperCase()}+ only
                    </span>
                  )}
                </div>
                
                <button
                  className={styles.redeemButton}
                  disabled={!canRedeem || !tierRequirementMet}
                  onClick={() => handleRedeemReward(reward.id)}
                >
                  {!tierRequirementMet ? 'Tier Required' : 
                   !canRedeem ? 'Insufficient Points' : 'Redeem'}
                </button>
              </div>
            )
          })}
        </div>
      )}
    </div>
  )

  const renderTransactions = () => (
    <div className={styles.transactions}>
      <h3>Points History</h3>
      
      {transactions.length === 0 ? (
        <div className={styles.noTransactions}>
          <p>No point transactions yet.</p>
        </div>
      ) : (
        <div className={styles.transactionsList}>
          {transactions.map(transaction => (
            <div key={transaction.id} className={styles.transactionItem}>
              <div className={styles.transactionInfo}>
                <div className={styles.transactionDescription}>
                  {transaction.description}
                </div>
                <div className={styles.transactionDate}>
                  {new Date(transaction.created_at).toLocaleDateString()}
                </div>
              </div>
              
              <div className={styles.transactionAmount}>
                <span className={`${styles.pointsChange} ${
                  transaction.transaction_type === 'earned' || transaction.transaction_type === 'bonus' 
                    ? styles.positive : styles.negative
                }`}>
                  {transaction.transaction_type === 'earned' || transaction.transaction_type === 'bonus' ? '+' : '-'}
                  {Math.abs(transaction.points_amount)}
                </span>
                <div className={styles.transactionType}>
                  {transaction.transaction_type}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )

  return (
    <div className={styles.loyaltyDashboard}>
      {/* Tab Navigation */}
      <div className={styles.tabNavigation}>
        <button
          className={`${styles.tab} ${activeTab === 'overview' ? styles.active : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          Overview
        </button>
        <button
          className={`${styles.tab} ${activeTab === 'rewards' ? styles.active : ''}`}
          onClick={() => setActiveTab('rewards')}
        >
          Rewards
        </button>
        <button
          className={`${styles.tab} ${activeTab === 'history' ? styles.active : ''}`}
          onClick={() => setActiveTab('history')}
        >
          History
        </button>
      </div>

      {/* Tab Content */}
      <div className={styles.tabContent}>
        {loading ? (
          <div className={styles.loading}>
            <div className={styles.spinner}></div>
            <p>Loading loyalty information...</p>
          </div>
        ) : (
          <>
            {activeTab === 'overview' && renderOverview()}
            {activeTab === 'rewards' && renderRewards()}
            {activeTab === 'history' && renderTransactions()}
          </>
        )}
      </div>
    </div>
  )
}
