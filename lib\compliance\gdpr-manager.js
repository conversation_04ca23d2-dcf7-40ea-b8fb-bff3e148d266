/**
 * GDPR Compliance Manager for Ocean Soul Sparkles
 * Handles data subject rights, consent management, and privacy compliance
 * 
 * Phase 9.2: Data Protection & Privacy Compliance
 */

import { createClient } from '@supabase/supabase-js'
import crypto from 'crypto'
import { encrypt, decrypt } from '@/lib/encryption'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

/**
 * GDPR Manager Class
 * Handles all GDPR compliance operations
 */
export class GDPRManager {
  constructor() {
    this.requestDeadlineDays = 30 // GDPR requires response within 30 days
    this.verificationTokenExpiry = 24 * 60 * 60 * 1000 // 24 hours
    this.dataRetentionPeriods = {
      customer_data: '7 years',
      booking_data: '7 years',
      payment_data: '7 years',
      marketing_data: '2 years',
      analytics_data: '2 years',
      session_data: '30 days',
      audit_logs: '7 years'
    }
  }

  /**
   * Create a new GDPR request
   * @param {Object} requestData - Request details
   * @returns {Promise<Object>} - Created request
   */
  async createGDPRRequest(requestData) {
    try {
      const {
        requestType,
        requesterEmail,
        requesterName,
        userId = null,
        customerId = null,
        requestDetails = {}
      } = requestData

      // Generate verification token
      const verificationToken = crypto.randomBytes(32).toString('hex')
      const verificationExpiresAt = new Date(Date.now() + this.verificationTokenExpiry)
      const completionDeadline = new Date(Date.now() + (this.requestDeadlineDays * 24 * 60 * 60 * 1000))

      const { data: request, error } = await supabase
        .from('gdpr_requests')
        .insert({
          user_id: userId,
          customer_id: customerId,
          request_type: requestType,
          requester_email: requesterEmail,
          requester_name: requesterName,
          verification_token: verificationToken,
          verification_expires_at: verificationExpiresAt.toISOString(),
          completion_deadline: completionDeadline.toISOString(),
          request_details: requestDetails
        })
        .select()
        .single()

      if (error) {
        throw new Error(`Failed to create GDPR request: ${error.message}`)
      }

      // Send verification email
      await this.sendVerificationEmail(requesterEmail, verificationToken, requestType)

      // Log the request creation
      await this.logDataAccess(userId, 'gdpr_requests', request.id, 'create', 'confidential', 
        ['request_type', 'requester_email'], 'GDPR request creation', 'consent')

      return {
        success: true,
        requestId: request.id,
        verificationRequired: true,
        deadline: completionDeadline
      }
    } catch (error) {
      console.error('Error creating GDPR request:', error)
      throw error
    }
  }

  /**
   * Verify GDPR request with token
   * @param {string} token - Verification token
   * @returns {Promise<Object>} - Verification result
   */
  async verifyGDPRRequest(token) {
    try {
      const { data: request, error } = await supabase
        .from('gdpr_requests')
        .select('*')
        .eq('verification_token', token)
        .eq('verification_status', 'pending')
        .single()

      if (error || !request) {
        return { success: false, error: 'Invalid or expired verification token' }
      }

      // Check if token is expired
      if (new Date(request.verification_expires_at) < new Date()) {
        return { success: false, error: 'Verification token has expired' }
      }

      // Update verification status
      const { error: updateError } = await supabase
        .from('gdpr_requests')
        .update({
          verification_status: 'verified',
          verification_token: null, // Clear token after verification
          updated_at: new Date().toISOString()
        })
        .eq('id', request.id)

      if (updateError) {
        throw new Error(`Failed to verify request: ${updateError.message}`)
      }

      // Start processing the request
      await this.processGDPRRequest(request.id)

      return {
        success: true,
        requestId: request.id,
        requestType: request.request_type
      }
    } catch (error) {
      console.error('Error verifying GDPR request:', error)
      throw error
    }
  }

  /**
   * Process a verified GDPR request
   * @param {string} requestId - Request ID
   * @returns {Promise<Object>} - Processing result
   */
  async processGDPRRequest(requestId) {
    try {
      const { data: request, error } = await supabase
        .from('gdpr_requests')
        .select('*')
        .eq('id', requestId)
        .single()

      if (error || !request) {
        throw new Error('Request not found')
      }

      // Update status to in_progress
      await supabase
        .from('gdpr_requests')
        .update({
          request_status: 'in_progress',
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId)

      let responseData = {}

      switch (request.request_type) {
        case 'access':
          responseData = await this.handleDataAccessRequest(request)
          break
        case 'portability':
          responseData = await this.handleDataPortabilityRequest(request)
          break
        case 'rectification':
          responseData = await this.handleDataRectificationRequest(request)
          break
        case 'erasure':
          responseData = await this.handleDataErasureRequest(request)
          break
        case 'restriction':
          responseData = await this.handleDataRestrictionRequest(request)
          break
        case 'objection':
          responseData = await this.handleDataObjectionRequest(request)
          break
        default:
          throw new Error(`Unknown request type: ${request.request_type}`)
      }

      // Update request with response data
      await supabase
        .from('gdpr_requests')
        .update({
          request_status: 'completed',
          response_data: responseData,
          processed_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId)

      return { success: true, responseData }
    } catch (error) {
      console.error('Error processing GDPR request:', error)
      
      // Update request status to failed
      await supabase
        .from('gdpr_requests')
        .update({
          request_status: 'rejected',
          notes: `Processing failed: ${error.message}`,
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId)

      throw error
    }
  }

  /**
   * Handle data access request (Article 15)
   * @param {Object} request - GDPR request
   * @returns {Promise<Object>} - User data
   */
  async handleDataAccessRequest(request) {
    try {
      const userData = {}

      // Collect user data from various tables
      if (request.user_id) {
        // User account data
        const { data: user } = await supabase.auth.admin.getUserById(request.user_id)
        userData.account = {
          id: user.user?.id,
          email: user.user?.email,
          created_at: user.user?.created_at,
          last_sign_in_at: user.user?.last_sign_in_at
        }

        // User roles
        const { data: roles } = await supabase
          .from('user_roles')
          .select('*')
          .eq('id', request.user_id)
        userData.roles = roles

        // MFA settings (without secrets)
        const { data: mfaSettings } = await supabase
          .from('user_mfa_settings')
          .select('mfa_enabled, mfa_enforced, created_at, updated_at')
          .eq('user_id', request.user_id)
        userData.mfa_settings = mfaSettings

        // Sessions
        const { data: sessions } = await supabase
          .from('user_sessions')
          .select('ip_address, user_agent, location_country, created_at, last_activity')
          .eq('user_id', request.user_id)
        userData.sessions = sessions

        // Security events
        const { data: securityEvents } = await supabase
          .from('security_events')
          .select('event_type, event_description, severity, created_at')
          .eq('user_id', request.user_id)
        userData.security_events = securityEvents
      }

      if (request.customer_id) {
        // Customer data
        const { data: customer } = await supabase
          .from('customers')
          .select('*')
          .eq('id', request.customer_id)
          .single()
        userData.customer_profile = customer

        // Bookings
        const { data: bookings } = await supabase
          .from('bookings')
          .select('*')
          .eq('customer_id', request.customer_id)
        userData.bookings = bookings

        // Customer preferences
        const { data: preferences } = await supabase
          .from('customer_preferences')
          .select('*')
          .eq('customer_id', request.customer_id)
        userData.preferences = preferences

        // Consents
        const { data: consents } = await supabase
          .from('user_consents')
          .select('*')
          .eq('customer_id', request.customer_id)
        userData.consents = consents

        // Privacy preferences
        const { data: privacyPrefs } = await supabase
          .from('privacy_preferences')
          .select('*')
          .eq('customer_id', request.customer_id)
        userData.privacy_preferences = privacyPrefs
      }

      // Log data access
      await this.logDataAccess(request.user_id, 'multiple_tables', null, 'export', 'confidential',
        ['personal_data'], 'GDPR data access request', 'consent')

      return {
        request_id: request.id,
        data_exported_at: new Date().toISOString(),
        data: userData
      }
    } catch (error) {
      console.error('Error handling data access request:', error)
      throw error
    }
  }

  /**
   * Handle data portability request (Article 20)
   * @param {Object} request - GDPR request
   * @returns {Promise<Object>} - Portable data
   */
  async handleDataPortabilityRequest(request) {
    try {
      // Get all user data in a structured, machine-readable format
      const portableData = await this.handleDataAccessRequest(request)
      
      // Format data for portability (JSON structure)
      const structuredData = {
        format: 'JSON',
        version: '1.0',
        exported_at: new Date().toISOString(),
        data_subject: {
          email: request.requester_email,
          name: request.requester_name
        },
        data: portableData.data
      }

      return {
        portable_data: structuredData,
        format: 'application/json',
        filename: `ocean-soul-sparkles-data-export-${Date.now()}.json`
      }
    } catch (error) {
      console.error('Error handling data portability request:', error)
      throw error
    }
  }

  /**
   * Handle data erasure request (Article 17 - Right to be forgotten)
   * @param {Object} request - GDPR request
   * @returns {Promise<Object>} - Erasure result
   */
  async handleDataErasureRequest(request) {
    try {
      const deletedData = []

      if (request.customer_id) {
        // Anonymize customer data (preserve for business records)
        const { error: customerError } = await supabase
          .from('customers')
          .update({
            name: 'Anonymized User',
            email: `anonymized-${Date.now()}@deleted.oceansoulsparkles.com.au`,
            phone: null,
            address: null,
            city: null,
            state: null,
            postal_code: null,
            notes: 'Data deleted per GDPR request',
            marketing_consent: false,
            updated_at: new Date().toISOString()
          })
          .eq('id', request.customer_id)

        if (!customerError) {
          deletedData.push('customer_profile')
        }

        // Delete customer preferences
        await supabase
          .from('customer_preferences')
          .delete()
          .eq('customer_id', request.customer_id)
        deletedData.push('customer_preferences')

        // Delete privacy preferences
        await supabase
          .from('privacy_preferences')
          .delete()
          .eq('customer_id', request.customer_id)
        deletedData.push('privacy_preferences')

        // Delete consents
        await supabase
          .from('user_consents')
          .delete()
          .eq('customer_id', request.customer_id)
        deletedData.push('user_consents')
      }

      if (request.user_id) {
        // Delete user account (if no business records exist)
        const { error: userError } = await supabase.auth.admin.deleteUser(request.user_id)
        if (!userError) {
          deletedData.push('user_account')
        }

        // Delete MFA settings
        await supabase
          .from('user_mfa_settings')
          .delete()
          .eq('user_id', request.user_id)
        deletedData.push('mfa_settings')

        // Delete sessions
        await supabase
          .from('user_sessions')
          .delete()
          .eq('user_id', request.user_id)
        deletedData.push('user_sessions')
      }

      // Log data retention
      await this.logDataRetention('multiple_tables', request.customer_id || request.user_id,
        'gdpr_erasure', 'GDPR right to be forgotten request', 'hard_delete')

      return {
        deleted_data_types: deletedData,
        deletion_completed_at: new Date().toISOString(),
        retention_note: 'Some business records may be retained for legal compliance'
      }
    } catch (error) {
      console.error('Error handling data erasure request:', error)
      throw error
    }
  }

  /**
   * Handle data rectification request (Article 16)
   * @param {Object} request - GDPR request
   * @returns {Promise<Object>} - Rectification result
   */
  async handleDataRectificationRequest(request) {
    try {
      const corrections = request.request_details.corrections || {}
      const correctedFields = []

      if (request.customer_id && corrections.customer) {
        const { error } = await supabase
          .from('customers')
          .update({
            ...corrections.customer,
            updated_at: new Date().toISOString()
          })
          .eq('id', request.customer_id)

        if (!error) {
          correctedFields.push(...Object.keys(corrections.customer))
        }
      }

      return {
        corrected_fields: correctedFields,
        correction_completed_at: new Date().toISOString()
      }
    } catch (error) {
      console.error('Error handling data rectification request:', error)
      throw error
    }
  }

  /**
   * Handle data restriction request (Article 18)
   * @param {Object} request - GDPR request
   * @returns {Promise<Object>} - Restriction result
   */
  async handleDataRestrictionRequest(request) {
    try {
      // Mark data as restricted (add restriction flag)
      const restrictedData = []

      if (request.customer_id) {
        const { error } = await supabase
          .from('customers')
          .update({
            notes: (await supabase.from('customers').select('notes').eq('id', request.customer_id).single()).data?.notes + 
                   '\n[GDPR RESTRICTION: Processing restricted per user request]',
            updated_at: new Date().toISOString()
          })
          .eq('id', request.customer_id)

        if (!error) {
          restrictedData.push('customer_data')
        }
      }

      return {
        restricted_data_types: restrictedData,
        restriction_applied_at: new Date().toISOString(),
        restriction_note: 'Data processing restricted as requested'
      }
    } catch (error) {
      console.error('Error handling data restriction request:', error)
      throw error
    }
  }

  /**
   * Handle data objection request (Article 21)
   * @param {Object} request - GDPR request
   * @returns {Promise<Object>} - Objection result
   */
  async handleDataObjectionRequest(request) {
    try {
      // Stop processing for marketing/analytics purposes
      const stoppedProcessing = []

      if (request.customer_id) {
        // Withdraw marketing consent
        await supabase
          .from('user_consents')
          .update({
            consent_given: false,
            withdrawn_at: new Date().toISOString(),
            withdrawal_reason: 'GDPR objection request'
          })
          .eq('customer_id', request.customer_id)
          .eq('consent_type', 'marketing')

        stoppedProcessing.push('marketing_processing')

        // Update customer marketing consent
        await supabase
          .from('customers')
          .update({
            marketing_consent: false,
            updated_at: new Date().toISOString()
          })
          .eq('id', request.customer_id)
      }

      return {
        stopped_processing_types: stoppedProcessing,
        objection_processed_at: new Date().toISOString()
      }
    } catch (error) {
      console.error('Error handling data objection request:', error)
      throw error
    }
  }

  /**
   * Send verification email for GDPR request
   * @param {string} email - Email address
   * @param {string} token - Verification token
   * @param {string} requestType - Type of request
   */
  async sendVerificationEmail(email, token, requestType) {
    try {
      // TODO: Implement email sending
      // For now, log the verification link
      const verificationLink = `${process.env.NEXT_PUBLIC_SITE_URL}/gdpr/verify?token=${token}`
      console.log(`GDPR ${requestType} verification link for ${email}: ${verificationLink}`)
      
      // In production, send actual email using your email service
    } catch (error) {
      console.error('Error sending verification email:', error)
    }
  }

  /**
   * Log data access for audit trail
   * @param {string} userId - User ID
   * @param {string} tableName - Table name
   * @param {string} recordId - Record ID
   * @param {string} accessType - Access type
   * @param {string} classification - Data classification
   * @param {Array} fields - Fields accessed
   * @param {string} purpose - Purpose of access
   * @param {string} legalBasis - Legal basis
   */
  async logDataAccess(userId, tableName, recordId, accessType, classification, fields, purpose, legalBasis) {
    try {
      await supabase
        .from('data_access_logs')
        .insert({
          user_id: userId,
          table_name: tableName,
          record_id: recordId,
          access_type: accessType,
          data_classification: classification,
          fields_accessed: fields,
          purpose,
          legal_basis: legalBasis
        })
    } catch (error) {
      console.error('Error logging data access:', error)
    }
  }

  /**
   * Log data retention action
   * @param {string} tableName - Table name
   * @param {string} recordId - Record ID
   * @param {string} policy - Retention policy
   * @param {string} reason - Deletion reason
   * @param {string} method - Deletion method
   */
  async logDataRetention(tableName, recordId, policy, reason, method) {
    try {
      await supabase
        .from('data_retention_logs')
        .insert({
          table_name: tableName,
          record_id: recordId,
          retention_policy: policy,
          retention_period: this.dataRetentionPeriods[tableName] || '7 years',
          deletion_executed_at: new Date().toISOString(),
          deletion_method: method,
          deletion_reason: reason
        })
    } catch (error) {
      console.error('Error logging data retention:', error)
    }
  }

  /**
   * Get GDPR request status
   * @param {string} requestId - Request ID
   * @returns {Promise<Object>} - Request status
   */
  async getRequestStatus(requestId) {
    try {
      const { data: request, error } = await supabase
        .from('gdpr_requests')
        .select('*')
        .eq('id', requestId)
        .single()

      if (error) {
        throw error
      }

      return request
    } catch (error) {
      console.error('Error getting request status:', error)
      throw error
    }
  }
}

// Export singleton instance
export const gdprManager = new GDPRManager()
export default gdprManager
