import { supabase } from '@/lib/supabase'; // Corrected import path
import { authenticateAdminRequest } from '@/lib/admin-auth'; // Assuming this path is correct

// Helper function to get dates for the current week (Monday to Sunday)
const getCurrentWeekDates = () => {
  const today = new Date();
  const currentDay = today.getDay(); // 0 (Sunday) - 6 (Saturday)

  // Adjust diffToMonday based on whether Sunday is 0 (Sunday) or 6 (Saturday)
  // Assuming getDay() returns 0 for Sunday, 1 for Monday, ..., 6 for Saturday.
  // We want Monday as the start of the week.
  const diffToMonday = currentDay === 0 ? -6 : 1 - currentDay;

  const monday = new Date(today);
  monday.setDate(today.getDate() + diffToMonday);
  monday.setHours(0, 0, 0, 0); // Normalize to start of day

  const weekDates = [];
  for (let i = 0; i < 7; i++) {
    const dayInWeek = new Date(monday);
    dayInWeek.setDate(monday.getDate() + i);
    weekDates.push(dayInWeek.toISOString().split('T')[0]); // Format as YYYY-MM-DD
  }
  return weekDates;
};

export default async function handler(req, res) {
  if (req.method !== 'PUT') {
    res.setHeader('Allow', ['PUT']);
    return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }

  const authResult = await authenticateAdminRequest(req, res);
  if (!authResult.authenticated) {
    // authenticateAdminRequest already sends a response on failure
    return;
  }

  const { user, role } = authResult; // Corrected destructuring

  if (role !== 'artist' && role !== 'braider') {
    return res.status(403).json({ error: 'Forbidden: User must be an artist or braider.' });
  }

  try {
    // SECURITY ENHANCEMENT: Check rate limits (max 10 requests per minute)
    try {
      await supabase.rpc('check_api_rate_limit', {
        endpoint_path: '/api/artist/availability/set-unavailable-week',
        max_requests: 10,
        window_minutes: 1
      });
    } catch (rateLimitError) {
      console.warn(`Rate limit exceeded for user ${user.id}:`, rateLimitError.message);
      return res.status(429).json({
        error: 'Rate limit exceeded',
        message: 'Too many requests. Please wait before trying again.',
        retryAfter: 60 // seconds
      });
    }
    // Fetch the artist_profiles.id using user.id (from auth.users.id)
    const { data: artistProfile, error: profileError } = await supabase
      .from('artist_profiles')
      .select('id') // We only need the PK of artist_profiles
      .eq('user_id', user.id) // user.id is from auth.users
      .single();

    if (profileError) {
      console.error('Error fetching artist profile:', profileError);
      return res.status(500).json({ error: 'Error fetching artist profile.', details: profileError.message });
    }
    if (!artistProfile) {
      return res.status(404).json({ error: 'Artist profile not found.' });
    }
    const artistProfileId = artistProfile.id;

    const weekDates = getCurrentWeekDates();

    // SECURITY ENHANCEMENT: Validate all dates before processing
    for (const date of weekDates) {
      try {
        await supabase.rpc('validate_availability_exception_date', {
          exception_date: date,
          artist_user_id: user.id
        });
      } catch (validationError) {
        console.warn(`Date validation failed for ${date}:`, validationError.message);
        return res.status(400).json({
          error: 'Invalid date range',
          message: `Cannot set availability for ${date}: ${validationError.message}`,
          date: date
        });
      }
    }

    const exceptionsToUpsert = weekDates.map(date => ({
      artist_id: artistProfileId,
      exception_date: date,
      exception_type: 'Unavailable',
      start_time: null,
      end_time: null,
      notes: 'Marked unavailable for the week via dashboard quick action.',
    }));

    // Upsert the exceptions
    // Conflict on (artist_id, exception_date) means we update if it exists
    const { data, error: upsertError } = await supabase
      .from('artist_availability_exceptions')
      .upsert(exceptionsToUpsert, {
        onConflict: 'artist_id, exception_date',
        // ignoreDuplicates: false by default, so it will update conflicting rows.
      })
      .select(); // Optionally select the upserted data if needed for response

    if (upsertError) {
      console.error('Error upserting availability exceptions:', upsertError);
      return res.status(500).json({ error: 'Failed to set week as unavailable.', details: upsertError.message });
    }

    // SECURITY ENHANCEMENT: Log the availability change for audit trail
    try {
      await supabase.rpc('log_artist_availability_change', {
        p_artist_id: artistProfileId,
        p_action_type: 'bulk_update',
        p_table_name: 'artist_availability_exceptions',
        p_record_id: null, // Multiple records
        p_old_values: null,
        p_new_values: { exceptions: exceptionsToUpsert },
        p_change_description: `Set unavailable for week: ${weekDates[0]} to ${weekDates[weekDates.length - 1]}`,
        p_api_endpoint: '/api/artist/availability/set-unavailable-week'
      });
    } catch (auditError) {
      // Don't fail the request if audit logging fails, but log the error
      console.error('Failed to log availability change:', auditError);
    }

    return res.status(200).json({
      message: 'Successfully marked unavailable for the current week.',
      data,
      weekDates: weekDates,
      affectedDates: data?.length || weekDates.length
    });

  } catch (error) {
    console.error('Unexpected error in set-unavailable-week:', error);
    return res.status(500).json({ error: 'An unexpected error occurred.', details: error.message });
  }
}
