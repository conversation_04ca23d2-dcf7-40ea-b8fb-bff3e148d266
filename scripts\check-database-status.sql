-- Quick Database Status Check for Phase 7 Integrations
-- Ocean Soul Sparkles - Run this in Supabase SQL Editor

-- =====================================================
-- CHECK INTEGRATION TABLES EXIST
-- =====================================================

SELECT 
  'Integration Tables Status' as check_type,
  table_name,
  CASE 
    WHEN table_name IS NOT NULL THEN '✅ EXISTS'
    ELSE '❌ MISSING'
  END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_name IN (
    'integration_credentials',
    'integration_logs', 
    'integration_settings',
    'integration_sync_status',
    'security_logs',
    'api_access_logs',
    'rate_limit_requests'
  )
ORDER BY table_name;

-- =====================================================
-- CHECK INTEGRATION COLUMNS IN EXISTING TABLES
-- =====================================================

-- Check bookings table integration columns
SELECT 
  'Bookings Integration Columns' as check_type,
  column_name,
  data_type,
  CASE 
    WHEN column_name IS NOT NULL THEN '✅ EXISTS'
    ELSE '❌ MISSING'
  END as status
FROM information_schema.columns 
WHERE table_schema = 'public' 
  AND table_name = 'bookings'
  AND column_name IN (
    'quickbooks_customer_id',
    'quickbooks_invoice_id',
    'accounting_sync_data',
    'accounting_sync_status',
    'calendar_event_id',
    'calendar_sync_data',
    'calendar_sync_status'
  )
ORDER BY column_name;

-- Check customers table integration columns
SELECT 
  'Customers Integration Columns' as check_type,
  column_name,
  data_type,
  CASE 
    WHEN column_name IS NOT NULL THEN '✅ EXISTS'
    ELSE '❌ MISSING'
  END as status
FROM information_schema.columns 
WHERE table_schema = 'public' 
  AND table_name = 'customers'
  AND column_name IN (
    'quickbooks_customer_id',
    'mailchimp_subscriber_id',
    'marketing_sync_data',
    'marketing_sync_status',
    'customer_segment',
    'customer_tags',
    'email_subscription_status'
  )
ORDER BY column_name;

-- Check portfolio_items table integration columns
SELECT 
  'Portfolio Integration Columns' as check_type,
  column_name,
  data_type,
  CASE 
    WHEN column_name IS NOT NULL THEN '✅ EXISTS'
    ELSE '❌ MISSING'
  END as status
FROM information_schema.columns 
WHERE table_schema = 'public' 
  AND table_name = 'portfolio_items'
  AND column_name IN (
    'social_sync_data',
    'social_sync_status',
    'instagram_post_id',
    'facebook_post_id',
    'social_media_urls'
  )
ORDER BY column_name;

-- Check services table integration columns
SELECT 
  'Services Integration Columns' as check_type,
  column_name,
  data_type,
  CASE 
    WHEN column_name IS NOT NULL THEN '✅ EXISTS'
    ELSE '❌ MISSING'
  END as status
FROM information_schema.columns 
WHERE table_schema = 'public' 
  AND table_name = 'services'
  AND column_name IN (
    'quickbooks_item_id',
    'accounting_sync_data',
    'accounting_sync_status'
  )
ORDER BY column_name;

-- =====================================================
-- CHECK RLS POLICIES
-- =====================================================

SELECT 
  'RLS Policies Status' as check_type,
  schemaname,
  tablename,
  CASE 
    WHEN rowsecurity THEN '✅ ENABLED'
    ELSE '❌ DISABLED'
  END as rls_status,
  (
    SELECT COUNT(*) 
    FROM pg_policies 
    WHERE schemaname = 'public' 
      AND tablename = pg_tables.tablename
  ) as policy_count
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename IN (
    'integration_credentials',
    'integration_logs',
    'integration_settings', 
    'integration_sync_status',
    'security_logs',
    'api_access_logs',
    'rate_limit_requests',
    'bookings',
    'customers',
    'portfolio_items',
    'services'
  )
ORDER BY tablename;

-- =====================================================
-- CHECK INDEXES FOR PERFORMANCE
-- =====================================================

SELECT 
  'Integration Indexes Status' as check_type,
  indexname,
  tablename,
  CASE 
    WHEN indexname IS NOT NULL THEN '✅ EXISTS'
    ELSE '❌ MISSING'
  END as status
FROM pg_indexes 
WHERE schemaname = 'public' 
  AND indexname LIKE '%quickbooks%' 
   OR indexname LIKE '%mailchimp%'
   OR indexname LIKE '%calendar%'
   OR indexname LIKE '%social%'
   OR indexname LIKE '%integration%'
ORDER BY tablename, indexname;

-- =====================================================
-- SUMMARY REPORT
-- =====================================================

SELECT 
  'PHASE 7 DATABASE STATUS SUMMARY' as summary_type,
  (
    SELECT COUNT(*) 
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
      AND table_name IN (
        'integration_credentials',
        'integration_logs',
        'integration_settings',
        'integration_sync_status',
        'security_logs',
        'api_access_logs',
        'rate_limit_requests'
      )
  ) as integration_tables_count,
  (
    SELECT COUNT(*) 
    FROM information_schema.columns 
    WHERE table_schema = 'public' 
      AND table_name = 'bookings'
      AND column_name IN (
        'quickbooks_customer_id',
        'accounting_sync_data',
        'calendar_event_id',
        'calendar_sync_data'
      )
  ) as booking_integration_columns,
  (
    SELECT COUNT(*) 
    FROM information_schema.columns 
    WHERE table_schema = 'public' 
      AND table_name = 'customers'
      AND column_name IN (
        'quickbooks_customer_id',
        'mailchimp_subscriber_id',
        'marketing_sync_data',
        'customer_segment'
      )
  ) as customer_integration_columns,
  CASE 
    WHEN (
      SELECT COUNT(*) 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
        AND table_name IN (
          'integration_credentials',
          'integration_logs',
          'integration_settings'
        )
    ) >= 3 THEN '✅ READY FOR PHASE 7'
    ELSE '❌ MIGRATION REQUIRED'
  END as overall_status;
