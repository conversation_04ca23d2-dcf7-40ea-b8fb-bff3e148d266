import { supabase } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth'; // Using this as a placeholder for artist auth

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }

  // Authenticate the request
  // This should ideally be a more specific authenticator for artists,
  // but authenticateAdminRequest might provide user/role for now.
  const authResult = await authenticateAdminRequest(req);

  if (!authResult.authorized) {
    return res.status(401).json({ error: authResult.error?.message || 'Unauthorized' });
  }

  const { user, role } = authResult;

  // Ensure user is an artist or braider
  if (role !== 'artist' && role !== 'braider') {
    return res.status(403).json({ error: 'Forbidden: Access restricted to artists and braiders.' });
  }

  const userId = user.id;

  try {
    // Check if an artist_profile record exists, as is_profile_complete is on that table.
    const { data: existingProfile, error: fetchError } = await supabase
      .from('artist_profiles')
      .select('user_id')
      .eq('user_id', userId)
      .maybeSingle();

    if (fetchError) {
      console.error('Error fetching artist profile before update:', fetchError);
      return res.status(500).json({ error: 'Failed to verify artist profile existence.', details: fetchError.message });
    }

    if (!existingProfile) {
      // This case should ideally not happen if the onboarding flow ensures a profile is created.
      // However, if it does, we might create a minimal profile here or return an error.
      // For now, let's assume a profile should exist from the approval workflow.
      // If not, creating one here with just user_id and is_profile_complete = true might be an option.
      // For this iteration, let's return an error if no profile exists, as the field is on this table.
      console.warn(`No artist_profile found for user ${userId} when trying to update onboarding status.`);
      return res.status(404).json({ error: 'Artist profile not found. Cannot update onboarding status.' });
    }

    // Update the is_profile_complete field
    const { data: updatedProfile, error: updateError } = await supabase
      .from('artist_profiles')
      .update({
        is_profile_complete: true,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .select('is_profile_complete, updated_at') // Select the fields that were updated
      .single();

    if (updateError) {
      console.error('Error updating onboarding status:', updateError);
      return res.status(500).json({ error: 'Failed to update onboarding status.', details: updateError.message });
    }

    if (!updatedProfile) {
      // Should not happen if existingProfile was found and update didn't error, but as a safeguard:
      return res.status(404).json({ error: 'Failed to update: Profile not found after update attempt.' });
    }

    res.status(200).json({
      success: true,
      message: 'Onboarding status updated successfully.',
      is_profile_complete: updatedProfile.is_profile_complete,
      updated_at: updatedProfile.updated_at
    });

  } catch (error) {
    console.error('Unexpected error updating onboarding status:', error);
    res.status(500).json({ error: 'Internal server error', details: error.message });
  }
}
