/**
 * Create Phase 9.2 Data Protection & Privacy Compliance Tables
 * Ocean Soul Sparkles - Security & Compliance Enhancement
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'
import path from 'path'
import { fileURLToPath } from 'url'

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Load environment variables
dotenv.config({ path: path.join(__dirname, '..', '.env.local') })

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

async function createTables() {
  console.log('🚀 Creating Phase 9.2 Data Protection & Privacy Compliance Tables...')
  
  const tables = [
    {
      name: 'gdpr_requests',
      sql: `
        CREATE TABLE IF NOT EXISTS public.gdpr_requests (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
          customer_id UUID REFERENCES public.customers(id) ON DELETE SET NULL,
          request_type VARCHAR(50) NOT NULL CHECK (request_type IN (
            'access', 'portability', 'rectification', 'erasure', 'restriction', 'objection'
          )),
          request_status VARCHAR(30) NOT NULL CHECK (request_status IN (
            'pending', 'in_progress', 'completed', 'rejected', 'cancelled'
          )) DEFAULT 'pending',
          requester_email TEXT NOT NULL,
          requester_name TEXT,
          verification_method VARCHAR(50),
          verification_status VARCHAR(30) DEFAULT 'pending' CHECK (verification_status IN (
            'pending', 'verified', 'failed', 'expired'
          )),
          verification_token TEXT UNIQUE,
          verification_expires_at TIMESTAMP WITH TIME ZONE,
          request_details JSONB DEFAULT '{}',
          response_data JSONB DEFAULT '{}',
          processed_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
          processed_at TIMESTAMP WITH TIME ZONE,
          completion_deadline TIMESTAMP WITH TIME ZONE,
          notes TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    },
    {
      name: 'user_consents',
      sql: `
        CREATE TABLE IF NOT EXISTS public.user_consents (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
          customer_id UUID REFERENCES public.customers(id) ON DELETE CASCADE,
          consent_type VARCHAR(50) NOT NULL,
          consent_given BOOLEAN NOT NULL,
          consent_version VARCHAR(20) NOT NULL,
          consent_method VARCHAR(50),
          consent_source VARCHAR(100),
          ip_address INET,
          user_agent TEXT,
          consent_data JSONB DEFAULT '{}',
          withdrawn_at TIMESTAMP WITH TIME ZONE,
          withdrawal_reason TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    },
    {
      name: 'privacy_preferences',
      sql: `
        CREATE TABLE IF NOT EXISTS public.privacy_preferences (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
          customer_id UUID REFERENCES public.customers(id) ON DELETE CASCADE,
          preference_category VARCHAR(50) NOT NULL,
          preference_key VARCHAR(100) NOT NULL,
          preference_value JSONB NOT NULL,
          is_active BOOLEAN DEFAULT TRUE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    },
    {
      name: 'cookie_consents',
      sql: `
        CREATE TABLE IF NOT EXISTS public.cookie_consents (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          session_id TEXT,
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
          customer_id UUID REFERENCES public.customers(id) ON DELETE CASCADE,
          essential_cookies BOOLEAN DEFAULT TRUE,
          functional_cookies BOOLEAN DEFAULT FALSE,
          analytics_cookies BOOLEAN DEFAULT FALSE,
          marketing_cookies BOOLEAN DEFAULT FALSE,
          consent_version VARCHAR(20) NOT NULL,
          ip_address INET,
          user_agent TEXT,
          consent_banner_shown BOOLEAN DEFAULT FALSE,
          consent_given_at TIMESTAMP WITH TIME ZONE,
          last_updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          expires_at TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    },
    {
      name: 'data_access_logs',
      sql: `
        CREATE TABLE IF NOT EXISTS public.data_access_logs (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
          customer_id UUID REFERENCES public.customers(id) ON DELETE SET NULL,
          table_name VARCHAR(100) NOT NULL,
          record_id UUID,
          access_type VARCHAR(50) NOT NULL,
          data_classification VARCHAR(50),
          fields_accessed TEXT[],
          query_type VARCHAR(50),
          ip_address INET,
          user_agent TEXT,
          session_id TEXT,
          api_endpoint TEXT,
          request_method VARCHAR(10),
          response_status INTEGER,
          processing_time_ms INTEGER,
          data_volume_bytes INTEGER,
          purpose VARCHAR(200),
          legal_basis VARCHAR(100),
          retention_period INTERVAL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    },
    {
      name: 'data_modification_logs',
      sql: `
        CREATE TABLE IF NOT EXISTS public.data_modification_logs (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
          table_name VARCHAR(100) NOT NULL,
          record_id UUID NOT NULL,
          operation VARCHAR(20) NOT NULL CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')),
          old_values JSONB,
          new_values JSONB,
          changed_fields TEXT[],
          change_reason VARCHAR(500),
          ip_address INET,
          user_agent TEXT,
          session_id TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    },
    {
      name: 'data_retention_logs',
      sql: `
        CREATE TABLE IF NOT EXISTS public.data_retention_logs (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          table_name VARCHAR(100) NOT NULL,
          record_id UUID,
          retention_policy VARCHAR(100) NOT NULL,
          retention_period INTERVAL NOT NULL,
          deletion_scheduled_at TIMESTAMP WITH TIME ZONE,
          deletion_executed_at TIMESTAMP WITH TIME ZONE,
          deletion_method VARCHAR(50),
          deletion_reason VARCHAR(200),
          data_backup_location TEXT,
          executed_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    },
    {
      name: 'encryption_metadata',
      sql: `
        CREATE TABLE IF NOT EXISTS public.encryption_metadata (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          table_name VARCHAR(100) NOT NULL,
          field_name VARCHAR(100) NOT NULL,
          encryption_algorithm VARCHAR(50) NOT NULL,
          key_version VARCHAR(20) NOT NULL,
          encryption_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          last_rotation_date TIMESTAMP WITH TIME ZONE,
          next_rotation_due TIMESTAMP WITH TIME ZONE,
          data_classification VARCHAR(50) NOT NULL,
          compliance_requirements TEXT[],
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(table_name, field_name)
        );
      `
    }
  ]

  let successCount = 0
  let errorCount = 0

  for (const table of tables) {
    try {
      console.log(`📋 Creating table: ${table.name}`)
      
      const { error } = await supabase.rpc('execute_sql', {
        sql: table.sql
      })
      
      if (error) {
        console.error(`❌ Error creating ${table.name}:`, error.message)
        errorCount++
      } else {
        console.log(`✅ Table ${table.name} created successfully`)
        successCount++
      }
    } catch (err) {
      console.error(`❌ Exception creating ${table.name}:`, err.message)
      errorCount++
    }
  }

  console.log('\n📊 Table Creation Summary:')
  console.log(`✅ Successful tables: ${successCount}`)
  console.log(`❌ Failed tables: ${errorCount}`)

  if (errorCount === 0) {
    console.log('🎉 All Phase 9.2 tables created successfully!')
  } else {
    console.log('⚠️  Some tables failed to create. Please review the logs.')
  }

  // Verify tables
  console.log('\n🔍 Verifying tables...')
  await verifyTables()
}

async function verifyTables() {
  const tableNames = [
    'gdpr_requests',
    'user_consents',
    'privacy_preferences',
    'cookie_consents',
    'data_access_logs',
    'data_modification_logs',
    'data_retention_logs',
    'encryption_metadata'
  ]

  for (const tableName of tableNames) {
    try {
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1)
      
      if (error) {
        console.log(`❌ Table ${tableName}: ${error.message}`)
      } else {
        console.log(`✅ Table ${tableName}: Verified successfully`)
      }
    } catch (err) {
      console.log(`❌ Table ${tableName}: ${err.message}`)
    }
  }
}

// Run the table creation
createTables()
