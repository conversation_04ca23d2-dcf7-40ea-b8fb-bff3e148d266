import { useState, useEffect, useMemo } from 'react';
import { Line, Bar, Radar } from 'react-chartjs-2';
import { authenticatedFetch } from '@/lib/auth-utils';
import { DynamicPricingEngine, MarketAnalysisEngine, PriceElasticityCalculator } from '@/lib/analytics/pricing-optimization';
import styles from '@/styles/admin/analytics/DynamicPricingEngine.module.css';

/**
 * Dynamic Pricing Engine Component
 * Provides intelligent pricing optimization and recommendations
 */
export default function DynamicPricingEngineComponent() {
  const [pricingData, setPricingData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedService, setSelectedService] = useState('all');
  const [pricingStrategy, setPricingStrategy] = useState('balanced');
  const [simulationMode, setSimulationMode] = useState(false);

  const pricingEngine = useMemo(() => new DynamicPricingEngine(), []);
  const marketAnalyzer = useMemo(() => new MarketAnalysisEngine(), []);
  const elasticityCalculator = useMemo(() => new PriceElasticityCalculator(), []);

  useEffect(() => {
    loadPricingData();
  }, [selectedService, pricingStrategy]);

  const loadPricingData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await authenticatedFetch(`/api/analytics/pricing-recommendations?service=${selectedService}&strategy=${pricingStrategy}`);
      
      if (response.success) {
        setPricingData(response.data);
      } else {
        throw new Error(response.error || 'Failed to load pricing data');
      }
    } catch (err) {
      console.error('Error loading pricing data:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const generatePriceOptimizationChart = () => {
    if (!pricingData?.priceOptimization) return null;

    const optimization = pricingData.priceOptimization;
    const pricePoints = optimization.pricePoints || [];
    const revenues = optimization.expectedRevenues || [];
    const demands = optimization.expectedDemands || [];

    return {
      labels: pricePoints.map(price => `$${price}`),
      datasets: [
        {
          label: 'Expected Revenue',
          data: revenues,
          borderColor: 'rgb(59, 130, 246)',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          yAxisID: 'y',
          tension: 0.4
        },
        {
          label: 'Expected Demand',
          data: demands,
          borderColor: 'rgb(16, 185, 129)',
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          yAxisID: 'y1',
          tension: 0.4
        }
      ]
    };
  };

  const generateCompetitorAnalysisChart = () => {
    if (!pricingData?.competitorAnalysis) return null;

    const competitors = pricingData.competitorAnalysis.competitors || [];
    const services = [...new Set(competitors.map(c => c.service))];

    const datasets = services.map((service, index) => {
      const serviceCompetitors = competitors.filter(c => c.service === service);
      const colors = [
        'rgba(239, 68, 68, 0.8)',
        'rgba(245, 158, 11, 0.8)',
        'rgba(16, 185, 129, 0.8)',
        'rgba(59, 130, 246, 0.8)',
        'rgba(139, 92, 246, 0.8)'
      ];

      return {
        label: service,
        data: serviceCompetitors.map(c => c.price),
        backgroundColor: colors[index % colors.length],
        borderColor: colors[index % colors.length].replace('0.8', '1'),
        borderWidth: 1
      };
    });

    return {
      labels: competitors.map(c => c.name),
      datasets
    };
  };

  const generateElasticityChart = () => {
    if (!pricingData?.elasticityAnalysis) return null;

    const elasticity = pricingData.elasticityAnalysis;
    const priceChanges = elasticity.priceHistory || [];
    const demandChanges = elasticity.demandHistory || [];

    return {
      datasets: [
        {
          label: 'Price vs Demand Elasticity',
          data: priceChanges.map((price, i) => ({
            x: price,
            y: demandChanges[i] || 0
          })),
          backgroundColor: 'rgba(236, 72, 153, 0.6)',
          borderColor: 'rgb(236, 72, 153)',
          borderWidth: 2,
          pointRadius: 6
        }
      ]
    };
  };

  const generateMarketPositionChart = () => {
    if (!pricingData?.marketPosition) return null;

    const position = pricingData.marketPosition;
    const metrics = {
      'Price Competitiveness': position.priceCompetitiveness || 50,
      'Quality Rating': position.qualityRating || 50,
      'Market Share': position.marketShare || 50,
      'Customer Satisfaction': position.customerSatisfaction || 50,
      'Brand Recognition': position.brandRecognition || 50,
      'Service Availability': position.serviceAvailability || 50
    };

    return {
      labels: Object.keys(metrics),
      datasets: [
        {
          label: 'Market Position',
          data: Object.values(metrics),
          backgroundColor: 'rgba(59, 130, 246, 0.2)',
          borderColor: 'rgb(59, 130, 246)',
          borderWidth: 2,
          pointBackgroundColor: 'rgb(59, 130, 246)',
          pointBorderColor: '#fff',
          pointHoverBackgroundColor: '#fff',
          pointHoverBorderColor: 'rgb(59, 130, 246)'
        }
      ]
    };
  };

  const handlePriceSimulation = async (serviceId, newPrice) => {
    try {
      const response = await authenticatedFetch('/api/analytics/price-simulation', {
        method: 'POST',
        body: JSON.stringify({
          serviceId,
          newPrice,
          strategy: pricingStrategy
        })
      });

      if (response.success) {
        // Update pricing data with simulation results
        setPricingData(prev => ({
          ...prev,
          simulation: response.data
        }));
      }
    } catch (error) {
      console.error('Price simulation error:', error);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(amount);
  };

  const formatPercentage = (value) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Pricing Analytics'
      }
    },
    scales: {
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        title: {
          display: true,
          text: 'Revenue ($)'
        }
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        title: {
          display: true,
          text: 'Demand (bookings)'
        },
        grid: {
          drawOnChartArea: false,
        },
      }
    }
  };

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.spinner}></div>
        <p>Analyzing pricing data...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.errorContainer}>
        <h3>Error Loading Pricing Data</h3>
        <p>{error}</p>
        <button onClick={loadPricingData} className={styles.retryButton}>
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className={styles.pricingEngine}>
      <div className={styles.header}>
        <h2>Dynamic Pricing Engine</h2>
        <div className={styles.controls}>
          <select
            value={selectedService}
            onChange={(e) => setSelectedService(e.target.value)}
            className={styles.serviceSelect}
          >
            <option value="all">All Services</option>
            {pricingData?.services?.map(service => (
              <option key={service.id} value={service.id}>
                {service.name}
              </option>
            ))}
          </select>
          
          <select
            value={pricingStrategy}
            onChange={(e) => setPricingStrategy(e.target.value)}
            className={styles.strategySelect}
          >
            <option value="revenue_max">Revenue Maximization</option>
            <option value="market_share">Market Share Growth</option>
            <option value="balanced">Balanced Approach</option>
            <option value="premium">Premium Positioning</option>
          </select>

          <button
            onClick={() => setSimulationMode(!simulationMode)}
            className={`${styles.simulationButton} ${simulationMode ? styles.active : ''}`}
          >
            {simulationMode ? 'Exit Simulation' : 'Price Simulation'}
          </button>
        </div>
      </div>

      {/* Pricing Recommendations */}
      <div className={styles.recommendationsGrid}>
        {pricingData?.recommendations?.map((rec, index) => (
          <div key={index} className={`${styles.recommendationCard} ${styles[rec.priority]}`}>
            <h3>{rec.service}</h3>
            <div className={styles.currentPrice}>
              Current: {formatCurrency(rec.currentPrice)}
            </div>
            <div className={styles.recommendedPrice}>
              Recommended: {formatCurrency(rec.recommendedPrice)}
            </div>
            <div className={styles.priceChange}>
              Change: {rec.priceChange > 0 ? '+' : ''}{formatPercentage(rec.priceChange)}
            </div>
            <div className={styles.expectedImpact}>
              Expected Revenue Impact: {formatPercentage(rec.expectedImpact)}
            </div>
            <div className={styles.confidence}>
              Confidence: {formatPercentage(rec.confidence)}
            </div>
            {simulationMode && (
              <button
                onClick={() => handlePriceSimulation(rec.serviceId, rec.recommendedPrice)}
                className={styles.simulateButton}
              >
                Simulate
              </button>
            )}
          </div>
        ))}
      </div>

      {/* Charts Grid */}
      <div className={styles.chartsGrid}>
        {/* Price Optimization Chart */}
        <div className={styles.chartCard}>
          <h3>Price Optimization Analysis</h3>
          <div className={styles.chartContainer}>
            {generatePriceOptimizationChart() && (
              <Line data={generatePriceOptimizationChart()} options={chartOptions} />
            )}
          </div>
        </div>

        {/* Competitor Analysis */}
        <div className={styles.chartCard}>
          <h3>Competitor Pricing Analysis</h3>
          <div className={styles.chartContainer}>
            {generateCompetitorAnalysisChart() && (
              <Bar 
                data={generateCompetitorAnalysisChart()} 
                options={{
                  ...chartOptions,
                  scales: {
                    y: {
                      title: {
                        display: true,
                        text: 'Price ($)'
                      }
                    }
                  }
                }}
              />
            )}
          </div>
        </div>

        {/* Market Position Radar */}
        <div className={styles.chartCard}>
          <h3>Market Position Analysis</h3>
          <div className={styles.chartContainer}>
            {generateMarketPositionChart() && (
              <Radar 
                data={generateMarketPositionChart()} 
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  scales: {
                    r: {
                      beginAtZero: true,
                      max: 100
                    }
                  }
                }}
              />
            )}
          </div>
        </div>

        {/* Price Elasticity */}
        <div className={styles.chartCard}>
          <h3>Price Elasticity Analysis</h3>
          <div className={styles.chartContainer}>
            {generateElasticityChart() && (
              <Line 
                data={generateElasticityChart()} 
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'top',
                    }
                  },
                  scales: {
                    x: {
                      title: {
                        display: true,
                        text: 'Price Change (%)'
                      }
                    },
                    y: {
                      title: {
                        display: true,
                        text: 'Demand Change (%)'
                      }
                    }
                  }
                }}
              />
            )}
          </div>
        </div>
      </div>

      {/* Pricing Insights */}
      <div className={styles.insightsSection}>
        <h3>Pricing Insights & Market Intelligence</h3>
        <div className={styles.insightsGrid}>
          {pricingData?.insights?.map((insight, index) => (
            <div key={index} className={`${styles.insightCard} ${styles[insight.type]}`}>
              <h4>{insight.title}</h4>
              <p>{insight.description}</p>
              {insight.recommendation && (
                <div className={styles.recommendation}>
                  <strong>Recommendation:</strong> {insight.recommendation}
                </div>
              )}
              {insight.impact && (
                <div className={styles.impact}>
                  <strong>Expected Impact:</strong> {insight.impact}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Elasticity Summary */}
      {pricingData?.elasticityAnalysis && (
        <div className={styles.elasticitySection}>
          <h3>Price Elasticity Summary</h3>
          <div className={styles.elasticityCards}>
            <div className={styles.elasticityCard}>
              <h4>Elasticity Coefficient</h4>
              <div className={styles.elasticityValue}>
                {pricingData.elasticityAnalysis.elasticity?.toFixed(2) || 'N/A'}
              </div>
              <div className={styles.elasticityType}>
                {pricingData.elasticityAnalysis.interpretation?.type || 'Unknown'}
              </div>
            </div>
            
            <div className={styles.elasticityCard}>
              <h4>Price Sensitivity</h4>
              <div className={styles.elasticityValue}>
                {Math.abs(pricingData.elasticityAnalysis.elasticity || 0) > 1 ? 'High' : 'Low'}
              </div>
              <div className={styles.elasticityType}>
                {pricingData.elasticityAnalysis.interpretation?.description || ''}
              </div>
            </div>
            
            <div className={styles.elasticityCard}>
              <h4>Optimal Strategy</h4>
              <div className={styles.elasticityValue}>
                {pricingData.elasticityAnalysis.interpretation?.recommendation?.split(' ')[0] || 'Maintain'}
              </div>
              <div className={styles.elasticityType}>
                {pricingData.elasticityAnalysis.interpretation?.recommendation || 'Current pricing'}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
