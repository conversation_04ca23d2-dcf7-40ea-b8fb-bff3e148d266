/* Customer Layout Styles - Phase 8: Advanced Customer Experience */

.layout {
  display: flex;
  min-height: 100vh;
  background: #f8f9fa;
}

/* Mobile Header */
.mobileHeader {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: white;
  border-bottom: 1px solid #e9ecef;
  z-index: 1000;
  padding: 0 1rem;
  align-items: center;
  justify-content: space-between;
}

.menuButton {
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hamburger {
  width: 24px;
  height: 2px;
  background: #333;
  position: relative;
  transition: all 0.3s ease;
}

.hamburger::before,
.hamburger::after {
  content: '';
  position: absolute;
  width: 24px;
  height: 2px;
  background: #333;
  transition: all 0.3s ease;
}

.hamburger::before {
  top: -8px;
}

.hamburger::after {
  top: 8px;
}

.logoLink {
  text-decoration: none;
}

.logo {
  height: auto;
  max-height: 40px;
}

.headerActions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.notificationButton {
  position: relative;
  padding: 0.5rem;
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.messageIcon {
  font-size: 1.5rem;
}

.notificationBadge {
  position: absolute;
  top: 0;
  right: 0;
  background: #ff4757;
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.2rem 0.4rem;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
}

/* Sidebar */
.sidebar {
  width: 280px;
  background: white;
  border-right: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  overflow-y: auto;
  z-index: 999;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.sidebarOpen {
  transform: translateX(0);
}

.sidebarHeader {
  padding: 1.5rem;
  border-bottom: 1px solid #e9ecef;
}

.customerInfo {
  padding: 1.5rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.customerAvatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.avatarImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatarPlaceholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 600;
}

.customerDetails {
  flex: 1;
  min-width: 0;
}

.customerName {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.customerEmail {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Navigation */
.navigation {
  flex: 1;
  padding: 1rem 0;
}

.navList {
  list-style: none;
  margin: 0;
  padding: 0;
}

.navItem {
  margin: 0;
}

.navLink {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 1.5rem;
  text-decoration: none;
  color: #666;
  transition: all 0.3s ease;
  position: relative;
}

.navLink:hover {
  background: #f8f9fa;
  color: #4ECDC4;
}

.navLink.active {
  background: #4ECDC4;
  color: white;
}

.navLink.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: #44A08D;
}

.navIcon {
  font-size: 1.2rem;
  width: 24px;
  text-align: center;
}

.navLabel {
  font-weight: 500;
  flex: 1;
}

.navBadge {
  background: #ff4757;
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

.navLink.active .navBadge {
  background: rgba(255, 255, 255, 0.3);
}

/* Quick Actions */
.quickActions {
  padding: 1rem;
  border-top: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.quickActionButton {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border: none;
  border-radius: 8px;
  text-decoration: none;
  color: #666;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quickActionButton:hover {
  background: #4ECDC4;
  color: white;
}

.quickActionIcon {
  font-size: 1.1rem;
}

.quickActionLabel {
  flex: 1;
  text-align: left;
}

/* Sidebar Footer */
.sidebarFooter {
  padding: 1rem;
  border-top: 1px solid #e9ecef;
}

.signOutButton {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem;
  background: none;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  color: #666;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.signOutButton:hover {
  background: #fee;
  border-color: #fcc;
  color: #c33;
}

.signOutIcon {
  font-size: 1.1rem;
}

.signOutLabel {
  flex: 1;
  text-align: left;
}

/* Sidebar Overlay */
.sidebarOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 998;
}

/* Main Content */
.main {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-left: 280px;
  min-height: 100vh;
}

.content {
  flex: 1;
  padding: 2rem;
  overflow-x: hidden;
}

/* Bottom Navigation */
.bottomNavigation {
  display: none;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 70px;
  background: white;
  border-top: 1px solid #e9ecef;
  z-index: 1000;
  padding: 0.5rem;
  justify-content: space-around;
  align-items: center;
}

.bottomNavItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem;
  text-decoration: none;
  color: #666;
  font-size: 0.8rem;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
  min-width: 60px;
}

.bottomNavItem:hover,
.bottomNavItem.active {
  color: #4ECDC4;
  background: rgba(78, 205, 196, 0.1);
}

.bottomNavIcon {
  font-size: 1.2rem;
}

.bottomNavLabel {
  font-size: 0.7rem;
  text-align: center;
}

.bottomNavBadge {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  background: #ff4757;
  color: white;
  font-size: 0.6rem;
  font-weight: 600;
  padding: 0.15rem 0.3rem;
  border-radius: 8px;
  min-width: 14px;
  text-align: center;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .mobileHeader {
    display: flex;
  }

  .sidebar {
    width: 100%;
    max-width: 320px;
  }

  .main {
    margin-left: 0;
    padding-top: 60px;
    padding-bottom: 70px;
  }

  .content {
    padding: 1rem;
  }

  .bottomNavigation {
    display: flex;
  }
}

@media (max-width: 480px) {
  .content {
    padding: 0.5rem;
  }

  .customerInfo {
    padding: 1rem;
  }

  .customerName {
    font-size: 1rem;
  }

  .customerEmail {
    font-size: 0.8rem;
  }

  .navLink {
    padding: 0.75rem 1rem;
  }

  .quickActions {
    padding: 0.75rem;
  }

  .sidebarFooter {
    padding: 0.75rem;
  }
}

/* Desktop Responsive */
@media (min-width: 769px) {
  .sidebar {
    position: static;
    transform: none;
  }

  .main {
    margin-left: 280px;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .layout {
    background: #1a1a1a;
  }

  .mobileHeader,
  .sidebar,
  .bottomNavigation {
    background: #2d2d2d;
    border-color: #444;
  }

  .customerName {
    color: #fff;
  }

  .customerEmail,
  .navLink,
  .quickActionButton,
  .signOutButton,
  .bottomNavItem {
    color: #ccc;
  }

  .navLink:hover,
  .quickActionButton:hover {
    background: #444;
    color: #4ECDC4;
  }

  .navLink.active {
    background: #4ECDC4;
    color: white;
  }

  .signOutButton:hover {
    background: #441;
    border-color: #663;
    color: #faa;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .sidebar,
  .navLink,
  .quickActionButton,
  .signOutButton,
  .bottomNavItem {
    transition: none;
  }
}
