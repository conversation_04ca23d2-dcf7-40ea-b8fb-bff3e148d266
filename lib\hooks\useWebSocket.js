/**
 * WebSocket Hook - Phase 8: Advanced Customer Experience
 * Ocean Soul Sparkles - Real-time Communication Hook
 */

import { useState, useEffect, useRef, useCallback } from 'react'

export function useWebSocket(url, options = {}) {
  const [socket, setSocket] = useState(null)
  const [lastMessage, setLastMessage] = useState(null)
  const [readyState, setReadyState] = useState(0) // 0: CONNECTING, 1: OPEN, 2: CLOSING, 3: CLOSED
  const [connectionStatus, setConnectionStatus] = useState('Disconnected')
  
  const reconnectTimeoutRef = useRef(null)
  const reconnectAttemptsRef = useRef(0)
  const maxReconnectAttempts = options.maxReconnectAttempts || 5
  const reconnectInterval = options.reconnectInterval || 3000
  const shouldReconnect = options.shouldReconnect !== false

  const connect = useCallback(() => {
    try {
      if (!url) return

      const ws = new WebSocket(url)
      
      ws.onopen = () => {
        console.log('WebSocket connected')
        setReadyState(1)
        setConnectionStatus('Connected')
        reconnectAttemptsRef.current = 0
        
        if (options.onOpen) {
          options.onOpen()
        }
      }

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          setLastMessage(data)
          
          if (options.onMessage) {
            options.onMessage(data)
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error)
          setLastMessage({ error: 'Failed to parse message', raw: event.data })
        }
      }

      ws.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason)
        setReadyState(3)
        setConnectionStatus('Disconnected')
        setSocket(null)
        
        if (options.onClose) {
          options.onClose(event)
        }

        // Attempt to reconnect if enabled and not a normal closure
        if (shouldReconnect && event.code !== 1000 && reconnectAttemptsRef.current < maxReconnectAttempts) {
          reconnectAttemptsRef.current += 1
          setConnectionStatus(`Reconnecting... (${reconnectAttemptsRef.current}/${maxReconnectAttempts})`)
          
          reconnectTimeoutRef.current = setTimeout(() => {
            connect()
          }, reconnectInterval)
        } else if (reconnectAttemptsRef.current >= maxReconnectAttempts) {
          setConnectionStatus('Connection failed')
        }
      }

      ws.onerror = (error) => {
        console.error('WebSocket error:', error)
        setConnectionStatus('Error')
        
        if (options.onError) {
          options.onError(error)
        }
      }

      setSocket(ws)
      setReadyState(0)
      setConnectionStatus('Connecting')
      
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error)
      setConnectionStatus('Connection failed')
    }
  }, [url, options, shouldReconnect, maxReconnectAttempts, reconnectInterval])

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }
    
    if (socket && socket.readyState === WebSocket.OPEN) {
      socket.close(1000, 'Manual disconnect')
    }
    
    setSocket(null)
    setReadyState(3)
    setConnectionStatus('Disconnected')
  }, [socket])

  const sendMessage = useCallback((message) => {
    if (socket && socket.readyState === WebSocket.OPEN) {
      try {
        const messageString = typeof message === 'string' ? message : JSON.stringify(message)
        socket.send(messageString)
        return true
      } catch (error) {
        console.error('Failed to send WebSocket message:', error)
        return false
      }
    } else {
      console.warn('WebSocket is not connected. Message not sent:', message)
      return false
    }
  }, [socket])

  const sendJsonMessage = useCallback((message) => {
    return sendMessage(JSON.stringify(message))
  }, [sendMessage])

  // Connect on mount
  useEffect(() => {
    if (url) {
      connect()
    }

    return () => {
      disconnect()
    }
  }, [url]) // Only reconnect when URL changes

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current)
      }
      
      if (socket) {
        socket.close(1000, 'Component unmounting')
      }
    }
  }, [])

  return {
    socket,
    lastMessage,
    readyState,
    connectionStatus,
    sendMessage,
    sendJsonMessage,
    connect,
    disconnect,
    // WebSocket ready states for convenience
    CONNECTING: 0,
    OPEN: 1,
    CLOSING: 2,
    CLOSED: 3
  }
}

// Hook for messaging-specific WebSocket functionality
export function useMessagingWebSocket(conversationId, options = {}) {
  const [messages, setMessages] = useState([])
  const [typingUsers, setTypingUsers] = useState([])
  
  const wsUrl = conversationId 
    ? `${process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3001'}/messaging/${conversationId}`
    : null

  const handleMessage = useCallback((data) => {
    switch (data.type) {
      case 'message':
        setMessages(prev => [...prev, data.message])
        break
        
      case 'typing_start':
        setTypingUsers(prev => {
          if (!prev.includes(data.userId)) {
            return [...prev, data.userId]
          }
          return prev
        })
        break
        
      case 'typing_stop':
        setTypingUsers(prev => prev.filter(id => id !== data.userId))
        break
        
      case 'message_read':
        setMessages(prev => prev.map(msg => 
          msg.id === data.messageId 
            ? { ...msg, read_at: data.readAt }
            : msg
        ))
        break
        
      default:
        console.log('Unknown message type:', data.type)
    }
    
    if (options.onMessage) {
      options.onMessage(data)
    }
  }, [options])

  const {
    socket,
    lastMessage,
    readyState,
    connectionStatus,
    sendJsonMessage,
    connect,
    disconnect
  } = useWebSocket(wsUrl, {
    ...options,
    onMessage: handleMessage
  })

  const sendMessage = useCallback((messageText, attachments = []) => {
    return sendJsonMessage({
      type: 'send_message',
      message: messageText,
      attachments: attachments
    })
  }, [sendJsonMessage])

  const startTyping = useCallback(() => {
    return sendJsonMessage({
      type: 'typing_start'
    })
  }, [sendJsonMessage])

  const stopTyping = useCallback(() => {
    return sendJsonMessage({
      type: 'typing_stop'
    })
  }, [sendJsonMessage])

  const markAsRead = useCallback((messageId) => {
    return sendJsonMessage({
      type: 'mark_read',
      messageId: messageId
    })
  }, [sendJsonMessage])

  return {
    socket,
    lastMessage,
    readyState,
    connectionStatus,
    messages,
    typingUsers,
    sendMessage,
    startTyping,
    stopTyping,
    markAsRead,
    connect,
    disconnect,
    // WebSocket ready states for convenience
    CONNECTING: 0,
    OPEN: 1,
    CLOSING: 2,
    CLOSED: 3
  }
}

export default useWebSocket
