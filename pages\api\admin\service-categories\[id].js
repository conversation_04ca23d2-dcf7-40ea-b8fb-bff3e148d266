
import { supabaseAdmin } from '../../../../lib/supabase';

// Helper function to serialize category data consistently
const serializeCategory = (category) => {
  if (!category) return null;
  return {
    id: String(category.id || ''),
    name: String(category.name || ''),
    description: category.description ? String(category.description) : null,
    parent_id: category.parent_id ? String(category.parent_id) : null,
    created_at: category.created_at ? String(new Date(category.created_at).toISOString()) : null,
    updated_at: category.updated_at ? String(new Date(category.updated_at).toISOString()) : null,
  };
};

// Basic UUID validation
const isValidUUID = (uuid) => {
  const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
  return uuidRegex.test(uuid);
};

export default async function handler(req, res) {
  const { id } = req.query;

  // Validate UUID format
  if (!isValidUUID(id)) {
    return res.status(400).json({ success: false, error: 'Invalid category ID format.' });
  }

  if (req.method === 'GET') {
    try {
      console.log(`🔍 Service Categories API [/${id}] - GET: Fetching category`);

      const { data: category, error } = await supabaseAdmin
        .from('service_categories')
        .select('id, name, description, parent_id, created_at, updated_at')
        .eq('id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          console.log(`⚠️ Category with ID ${id} not found.`);
          return res.status(404).json({ success: false, error: 'Category not found.' });
        }
        console.error(`❌ Service Categories API [/${id}] - GET: Database error:`, error);
        return res.status(500).json({ success: false, error: 'Database error occurred.', details: error.message });
      }

      if (!category) {
        console.log(`⚠️ Service Categories API [/${id}] - GET: No category found.`);
        return res.status(404).json({ success: false, error: 'Category not found.' });
      }

      console.log(`✅ Service Categories API [/${id}] - GET: Found category "${category.name}"`);
      res.status(200).json({ success: true, category: serializeCategory(category) });

    } catch (error) {
      console.error(`💥 Service Categories API [/${id}] - GET: Unexpected error:`, error);
      res.status(500).json({ success: false, error: 'Failed to fetch service category.', details: error.message });
    }
  } else {
    console.log(`🚫 Service Categories API [/${id}] - Method ${req.method} not allowed.`);
    res.setHeader('Allow', ['GET']);
    res.status(405).json({ success: false, error: `Method ${req.method} Not Allowed` });
  }
}
