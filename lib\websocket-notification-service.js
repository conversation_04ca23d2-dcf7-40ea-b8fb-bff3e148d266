/**
 * WebSocket Notification Service
 * Ocean Soul Sparkles - Real-time Dashboard Notifications
 */

import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

class WebSocketNotificationService {
  constructor() {
    this.io = null
    this.activeConnections = new Map()
  }

  /**
   * Initialize the service with Socket.IO instance
   */
  initialize(io) {
    this.io = io
    console.log('[WebSocketNotificationService] Service initialized')
  }

  /**
   * Register a new connection
   */
  registerConnection(socketId, connectionInfo) {
    this.activeConnections.set(socketId, connectionInfo)
    console.log(`[WebSocketNotificationService] Connection registered: ${socketId}`)
  }

  /**
   * Unregister a connection
   */
  unregisterConnection(socketId) {
    this.activeConnections.delete(socketId)
    console.log(`[WebSocketNotificationService] Connection unregistered: ${socketId}`)
  }

  /**
   * Send notification to specific user
   */
  notifyUser(userId, eventType, data) {
    if (!this.io) return

    const roomName = `user_${userId}`
    console.log(`[WebSocketNotificationService] Sending ${eventType} to user ${userId}`)
    
    this.io.to(roomName).emit(eventType, {
      type: eventType,
      payload: data,
      timestamp: new Date().toISOString()
    })
  }

  /**
   * Send notification to all users with specific role
   */
  notifyRole(role, eventType, data) {
    if (!this.io) return

    const roomName = `role_${role}`
    console.log(`[WebSocketNotificationService] Sending ${eventType} to role ${role}`)
    
    this.io.to(roomName).emit(eventType, {
      type: eventType,
      payload: data,
      timestamp: new Date().toISOString()
    })
  }

  /**
   * Handle new booking notification
   */
  async handleNewBooking(bookingData) {
    try {
      // Get booking details with related data
      const { data: booking, error } = await supabase
        .from('bookings')
        .select(`
          *,
          customers (name, email),
          services (name, duration, price),
          artist_profiles (user_id)
        `)
        .eq('id', bookingData.id)
        .single()

      if (error || !booking) {
        console.error('[WebSocketNotificationService] Error fetching booking details:', error)
        return
      }

      // Notify assigned artist
      if (booking.assigned_artist_id && booking.artist_profiles?.user_id) {
        this.notifyUser(booking.artist_profiles.user_id, 'booking_notification', {
          type: 'new_booking',
          booking: {
            id: booking.id,
            start_time: booking.start_time,
            end_time: booking.end_time,
            service_name: booking.services?.name,
            customer_name: booking.customers?.name,
            location: booking.location
          }
        })

        // Also trigger dashboard refresh for the artist
        this.refreshArtistDashboard(booking.artist_profiles.user_id)
      }

      // Notify all admins
      this.notifyRole('admin', 'booking_notification', {
        type: 'new_booking',
        booking: {
          id: booking.id,
          start_time: booking.start_time,
          service_name: booking.services?.name,
          customer_name: booking.customers?.name,
          artist_name: booking.artist_profiles?.artist_name
        }
      })

    } catch (error) {
      console.error('[WebSocketNotificationService] Error handling new booking:', error)
    }
  }

  /**
   * Handle booking update notification
   */
  async handleBookingUpdate(bookingData, changeType = 'updated') {
    try {
      // Get booking details
      const { data: booking, error } = await supabase
        .from('bookings')
        .select(`
          *,
          customers (name, email),
          services (name, duration, price),
          artist_profiles (user_id, artist_name)
        `)
        .eq('id', bookingData.id)
        .single()

      if (error || !booking) {
        console.error('[WebSocketNotificationService] Error fetching booking details:', error)
        return
      }

      // Notify assigned artist
      if (booking.assigned_artist_id && booking.artist_profiles?.user_id) {
        this.notifyUser(booking.artist_profiles.user_id, 'booking_notification', {
          type: `booking_${changeType}`,
          booking: {
            id: booking.id,
            start_time: booking.start_time,
            end_time: booking.end_time,
            service_name: booking.services?.name,
            customer_name: booking.customers?.name,
            status: booking.status
          }
        })

        // Refresh dashboard data
        this.refreshArtistDashboard(booking.artist_profiles.user_id)
      }

    } catch (error) {
      console.error('[WebSocketNotificationService] Error handling booking update:', error)
    }
  }

  /**
   * Handle availability update notification
   */
  async handleAvailabilityUpdate(artistProfileData) {
    try {
      // Get artist profile details
      const { data: profile, error } = await supabase
        .from('artist_profiles')
        .select('user_id, artist_name, is_available_today')
        .eq('id', artistProfileData.id)
        .single()

      if (error || !profile) {
        console.error('[WebSocketNotificationService] Error fetching artist profile:', error)
        return
      }

      // Notify the artist
      this.notifyUser(profile.user_id, 'availability_update', {
        is_available_today: profile.is_available_today,
        artist_name: profile.artist_name
      })

      // Notify admins
      this.notifyRole('admin', 'availability_update', {
        artist_id: artistProfileData.id,
        artist_name: profile.artist_name,
        is_available_today: profile.is_available_today
      })

    } catch (error) {
      console.error('[WebSocketNotificationService] Error handling availability update:', error)
    }
  }

  /**
   * Refresh artist dashboard data
   */
  async refreshArtistDashboard(userId) {
    try {
      // Fetch fresh dashboard data
      const dashboardData = await this.fetchDashboardData(userId)
      
      // Send dashboard update
      this.notifyUser(userId, 'dashboard_update', {
        type: 'data_refresh',
        data: dashboardData
      })

    } catch (error) {
      console.error('[WebSocketNotificationService] Error refreshing dashboard:', error)
    }
  }

  /**
   * Fetch dashboard data for user
   */
  async fetchDashboardData(userId) {
    try {
      // Get artist profile
      const { data: artistProfile } = await supabase
        .from('artist_profiles')
        .select('*')
        .eq('user_id', userId)
        .single()

      if (!artistProfile) {
        throw new Error('Artist profile not found')
      }

      // Get recent bookings
      const { data: bookings } = await supabase
        .from('bookings')
        .select(`
          *,
          customers (name, email),
          services (name, duration, price)
        `)
        .or(`assigned_artist_id.eq.${artistProfile.id},preferred_artist_id.eq.${artistProfile.id}`)
        .gte('start_time', new Date().toISOString())
        .order('start_time', { ascending: true })
        .limit(10)

      // Get today's bookings count
      const today = new Date()
      const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate())
      const todayEnd = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000)

      const { count: todaysBookings } = await supabase
        .from('bookings')
        .select('*', { count: 'exact', head: true })
        .or(`assigned_artist_id.eq.${artistProfile.id},preferred_artist_id.eq.${artistProfile.id}`)
        .gte('start_time', todayStart.toISOString())
        .lt('start_time', todayEnd.toISOString())

      return {
        profile: artistProfile,
        upcomingBookings: bookings || [],
        todaysBookings: todaysBookings || 0,
        lastUpdated: new Date().toISOString()
      }
      
    } catch (error) {
      console.error('[WebSocketNotificationService] Error fetching dashboard data:', error)
      throw error
    }
  }

  /**
   * Send booking reminder notifications
   */
  async sendBookingReminders() {
    try {
      // Get bookings starting in 10 minutes
      const reminderTime = new Date()
      reminderTime.setMinutes(reminderTime.getMinutes() + 10)
      
      const { data: upcomingBookings } = await supabase
        .from('bookings')
        .select(`
          *,
          customers (name),
          services (name),
          artist_profiles (user_id, artist_name)
        `)
        .gte('start_time', reminderTime.toISOString())
        .lt('start_time', new Date(reminderTime.getTime() + 60000).toISOString()) // 1 minute window
        .eq('status', 'confirmed')

      if (upcomingBookings && upcomingBookings.length > 0) {
        for (const booking of upcomingBookings) {
          if (booking.artist_profiles?.user_id) {
            this.notifyUser(booking.artist_profiles.user_id, 'booking_notification', {
              type: 'booking_reminder',
              booking: {
                id: booking.id,
                start_time: booking.start_time,
                service_name: booking.services?.name,
                customer_name: booking.customers?.name,
                location: booking.location
              }
            })
          }
        }
      }

    } catch (error) {
      console.error('[WebSocketNotificationService] Error sending booking reminders:', error)
    }
  }

  /**
   * Get connection statistics
   */
  getConnectionStats() {
    const stats = {
      totalConnections: this.activeConnections.size,
      connectionsByRole: {},
      connectionsByUser: {}
    }

    this.activeConnections.forEach((connection) => {
      // Count by role
      if (!stats.connectionsByRole[connection.role]) {
        stats.connectionsByRole[connection.role] = 0
      }
      stats.connectionsByRole[connection.role]++

      // Count by user
      if (!stats.connectionsByUser[connection.userId]) {
        stats.connectionsByUser[connection.userId] = 0
      }
      stats.connectionsByUser[connection.userId]++
    })

    return stats
  }
}

// Create singleton instance
const websocketNotificationService = new WebSocketNotificationService()

export default websocketNotificationService
