import { useState, useEffect } from 'react';
import { authenticatedFetch } from '@/lib/auth-utils';
import { toast } from 'react-toastify';
import styles from '@/styles/admin/EmergencyNotificationPanel.module.css';

/**
 * Emergency Notification Panel Component
 * Provides emergency broadcast capabilities for critical alerts
 */
export default function EmergencyNotificationPanel({ userRole }) {
  const [emergencyData, setEmergencyData] = useState({
    title: '',
    message: '',
    priority: 'high',
    target_audience: 'all',
    channels: ['push', 'email'],
    expires_at: '',
    requires_acknowledgment: false
  });

  const [sending, setSending] = useState(false);
  const [recentEmergencies, setRecentEmergencies] = useState([]);
  const [loadingHistory, setLoadingHistory] = useState(true);

  // Emergency templates for quick access
  const emergencyTemplates = [
    {
      id: 'weather_alert',
      title: 'Weather Alert',
      message: 'Due to severe weather conditions, all outdoor events scheduled for today have been cancelled. Please check your bookings for updates.',
      priority: 'critical'
    },
    {
      id: 'system_maintenance',
      title: 'Emergency System Maintenance',
      message: 'We are performing emergency system maintenance. The booking system may be temporarily unavailable. We apologize for any inconvenience.',
      priority: 'high'
    },
    {
      id: 'event_cancellation',
      title: 'Event Cancellation',
      message: 'Due to unforeseen circumstances, the event has been cancelled. All affected bookings will be automatically refunded.',
      priority: 'critical'
    },
    {
      id: 'security_alert',
      title: 'Security Alert',
      message: 'We have detected unusual activity on your account. Please log in and verify your account security settings immediately.',
      priority: 'critical'
    },
    {
      id: 'location_change',
      title: 'Location Change',
      message: 'The event location has been changed due to unforeseen circumstances. Please check your booking details for the updated location.',
      priority: 'high'
    }
  ];

  useEffect(() => {
    loadEmergencyHistory();
  }, []);

  const loadEmergencyHistory = async () => {
    try {
      setLoadingHistory(true);
      
      const response = await authenticatedFetch('/api/notifications/emergency/history');
      
      if (response.success) {
        setRecentEmergencies(response.data || []);
      }
    } catch (error) {
      console.error('Error loading emergency history:', error);
      toast.error('Failed to load emergency notification history');
    } finally {
      setLoadingHistory(false);
    }
  };

  const applyTemplate = (template) => {
    setEmergencyData(prev => ({
      ...prev,
      title: template.title,
      message: template.message,
      priority: template.priority
    }));
  };

  const sendEmergencyNotification = async () => {
    if (!emergencyData.title.trim() || !emergencyData.message.trim()) {
      toast.error('Please provide both title and message for the emergency notification');
      return;
    }

    // Double confirmation for emergency notifications
    const confirmed = window.confirm(
      `Are you sure you want to send this emergency notification to ${emergencyData.target_audience}?\n\n` +
      `Title: ${emergencyData.title}\n` +
      `Message: ${emergencyData.message}\n\n` +
      `This action cannot be undone and will notify all selected users immediately.`
    );

    if (!confirmed) return;

    try {
      setSending(true);

      const response = await authenticatedFetch('/api/notifications/emergency', {
        method: 'POST',
        body: JSON.stringify({
          ...emergencyData,
          sent_by: userRole,
          sent_at: new Date().toISOString()
        })
      });

      if (response.success) {
        toast.success(`Emergency notification sent successfully to ${response.recipients_count} recipients`);
        
        // Reset form
        setEmergencyData({
          title: '',
          message: '',
          priority: 'high',
          target_audience: 'all',
          channels: ['push', 'email'],
          expires_at: '',
          requires_acknowledgment: false
        });

        // Reload history
        loadEmergencyHistory();
      } else {
        throw new Error(response.error || 'Failed to send emergency notification');
      }
    } catch (error) {
      console.error('Error sending emergency notification:', error);
      toast.error('Failed to send emergency notification: ' + error.message);
    } finally {
      setSending(false);
    }
  };

  const updateEmergencyData = (key, value) => {
    setEmergencyData(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const toggleChannel = (channel) => {
    setEmergencyData(prev => ({
      ...prev,
      channels: prev.channels.includes(channel)
        ? prev.channels.filter(c => c !== channel)
        : [...prev.channels, channel]
    }));
  };

  // Only allow admin and dev roles to access emergency notifications
  if (!['admin', 'dev'].includes(userRole)) {
    return (
      <div className={styles.accessDenied}>
        <h3>Access Denied</h3>
        <p>Emergency notification access is restricted to administrators only.</p>
      </div>
    );
  }

  return (
    <div className={styles.emergencyPanel}>
      <div className={styles.header}>
        <h2>🚨 Emergency Notification System</h2>
        <p className={styles.warning}>
          Use this system only for critical alerts that require immediate attention from all users.
        </p>
      </div>

      <div className={styles.content}>
        <div className={styles.mainPanel}>
          <div className={styles.section}>
            <h3>Quick Templates</h3>
            <div className={styles.templates}>
              {emergencyTemplates.map(template => (
                <button
                  key={template.id}
                  className={`${styles.templateButton} ${styles[template.priority]}`}
                  onClick={() => applyTemplate(template)}
                >
                  {template.title}
                </button>
              ))}
            </div>
          </div>

          <div className={styles.section}>
            <h3>Emergency Notification Details</h3>
            
            <div className={styles.formGroup}>
              <label>Title *</label>
              <input
                type="text"
                value={emergencyData.title}
                onChange={(e) => updateEmergencyData('title', e.target.value)}
                placeholder="Emergency notification title"
                maxLength={100}
              />
            </div>

            <div className={styles.formGroup}>
              <label>Message *</label>
              <textarea
                value={emergencyData.message}
                onChange={(e) => updateEmergencyData('message', e.target.value)}
                placeholder="Detailed emergency message"
                rows={4}
                maxLength={500}
              />
              <div className={styles.charCount}>
                {emergencyData.message.length}/500 characters
              </div>
            </div>

            <div className={styles.formRow}>
              <div className={styles.formGroup}>
                <label>Priority Level</label>
                <select
                  value={emergencyData.priority}
                  onChange={(e) => updateEmergencyData('priority', e.target.value)}
                >
                  <option value="high">High Priority</option>
                  <option value="critical">Critical Alert</option>
                </select>
              </div>

              <div className={styles.formGroup}>
                <label>Target Audience</label>
                <select
                  value={emergencyData.target_audience}
                  onChange={(e) => updateEmergencyData('target_audience', e.target.value)}
                >
                  <option value="all">All Users</option>
                  <option value="artists">Artists Only</option>
                  <option value="customers">Customers Only</option>
                  <option value="admins">Administrators Only</option>
                </select>
              </div>
            </div>

            <div className={styles.formGroup}>
              <label>Notification Channels</label>
              <div className={styles.channelOptions}>
                {['push', 'email', 'sms'].map(channel => (
                  <label key={channel} className={styles.checkboxLabel}>
                    <input
                      type="checkbox"
                      checked={emergencyData.channels.includes(channel)}
                      onChange={() => toggleChannel(channel)}
                    />
                    {channel.toUpperCase()}
                  </label>
                ))}
              </div>
            </div>

            <div className={styles.formRow}>
              <div className={styles.formGroup}>
                <label>Expires At (Optional)</label>
                <input
                  type="datetime-local"
                  value={emergencyData.expires_at}
                  onChange={(e) => updateEmergencyData('expires_at', e.target.value)}
                />
              </div>

              <div className={styles.formGroup}>
                <label className={styles.checkboxLabel}>
                  <input
                    type="checkbox"
                    checked={emergencyData.requires_acknowledgment}
                    onChange={(e) => updateEmergencyData('requires_acknowledgment', e.target.checked)}
                  />
                  Require User Acknowledgment
                </label>
              </div>
            </div>

            <div className={styles.actions}>
              <button
                className={styles.sendButton}
                onClick={sendEmergencyNotification}
                disabled={sending || !emergencyData.title.trim() || !emergencyData.message.trim()}
              >
                {sending ? 'Sending Emergency Alert...' : '🚨 Send Emergency Notification'}
              </button>
            </div>
          </div>
        </div>

        <div className={styles.sidebar}>
          <div className={styles.section}>
            <h3>Recent Emergency Notifications</h3>
            
            {loadingHistory ? (
              <div className={styles.loading}>Loading history...</div>
            ) : recentEmergencies.length === 0 ? (
              <div className={styles.noHistory}>No recent emergency notifications</div>
            ) : (
              <div className={styles.historyList}>
                {recentEmergencies.slice(0, 5).map(emergency => (
                  <div key={emergency.id} className={`${styles.historyItem} ${styles[emergency.priority]}`}>
                    <div className={styles.historyTitle}>{emergency.title}</div>
                    <div className={styles.historyMeta}>
                      {new Date(emergency.sent_at).toLocaleDateString()} • {emergency.target_audience}
                    </div>
                    <div className={styles.historyMessage}>{emergency.message}</div>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className={styles.section}>
            <h3>Emergency Guidelines</h3>
            <div className={styles.guidelines}>
              <ul>
                <li>Only use for genuine emergencies</li>
                <li>Keep messages clear and actionable</li>
                <li>Include specific instructions when possible</li>
                <li>Use appropriate priority levels</li>
                <li>Consider time zones for your audience</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
