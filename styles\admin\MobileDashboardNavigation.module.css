/* Mobile Dashboard Navigation Styles */

.container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: white;
  border-top: 1px solid #e5e7eb;
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  padding-bottom: env(safe-area-inset-bottom);
}

.visible {
  transform: translateY(0);
}

.hidden {
  transform: translateY(100%);
}

.navigation {
  padding: 8px 16px 12px;
}

.sectionTabs {
  display: flex;
  justify-content: space-around;
  align-items: center;
  gap: 4px;
  margin-bottom: 8px;
}

.sectionTab {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border: none;
  background: transparent;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 60px;
  position: relative;
  -webkit-tap-highlight-color: transparent;
}

.sectionTab:active {
  transform: scale(0.95);
  background: #f3f4f6;
}

.sectionTab.active {
  background: #3b82f6;
  color: white;
}

.sectionTab.active .sectionIcon {
  transform: scale(1.1);
}

.sectionIcon {
  font-size: 20px;
  transition: transform 0.2s ease;
}

.sectionLabel {
  font-size: 10px;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
}

.sectionBadge {
  position: absolute;
  top: 4px;
  right: 8px;
  background: #ef4444;
  color: white;
  font-size: 8px;
  font-weight: 600;
  padding: 2px 4px;
  border-radius: 8px;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.swipeIndicator {
  display: flex;
  justify-content: center;
  margin-bottom: 4px;
}

.swipeHint {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 10px;
  color: #6b7280;
  opacity: 0.7;
}

.swipeIcon {
  font-size: 12px;
}

.swipeText {
  font-weight: 500;
}

.progressContainer {
  display: flex;
  justify-content: center;
  padding: 0 16px 4px;
}

.progressTrack {
  display: flex;
  gap: 6px;
}

.progressDot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #d1d5db;
  transition: all 0.2s ease;
}

.activeDot {
  background: #3b82f6;
  transform: scale(1.2);
}

/* Section Styles */
.section {
  min-height: calc(100vh - 200px);
  padding: 16px;
  opacity: 0;
  transform: translateX(20px);
  transition: all 0.3s ease;
  pointer-events: none;
}

.activeSection {
  opacity: 1;
  transform: translateX(0);
  pointer-events: auto;
}

/* Floating Action Button Styles */
.fabContainer {
  position: fixed;
  bottom: 100px;
  right: 20px;
  z-index: 200;
}

.fabBackdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: -1;
}

.fabActions {
  position: absolute;
  bottom: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.fabAction {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  transition: all 0.2s ease;
  animation: fabSlideIn 0.3s ease;
  white-space: nowrap;
  -webkit-tap-highlight-color: transparent;
}

.fabAction:active {
  transform: scale(0.95);
}

.fabAction.primary {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.fabAction.secondary {
  background: #f3f4f6;
  color: #374151;
}

.fabAction.success {
  background: #10b981;
  color: white;
  border-color: #10b981;
}

.fabAction.warning {
  background: #f59e0b;
  color: white;
  border-color: #f59e0b;
}

.fabActionIcon {
  font-size: 16px;
}

.fabActionLabel {
  font-size: 14px;
  font-weight: 500;
}

.fab {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: #3b82f6;
  color: white;
  border: none;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-tap-highlight-color: transparent;
}

.fab:active {
  transform: scale(0.9);
}

.fabExpanded {
  background: #ef4444;
  transform: rotate(45deg);
}

.fabIcon {
  font-size: 20px;
  font-weight: bold;
  transition: transform 0.3s ease;
}

/* Touch Card Styles */
.touchCard {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
  user-select: none;
}

.touchCard:active,
.pressed {
  transform: scale(0.98);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  background: #f9fafb;
}

.touchCardTitle {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.touchCardContent {
  color: #4b5563;
}

/* Animations */
@keyframes fabSlideIn {
  from {
    opacity: 0;
    transform: translateX(20px) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .sectionTab {
    min-width: 50px;
    padding: 6px 8px;
  }
  
  .sectionIcon {
    font-size: 18px;
  }
  
  .sectionLabel {
    font-size: 9px;
  }
  
  .fab {
    width: 48px;
    height: 48px;
  }
  
  .fabIcon {
    font-size: 18px;
  }
  
  .fabContainer {
    bottom: 90px;
    right: 16px;
  }
}

/* Landscape orientation adjustments */
@media (orientation: landscape) and (max-height: 500px) {
  .container {
    padding: 4px 16px 8px;
  }
  
  .navigation {
    padding: 4px 16px 8px;
  }
  
  .sectionTab {
    padding: 4px 8px;
  }
  
  .swipeIndicator {
    display: none;
  }
  
  .progressContainer {
    padding: 0 16px 2px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .container {
    background: #1f2937;
    border-top-color: #374151;
  }
  
  .sectionTab {
    color: #d1d5db;
  }
  
  .sectionTab:active {
    background: #374151;
  }
  
  .touchCard {
    background: #1f2937;
    border-color: #374151;
    color: #d1d5db;
  }
  
  .touchCard:active,
  .pressed {
    background: #374151;
  }
  
  .touchCardTitle {
    color: #f9fafb;
  }
  
  .fabAction {
    background: #1f2937;
    border-color: #374151;
    color: #d1d5db;
  }
}
