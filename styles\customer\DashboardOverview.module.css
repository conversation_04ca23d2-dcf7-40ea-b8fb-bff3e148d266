/* Dashboard Overview Styles - Phase 8: Advanced Customer Experience */

.dashboardOverview {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Stats Grid */
.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.statCard {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.statCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.statIcon {
  font-size: 2.5rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  color: white;
}

.statContent {
  flex: 1;
}

.statValue {
  font-size: 1.8rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.25rem;
}

.statLabel {
  font-size: 0.9rem;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Next Booking Card */
.nextBookingCard {
  background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
  color: white;
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.cardTitle {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.viewDetailsLink {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.viewDetailsLink:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

.bookingInfo {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.bookingService,
.bookingDateTime,
.bookingArtist {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.1rem;
}

.serviceIcon,
.dateIcon,
.artistIcon {
  font-size: 1.2rem;
  opacity: 0.9;
}

.serviceName,
.dateTime,
.artistName {
  font-weight: 500;
}

.bookingActions {
  display: flex;
  gap: 1rem;
}

.rescheduleButton,
.cancelButton {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  text-decoration: none;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.rescheduleButton {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.rescheduleButton:hover {
  background: rgba(255, 255, 255, 0.3);
}

.cancelButton {
  background: transparent;
  color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.cancelButton:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

/* Loyalty Card */
.loyaltyCard {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.loyaltyContent {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.tierBadge {
  display: inline-block;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  align-self: flex-start;
}

.pointsInfo {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.currentPoints {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
}

.lifetimePoints {
  font-size: 0.9rem;
  color: #666;
}

.progressSection {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.progressLabel {
  font-size: 0.9rem;
  font-weight: 500;
  color: #666;
}

.progressBar {
  width: 100%;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, #4ECDC4 0%, #44A08D 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progressText {
  font-size: 0.8rem;
  color: #666;
  text-align: center;
}

/* Recent Activity Card */
.recentActivityCard {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.activityList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activityItem {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.activityIcon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  color: white;
  flex-shrink: 0;
}

.activityContent {
  flex: 1;
}

.activityService {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
}

.activityDate {
  font-size: 0.9rem;
  color: #666;
}

.activityRating {
  font-size: 1.2rem;
}

/* Quick Actions Summary */
.quickActionsSummary {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.actionsList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.actionItem {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  text-decoration: none;
  color: #333;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
}

.actionItem:hover {
  background: #f8f9fa;
  transform: translateX(4px);
}

.actionIcon {
  font-size: 1.2rem;
  width: 32px;
  text-align: center;
}

.actionLabel {
  flex: 1;
  font-weight: 500;
}

.actionBadge {
  background: #ff4757;
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

/* Refresh Section */
.refreshSection {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

.refreshButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: #4ECDC4;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.refreshButton:hover {
  background: #44A08D;
  transform: translateY(-1px);
}

.refreshIcon {
  font-size: 1rem;
}

.refreshLabel {
  font-size: 0.9rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .dashboardOverview {
    padding: 0.5rem;
    gap: 1.5rem;
  }

  .statsGrid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }

  .statCard {
    padding: 1rem;
  }

  .statIcon {
    font-size: 2rem;
    width: 50px;
    height: 50px;
  }

  .statValue {
    font-size: 1.5rem;
  }

  .nextBookingCard,
  .loyaltyCard,
  .recentActivityCard,
  .quickActionsSummary {
    padding: 1.5rem;
  }

  .cardHeader {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .bookingActions {
    flex-direction: column;
  }

  .rescheduleButton,
  .cancelButton {
    text-align: center;
  }
}

@media (max-width: 480px) {
  .statsGrid {
    grid-template-columns: 1fr;
  }

  .nextBookingCard,
  .loyaltyCard,
  .recentActivityCard,
  .quickActionsSummary {
    padding: 1rem;
  }

  .bookingInfo {
    gap: 0.75rem;
  }

  .bookingService,
  .bookingDateTime,
  .bookingArtist {
    font-size: 1rem;
  }
}
