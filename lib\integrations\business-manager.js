/**
 * Business Manager for Ocean Soul Sparkles
 * Universal business integration interface supporting accounting and marketing
 * 
 * Phase 7.4: Business Management Integrations
 */

import QuickBooksClient from './accounting/quickbooks-api'
import MailchimpClient from './marketing/mailchimp-api'
import { AuditLogger } from './security-utils'
import { getIntegrationSettings } from '@/lib/supabase'

/**
 * Supported Business Providers
 */
const BUSINESS_PROVIDERS = {
  quickbooks: {
    name: 'QuickBooks',
    clientClass: QuickBooksClient,
    type: 'accounting',
    features: ['invoicing', 'expenses', 'customers', 'reports', 'financial_sync']
  },
  mailchimp: {
    name: 'Mailchimp',
    clientClass: MailchimpClient,
    type: 'marketing',
    features: ['email_campaigns', 'customer_segmentation', 'automation', 'analytics']
  }
  // Future providers can be added here:
  // xero: { name: 'Xero', clientClass: XeroClient, type: 'accounting' },
  // constant_contact: { name: 'Constant Contact', clientClass: ConstantContactClient, type: 'marketing' }
}

/**
 * Business Manager Class
 * Provides unified interface for business operations across providers
 */
export class BusinessManager {
  constructor(userId) {
    this.userId = userId
    this.clients = new Map()
  }

  /**
   * Get business client for a specific provider
   */
  async getClient(provider) {
    if (!BUSINESS_PROVIDERS[provider]) {
      throw new Error(`Unsupported business provider: ${provider}`)
    }

    // Return cached client if available
    if (this.clients.has(provider)) {
      return this.clients.get(provider)
    }

    // Create new client
    const ClientClass = BUSINESS_PROVIDERS[provider].clientClass
    const client = new ClientClass(this.userId)

    // Initialize client
    const initialized = await client.initialize()
    if (!initialized) {
      throw new Error(`Failed to initialize ${provider} client`)
    }

    // Cache client
    this.clients.set(provider, client)
    return client
  }

  /**
   * Get all connected business providers for user
   */
  async getConnectedProviders() {
    const settings = await getIntegrationSettings(this.userId)
    
    return settings
      .filter(setting => 
        BUSINESS_PROVIDERS[setting.provider] && 
        setting.enabled
      )
      .map(setting => ({
        provider: setting.provider,
        name: BUSINESS_PROVIDERS[setting.provider].name,
        type: BUSINESS_PROVIDERS[setting.provider].type,
        features: BUSINESS_PROVIDERS[setting.provider].features,
        settings: setting.settings
      }))
  }

  /**
   * Sync Ocean Soul Sparkles booking to accounting system
   */
  async syncBookingToAccounting(bookingData) {
    const connectedProviders = await this.getConnectedProviders()
    const accountingProviders = connectedProviders.filter(p => p.type === 'accounting')
    
    const syncResults = []

    for (const providerInfo of accountingProviders) {
      try {
        const client = await this.getClient(providerInfo.provider)
        
        let result
        if (providerInfo.provider === 'quickbooks') {
          result = await client.syncBookingToInvoice(bookingData)
        }
        // Add other accounting providers here
        
        syncResults.push({
          provider: providerInfo.provider,
          success: result?.success || false,
          invoiceId: result?.invoiceId,
          customerId: result?.customerId,
          error: result?.error
        })

        if (result?.success) {
          await AuditLogger.logIntegrationActivity(
            this.userId,
            providerInfo.provider,
            'booking_synced_to_accounting',
            'success',
            { 
              bookingId: bookingData.bookingId,
              invoiceId: result.invoiceId
            }
          )
        }

      } catch (error) {
        console.error(`Failed to sync booking to ${providerInfo.provider}:`, error)
        
        syncResults.push({
          provider: providerInfo.provider,
          success: false,
          error: error.message
        })

        await AuditLogger.logIntegrationActivity(
          this.userId,
          providerInfo.provider,
          'booking_sync_failed',
          'error',
          { 
            bookingId: bookingData.bookingId,
            error: error.message
          }
        )
      }
    }

    return syncResults
  }

  /**
   * Sync customers to marketing platforms
   */
  async syncCustomersToMarketing(customers, options = {}) {
    const connectedProviders = await this.getConnectedProviders()
    const marketingProviders = connectedProviders.filter(p => p.type === 'marketing')
    
    const syncResults = []

    for (const providerInfo of marketingProviders) {
      try {
        const client = await this.getClient(providerInfo.provider)
        const providerSettings = providerInfo.settings || {}
        
        let result
        if (providerInfo.provider === 'mailchimp') {
          const listId = providerSettings.defaultListId || options.listId
          if (!listId) {
            throw new Error('No Mailchimp list ID configured')
          }
          
          result = await client.syncCustomersToMailchimp(
            customers, 
            listId, 
            options.segmentTags || ['Ocean Soul Sparkles Customer']
          )
        }
        // Add other marketing providers here
        
        syncResults.push({
          provider: providerInfo.provider,
          results: result,
          successCount: result?.filter(r => r.success).length || 0,
          totalCount: result?.length || 0
        })

        await AuditLogger.logIntegrationActivity(
          this.userId,
          providerInfo.provider,
          'customers_synced_to_marketing',
          'success',
          { 
            customerCount: customers.length,
            successCount: result?.filter(r => r.success).length || 0
          }
        )

      } catch (error) {
        console.error(`Failed to sync customers to ${providerInfo.provider}:`, error)
        
        syncResults.push({
          provider: providerInfo.provider,
          success: false,
          error: error.message
        })

        await AuditLogger.logIntegrationActivity(
          this.userId,
          providerInfo.provider,
          'customer_sync_failed',
          'error',
          { 
            customerCount: customers.length,
            error: error.message
          }
        )
      }
    }

    return syncResults
  }

  /**
   * Get financial reports from accounting systems
   */
  async getFinancialReports(startDate, endDate) {
    const connectedProviders = await this.getConnectedProviders()
    const accountingProviders = connectedProviders.filter(p => p.type === 'accounting')
    
    const reports = {}

    for (const providerInfo of accountingProviders) {
      try {
        const client = await this.getClient(providerInfo.provider)
        
        if (providerInfo.provider === 'quickbooks') {
          const [profitLoss, salesReport] = await Promise.all([
            client.getProfitLossReport(startDate, endDate),
            client.getSalesReport(startDate, endDate)
          ])
          
          reports[providerInfo.provider] = {
            provider: providerInfo.provider,
            providerName: providerInfo.name,
            profitLoss,
            salesReport
          }
        }
        // Add other accounting providers here
        
      } catch (error) {
        console.error(`Failed to get reports from ${providerInfo.provider}:`, error)
        
        reports[providerInfo.provider] = {
          provider: providerInfo.provider,
          providerName: providerInfo.name,
          error: error.message
        }
      }
    }

    return reports
  }

  /**
   * Get marketing analytics from marketing platforms
   */
  async getMarketingAnalytics() {
    const connectedProviders = await this.getConnectedProviders()
    const marketingProviders = connectedProviders.filter(p => p.type === 'marketing')
    
    const analytics = {}

    for (const providerInfo of marketingProviders) {
      try {
        const client = await this.getClient(providerInfo.provider)
        
        if (providerInfo.provider === 'mailchimp') {
          // Get recent campaigns and their reports
          const campaigns = await client.getCampaigns(10, 0, 'sent')
          const campaignReports = []
          
          for (const campaign of campaigns.campaigns.slice(0, 5)) {
            try {
              const report = await client.getCampaignReports(campaign.id)
              campaignReports.push(report)
            } catch (error) {
              console.error(`Failed to get campaign report for ${campaign.id}:`, error)
            }
          }
          
          analytics[providerInfo.provider] = {
            provider: providerInfo.provider,
            providerName: providerInfo.name,
            campaigns: campaigns.campaigns,
            campaignReports
          }
        }
        // Add other marketing providers here
        
      } catch (error) {
        console.error(`Failed to get analytics from ${providerInfo.provider}:`, error)
        
        analytics[providerInfo.provider] = {
          provider: providerInfo.provider,
          providerName: providerInfo.name,
          error: error.message
        }
      }
    }

    return analytics
  }

  /**
   * Create expense in accounting system
   */
  async createExpense(expenseData) {
    const connectedProviders = await this.getConnectedProviders()
    const accountingProviders = connectedProviders.filter(p => p.type === 'accounting')
    
    const results = []

    for (const providerInfo of accountingProviders) {
      try {
        const client = await this.getClient(providerInfo.provider)
        
        let result
        if (providerInfo.provider === 'quickbooks') {
          result = await client.createExpense(expenseData)
        }
        // Add other accounting providers here
        
        results.push({
          provider: providerInfo.provider,
          success: true,
          expenseId: result?.Id,
          result
        })

      } catch (error) {
        console.error(`Failed to create expense in ${providerInfo.provider}:`, error)
        
        results.push({
          provider: providerInfo.provider,
          success: false,
          error: error.message
        })
      }
    }

    return results
  }

  /**
   * Test connections to all business providers
   */
  async testAllConnections() {
    const connectedProviders = await this.getConnectedProviders()
    const testResults = []

    for (const providerInfo of connectedProviders) {
      try {
        const client = await this.getClient(providerInfo.provider)
        const result = await client.testConnection()
        
        testResults.push({
          provider: providerInfo.provider,
          name: providerInfo.name,
          type: providerInfo.type,
          ...result
        })
      } catch (error) {
        testResults.push({
          provider: providerInfo.provider,
          name: providerInfo.name,
          type: providerInfo.type,
          success: false,
          error: error.message
        })
      }
    }

    return testResults
  }

  /**
   * Get available business providers
   */
  static getAvailableProviders() {
    return Object.keys(BUSINESS_PROVIDERS).map(key => ({
      id: key,
      name: BUSINESS_PROVIDERS[key].name,
      type: BUSINESS_PROVIDERS[key].type,
      features: BUSINESS_PROVIDERS[key].features
    }))
  }
}

export default BusinessManager
