/* Connection Status Indicator Styles */

.container {
  position: relative;
  display: inline-block;
}

.indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid transparent;
}

.indicator:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.icon {
  font-size: 0.75rem;
}

.text {
  color: inherit;
}

.timeAgo {
  font-size: 0.75rem;
  opacity: 0.7;
  margin-left: 0.25rem;
}

/* Status-specific styles */
.connected {
  background-color: #ecfdf5;
  color: #065f46;
  border-color: #10b981;
}

.connecting {
  background-color: #fffbeb;
  color: #92400e;
  border-color: #f59e0b;
}

.disconnected {
  background-color: #f9fafb;
  color: #374151;
  border-color: #6b7280;
}

.error {
  background-color: #fef2f2;
  color: #991b1b;
  border-color: #ef4444;
}

.unknown {
  background-color: #f3f4f6;
  color: #6b7280;
  border-color: #9ca3af;
}

/* Details dropdown */
.details {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  z-index: 50;
  min-width: 250px;
}

.detailsContent {
  padding: 1rem;
}

.statusRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.statusRow:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: 500;
  color: #6b7280;
  font-size: 0.875rem;
}

.value {
  color: #111827;
  font-size: 0.875rem;
}

.actions {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.retryButton {
  width: 100%;
  padding: 0.5rem 1rem;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.retryButton:hover {
  background-color: #2563eb;
}

.retryButton:active {
  background-color: #1d4ed8;
}

/* Status dot component */
.statusDot {
  position: relative;
  display: inline-block;
  width: 12px;
  height: 12px;
}

.dot {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.pulse {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  opacity: 0.6;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.3;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* Banner component */
.banner {
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  border: 1px solid;
}

.bannerContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

.bannerMessage {
  flex: 1;
  font-size: 0.875rem;
  font-weight: 500;
}

.bannerActions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.bannerButton {
  padding: 0.25rem 0.75rem;
  border: 1px solid;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  background: transparent;
}

.bannerDismiss {
  padding: 0.25rem;
  border: none;
  background: transparent;
  font-size: 1.25rem;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s ease;
  line-height: 1;
}

.bannerDismiss:hover {
  opacity: 1;
}

/* Banner types */
.banner.error {
  background-color: #fef2f2;
  color: #991b1b;
  border-color: #fecaca;
}

.banner.error .bannerButton {
  color: #991b1b;
  border-color: #991b1b;
}

.banner.error .bannerButton:hover {
  background-color: #991b1b;
  color: white;
}

.banner.warning {
  background-color: #fffbeb;
  color: #92400e;
  border-color: #fed7aa;
}

.banner.warning .bannerButton {
  color: #92400e;
  border-color: #92400e;
}

.banner.warning .bannerButton:hover {
  background-color: #92400e;
  color: white;
}

.banner.info {
  background-color: #eff6ff;
  color: #1e40af;
  border-color: #bfdbfe;
}

.banner.info .bannerButton {
  color: #1e40af;
  border-color: #1e40af;
}

.banner.info .bannerButton:hover {
  background-color: #1e40af;
  color: white;
}

/* Responsive design */
@media (max-width: 768px) {
  .details {
    right: auto;
    left: 0;
    min-width: 200px;
  }
  
  .indicator {
    padding: 0.375rem 0.5rem;
    font-size: 0.8125rem;
  }
  
  .timeAgo {
    display: none; /* Hide on mobile to save space */
  }
  
  .bannerContent {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
  
  .bannerActions {
    justify-content: flex-end;
  }
}
